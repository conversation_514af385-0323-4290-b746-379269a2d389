# Falcon系列通用处理器架构

## 🎯 架构优化目标

根据您的要求，A1 Pro和T1暂时使用相同的处理逻辑，通过通用处理器处理，只有在出现差异时才在各自的专用处理器中处理。

## 🏗️ 最终架构设计

### 1. 处理器层次结构
```
AbstractIotMessageHandler (顶层抽象)
├── AbstractLaserEngraverHandler (雕刻机基类)
│   ├── FalconSeriesMessageHandler (Falcon系列通用处理器) ✅ 主要使用
│   ├── FalconA1ProMessageHandler (A1 Pro专用) ⏸️ 暂时禁用
│   └── FalconT1MessageHandler (T1专用) ⏸️ 暂时禁用
├── AirPurifierMessageHandler (空气净化器)
└── DefaultMessageHandler (默认处理器)
```

### 2. 处理器状态
```java
// 通用处理器 - 当前激活
@Component
public class FalconSeriesMessageHandler extends AbstractLaserEngraverHandler {
    @Override
    public boolean supports(String deviceModel) {
        return Set.of("Creality Falcon A1 Pro", "Creality Falcon T1").contains(deviceModel);
    }
}

// 专用处理器 - 暂时禁用
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {
    @Override
    public boolean supports(String deviceModel) {
        return false; // 暂时禁用，使用通用处理器
    }
}
```

## 🔧 核心实现

### 1. Falcon系列通用处理器
```java
@Component
public class FalconSeriesMessageHandler extends AbstractLaserEngraverHandler {
    
    // 支持A1 Pro和T1两个型号
    private static final Set<String> SUPPORTED_MODELS = Set.of(
        "Creality Falcon A1 Pro",
        "Creality Falcon T1"
    );

    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODELS.contains(deviceModel);
    }

    // ==================== 通用处理方法 ====================
    
    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列OTA请求: model={}, sn={}", model, sn);
        iotService.getOtaVersion(model, messageMap);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列状态上报: model={}, sn={}", model, sn);
        iotService.updateDeviceWorkStatus(sn, messageMap);
    }
    
    // ... 其他通用方法
    
    // ==================== Falcon系列特有功能 ====================
    
    protected void handleAdvancedFeatureRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列高级功能请求: model={}, sn={}", model, sn);
        
        String featureType = (String) messageMap.get("featureType");
        if ("SMART_MODE".equals(featureType)) {
            handleSmartMode(model, sn, messageMap);
        } else if ("PRECISION_CONTROL".equals(featureType)) {
            handlePrecisionControl(model, sn, messageMap);
        }
    }
    
    // ==================== 工具方法 ====================
    
    protected boolean isA1Pro(String model) {
        return "Creality Falcon A1 Pro".equals(model);
    }

    protected boolean isT1(String model) {
        return "Creality Falcon T1".equals(model);
    }
}
```

### 2. 专用处理器（暂时禁用）
```java
// A1 Pro专用处理器 - 保留差异化功能示例
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {
    
    @Override
    public boolean supports(String deviceModel) {
        return false; // 暂时禁用，使用通用处理器
        // 当需要A1 Pro特有处理时，改为：
        // return "Creality Falcon A1 Pro".equals(deviceModel);
    }
    
    // A1 Pro特有功能示例（暂时不使用）
    private void handleA1ProMaterialRecognition(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro特有材料识别: model={}, sn={}", model, sn);
        // A1 Pro特有的材料识别逻辑
    }
}

// T1专用处理器 - 保留差异化功能示例
@Component
public class FalconT1MessageHandler extends AbstractLaserEngraverHandler {
    
    @Override
    public boolean supports(String deviceModel) {
        return false; // 暂时禁用，使用通用处理器
        // 当需要T1特有处理时，改为：
        // return "Creality Falcon T1".equals(deviceModel);
    }
    
    // T1特有功能示例（暂时不使用）
    private void handleT1TemperatureMonitoring(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1特有温度监控: model={}, sn={}", model, sn);
        // T1特有的温度监控逻辑
    }
}
```

## 📊 消息处理流程

### 1. 当前处理流程
```
MQTT消息 → 提取设备型号 → 查找处理器
├── "Creality Falcon A1 Pro" → FalconSeriesMessageHandler ✅
├── "Creality Falcon T1" → FalconSeriesMessageHandler ✅
├── "Creality Falcon AP" → AirPurifierMessageHandler ✅
└── 其他设备 → DefaultMessageHandler ✅
```

### 2. 处理器选择逻辑
```java
// 按注册顺序查找第一个支持的处理器
messageHandlers.stream()
    .filter(h -> h.supports("Creality Falcon A1 Pro"))
    .findFirst();

// 结果：FalconSeriesMessageHandler（因为专用处理器返回false）
```

### 3. 启动日志
```
初始化IoT消息处理器管理器，共注册5个处理器:
  - FalconSeriesMessageHandler
  - FalconA1ProMessageHandler
  - FalconT1MessageHandler  
  - AirPurifierMessageHandler
  - DefaultMessageHandler
```

## 🚀 差异化处理机制

### 1. 启用专用处理器
当A1 Pro或T1需要特有处理逻辑时：

```java
// 步骤1：启用专用处理器
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {
    @Override
    public boolean supports(String deviceModel) {
        return "Creality Falcon A1 Pro".equals(deviceModel); // 启用
    }
}

// 步骤2：通用处理器排除该型号
@Component
public class FalconSeriesMessageHandler extends AbstractLaserEngraverHandler {
    @Override
    public boolean supports(String deviceModel) {
        // 排除已有专用处理器的型号
        return "Creality Falcon T1".equals(deviceModel); // 只处理T1
    }
}
```

### 2. 处理器优先级
```
由于没有优先级概念，处理器按注册顺序查找：
1. FalconSeriesMessageHandler (支持A1 Pro和T1)
2. FalconA1ProMessageHandler (暂时禁用)
3. FalconT1MessageHandler (暂时禁用)

当启用专用处理器时，需要调整supports()方法避免冲突
```

### 3. 渐进式迁移
```java
// 场景1：A1 Pro需要特有的材料识别功能
// 1. 启用FalconA1ProMessageHandler.supports()
// 2. 实现handleA1ProMaterialRecognition()
// 3. 调整FalconSeriesMessageHandler.supports()排除A1 Pro

// 场景2：T1需要特有的温度控制功能  
// 1. 启用FalconT1MessageHandler.supports()
// 2. 实现handleT1TemperatureMonitoring()
// 3. 调整FalconSeriesMessageHandler.supports()排除T1
```

## 📁 文件结构

```
falcon-iot/src/main/java/com/creality/lcd/falcon/service/handler/
├── IotMessageHandler.java                           # 核心接口
├── AbstractIotMessageHandler.java                   # 抽象基类
├── IotMessageHandlerManager.java                    # 管理器
├── DefaultMessageHandler.java                       # 默认处理器
├── AirPurifierMessageHandler.java                   # 空气净化器处理器
├── laser/                                           # 雕刻机处理器目录
│   ├── AbstractLaserEngraverHandler.java           # 雕刻机基类
│   ├── FalconSeriesMessageHandler.java             # ✅ Falcon系列通用处理器
│   ├── FalconA1ProMessageHandler.java              # ⏸️ A1 Pro专用（暂时禁用）
│   └── FalconT1MessageHandler.java                 # ⏸️ T1专用（暂时禁用）
└── airpurifier/                                     # 空气净化器专用目录
    └── FalconAPService.java                         # AP专用服务接口
```

## ✅ 架构优势

### 1. 代码复用最大化
- ✅ **通用逻辑集中**: A1 Pro和T1的相同逻辑在FalconSeriesMessageHandler中
- ✅ **避免重复代码**: 不需要在两个处理器中重复相同的方法
- ✅ **维护简单**: 修改通用逻辑只需要改一个地方

### 2. 灵活的差异化支持
- ✅ **按需启用**: 只有在需要差异化处理时才启用专用处理器
- ✅ **渐进式迁移**: 可以逐步将特有功能迁移到专用处理器
- ✅ **向后兼容**: 不影响现有的通用处理逻辑

### 3. 清晰的架构层次
- ✅ **职责明确**: 通用处理器处理共同逻辑，专用处理器处理差异
- ✅ **扩展性好**: 新增型号可以选择使用通用或专用处理器
- ✅ **测试友好**: 可以独立测试通用和专用逻辑

## 🔍 使用示例

### 当前状态 - 通用处理
```
[MQTT] 消息来自: Topic=device/Creality Falcon A1 Pro/SN123/info/online/v1
使用处理器 FalconSeriesMessageHandler 处理消息
Falcon系列上线上报: model=Creality Falcon A1 Pro, sn=SN123

[MQTT] 消息来自: Topic=device/Creality Falcon T1/SN456/info/online/v1  
使用处理器 FalconSeriesMessageHandler 处理消息
Falcon系列上线上报: model=Creality Falcon T1, sn=SN456
```

### 启用差异化处理后
```
[MQTT] 消息来自: Topic=device/Creality Falcon A1 Pro/SN123/info/putA1ProSpecific/v1
使用处理器 FalconA1ProMessageHandler 处理消息
Falcon A1 Pro特有材料识别: model=Creality Falcon A1 Pro, sn=SN123

[MQTT] 消息来自: Topic=device/Creality Falcon T1/SN456/info/online/v1
使用处理器 FalconSeriesMessageHandler 处理消息  
Falcon系列上线上报: model=Creality Falcon T1, sn=SN456
```

## 🎉 总结

这个架构完美实现了您的要求：

- ✅ **A1 Pro和T1共用通用方法**: 通过FalconSeriesMessageHandler实现
- ✅ **差异化处理预留**: 专用处理器保留但暂时禁用
- ✅ **按需启用**: 当有差异时可以轻松启用专用处理器
- ✅ **代码复用**: 最大化代码复用，避免重复
- ✅ **维护简单**: 通用逻辑集中管理，差异逻辑分离

现在A1 Pro和T1使用完全相同的处理逻辑，当未来需要差异化处理时，只需要简单地启用对应的专用处理器即可！
