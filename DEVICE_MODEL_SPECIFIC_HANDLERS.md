# 设备型号专用处理器架构

## 🎯 优化目标

基于您的反馈，我进一步优化了架构，为每个具体设备型号创建专用的处理器，解决了不同型号可能有不同topic接口和处理逻辑的问题。

## 🏗️ 最终架构设计

### 1. 层次化处理器架构
```
AbstractIotMessageHandler (顶层抽象)
├── AbstractLaserEngraverHandler (雕刻机基类)
│   ├── FalconA1ProMessageHandler (A1 Pro专用)
│   └── FalconT1MessageHandler (T1专用)
├── AirPurifierMessageHandler (空气净化器)
└── DefaultMessageHandler (默认处理器)
```

### 2. 设备型号精确匹配
```java
// Falcon A1 Pro 处理器
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {
    private static final String SUPPORTED_MODEL = "Creality Falcon A1 Pro";
    
    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODEL.equals(deviceModel); // 精确匹配
    }
}

// Falcon T1 处理器
@Component  
public class FalconT1MessageHandler extends AbstractLaserEngraverHandler {
    private static final String SUPPORTED_MODEL = "Creality Falcon T1";
    
    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODEL.equals(deviceModel); // 精确匹配
    }
}
```

## 📁 最终文件结构

```
falcon-iot/src/main/java/com/creality/lcd/falcon/service/handler/
├── IotMessageHandler.java                           # 核心接口
├── AbstractIotMessageHandler.java                   # 顶层抽象基类
├── IotMessageHandlerManager.java                    # 处理器管理器
├── DefaultMessageHandler.java                       # 默认处理器
├── AirPurifierMessageHandler.java                   # 空气净化器处理器
├── laser/                                           # 雕刻机处理器目录
│   ├── AbstractLaserEngraverHandler.java           # 雕刻机基类
│   ├── FalconA1ProMessageHandler.java              # A1 Pro专用处理器
│   └── FalconT1MessageHandler.java                 # T1专用处理器
└── airpurifier/                                     # 空气净化器专用目录
    └── FalconAPService.java                         # AP专用服务接口
```

## 🔧 核心特性实现

### 1. 雕刻机基类 - 公共功能
```java
public abstract class AbstractLaserEngraverHandler extends AbstractIotMessageHandler {
    @Autowired
    protected IotService iotService;
    
    // 通用的雕刻机处理方法
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        iotService.getOtaVersion(model, messageMap);
    }
    
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        iotService.updateDeviceWorkStatus(sn, messageMap);
    }
    
    // ... 其他通用方法
    
    // 子类可重写的抽象方法
    protected abstract String getSupportedModel();
    protected void handleModelSpecificMessage(String topic, Map<String, Object> messageMap) {}
}
```

### 2. Falcon A1 Pro - 专用功能
```java
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {
    
    // A1 Pro特有功能
    private void handleAdvancedCalibration(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro高级校准: model={}, sn={}", model, sn);
        // A1 Pro特有的高级校准逻辑
    }
    
    private void handleSmartCuttingMode(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro智能切割模式: model={}, sn={}", model, sn);
        // A1 Pro特有的智能切割逻辑
    }
    
    private void handleMaterialRecognition(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro材料识别: model={}, sn={}", model, sn);
        // A1 Pro特有的材料识别逻辑
    }
    
    // 重写基类方法，添加A1 Pro特有逻辑
    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        if (messageMap.containsKey("advancedStatus")) {
            log.info("处理A1 Pro高级状态信息: {}", messageMap.get("advancedStatus"));
        }
        super.handleDeviceStatusReport(model, sn, messageMap);
    }
    
    // A1 Pro特有的topic识别
    @Override
    protected boolean isModelSpecificTopic(String topic) {
        return topic.matches("^device/Creality Falcon A1 Pro/.+/info/putAdvancedCalibration/" + version + "$") ||
               topic.matches("^device/Creality Falcon A1 Pro/.+/info/putSmartCuttingMode/" + version + "$") ||
               topic.matches("^device/Creality Falcon A1 Pro/.+/info/putMaterialRecognition/" + version + "$");
    }
}
```

### 3. Falcon T1 - 专用功能
```java
@Component
public class FalconT1MessageHandler extends AbstractLaserEngraverHandler {
    
    // T1特有功能
    private void handleTurboEngravingMode(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1快速雕刻模式: model={}, sn={}", model, sn);
        // T1特有的快速雕刻逻辑
    }
    
    private void handlePrecisionPositioning(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1精密定位: model={}, sn={}", model, sn);
        // T1特有的精密定位逻辑
    }
    
    private void handleTemperatureControl(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1温度控制: model={}, sn={}", model, sn);
        // T1特有的温度控制逻辑
    }
    
    // 重写基类方法，添加T1特有逻辑
    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        Object alarmType = messageMap.get("alarmType");
        if ("TEMPERATURE_OVERHEAT".equals(alarmType)) {
            log.warn("T1温度过热告警: sn={}", sn);
            // T1特有的温度过热处理逻辑
        }
        super.handleAlarmReport(model, sn, messageMap);
    }
    
    // T1特有的topic识别
    @Override
    protected boolean isModelSpecificTopic(String topic) {
        return topic.matches("^device/Creality Falcon T1/.+/info/putTurboEngravingMode/" + version + "$") ||
               topic.matches("^device/Creality Falcon T1/.+/info/putPrecisionPositioning/" + version + "$") ||
               topic.matches("^device/Creality Falcon T1/.+/info/putTemperatureControl/" + version + "$");
    }
}
```

## 📊 处理器优先级设计

```java
// 处理器优先级（数值越小优先级越高）
AbstractLaserEngraverHandler: 10 (基础优先级)
├── FalconA1ProMessageHandler: 11 (精确匹配优先)
└── FalconT1MessageHandler: 12 (精确匹配优先)

AirPurifierMessageHandler: 20
DefaultMessageHandler: Integer.MAX_VALUE (最低优先级，兜底)
```

## 🚀 扩展性设计

### 1. 添加新雕刻机型号
```java
@Component
public class FalconNewModelMessageHandler extends AbstractLaserEngraverHandler {
    
    private static final String SUPPORTED_MODEL = "Creality Falcon New Model";
    
    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODEL.equals(deviceModel);
    }
    
    @Override
    public int getPriority() {
        return 13; // 设置合适的优先级
    }
    
    @Override
    protected String getSupportedModel() {
        return SUPPORTED_MODEL;
    }
    
    // 实现新型号特有的功能
    private void handleNewModelSpecificFeature(String model, String sn, Map<String, Object> messageMap) {
        // 新型号特有的处理逻辑
    }
    
    @Override
    protected boolean isModelSpecificTopic(String topic) {
        return topic.matches("^device/Creality Falcon New Model/.+/info/putNewFeature/" + version + "$");
    }
}
```

### 2. 为空气净化器添加专用Service
```java
@Service
public class FalconAPServiceImpl implements FalconAPService {
    
    @Override
    public void putAirQualityData(String sn, Map<String, Object> messageMap) {
        // 处理空气质量数据的专用逻辑
        // 可能包括PM2.5、PM10、甲醛等数据的特殊处理
    }
    
    @Override
    public void putFanSpeedAdjustment(String sn, Map<String, Object> messageMap) {
        // 处理风速调节的专用逻辑
    }
    
    // ... 其他专用方法
}
```

## 🔍 消息处理流程

### 1. 消息到达流程
```
MQTT消息 → onMessageReceived() 
         → IotMessageHandlerManager.handleMessage()
         → 提取设备型号 ("Creality Falcon A1 Pro")
         → 查找匹配的处理器 (FalconA1ProMessageHandler)
         → 执行具体的处理逻辑
```

### 2. 处理器选择逻辑
```java
// 按优先级排序，选择第一个支持的处理器
Optional<IotMessageHandler> handler = messageHandlers.stream()
    .filter(h -> h.supports("Creality Falcon A1 Pro"))
    .findFirst();

// 结果：FalconA1ProMessageHandler (优先级11，精确匹配)
```

### 3. 消息分发逻辑
```java
// 在AbstractLaserEngraverHandler中
if (isOtaRequest(topic)) {
    handleOtaRequest(model, sn, messageMap);           // 通用处理
} else if (isModelSpecificTopic(topic)) {
    handleModelSpecificMessage(topic, messageMap);     // 型号特有处理
} else {
    handleCustomMessage(topic, messageMap);            // 其他处理
}
```

## ✅ 优化成果

### 1. 解决的问题
- ✅ **不同型号共用Service**: 每个型号有专用的处理器和可选的专用Service
- ✅ **topic接口差异**: 每个型号可以定义自己特有的topic格式
- ✅ **处理逻辑差异**: 每个型号可以重写基类方法，实现特有逻辑
- ✅ **功能差异**: 每个型号可以添加自己特有的功能方法

### 2. 架构优势
- ✅ **精确匹配**: 每个型号精确匹配，避免误处理
- ✅ **代码复用**: 公共逻辑在基类中实现
- ✅ **易于扩展**: 新增型号只需继承基类
- ✅ **职责清晰**: 每个处理器只负责一个型号

### 3. 维护性提升
- ✅ **问题定位**: 问题可以精确定位到具体型号的处理器
- ✅ **功能隔离**: 不同型号的功能完全隔离，互不影响
- ✅ **测试友好**: 每个处理器可以独立测试

## 🎯 使用示例

### 启动日志
```
初始化IoT消息处理器管理器，共注册5个处理器:
  - FalconA1ProMessageHandler: 优先级=11
  - FalconT1MessageHandler: 优先级=12
  - AirPurifierMessageHandler: 优先级=20
  - DefaultMessageHandler: 优先级=2147483647
```

### A1 Pro消息处理
```
[MQTT] 消息来自: Topic=device/Creality Falcon A1 Pro/SN123/info/putAdvancedCalibration/v1
使用处理器 FalconA1ProMessageHandler 处理消息
Falcon A1 Pro高级校准: model=Creality Falcon A1 Pro, sn=SN123
```

### T1消息处理
```
[MQTT] 消息来自: Topic=device/Creality Falcon T1/SN456/info/putTurboEngravingMode/v1
使用处理器 FalconT1MessageHandler 处理消息
Falcon T1快速雕刻模式: model=Creality Falcon T1, sn=SN456
```

## 🎉 总结

这次优化成功地解决了您提出的问题：

1. **不同型号不再共用Service**: 每个型号有专用的处理器，可以注入专用的Service
2. **支持不同的topic接口**: 每个型号可以定义自己特有的topic格式和处理逻辑
3. **支持不同的Controller方法**: 每个型号可以调用不同的Service方法
4. **完美的扩展性**: 新增型号只需继承基类，实现特有功能

现在的架构既保持了代码的复用性，又提供了足够的灵活性来处理不同型号的差异化需求！
