package com.creality.falcon.stat.domain.report;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultItemVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumLiuCunLvItem;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户留存率
 */
@Slf4j
@Component
public class ReportHandleUserLiuCunLv extends AbsReportHandle {
	
	@Autowired
	private IReportUserNumService reportUserNumService;
	
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return BaseResponse.success();
	}
	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_user_retention_rate;
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}
	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		vo.setReportNum("0%");
		
		//子项
		List<ReportDataResultItemVo> itemList=new ArrayList<ReportDataResultItemVo>();
		vo.setItemList(itemList);
		
		//统计时间不满足条件
		if(data.getStartDate()==null||data.getEndDate()==null||data.getEndDate().getTime()<=data.getStartDate().getTime()) {
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumLiuCunLvItem.item_new_user.getCode());
			item.setName(EnumLiuCunLvItem.item_new_user.getName());
			item.setReportNum("0%");
			
			ReportDataResultItemVo itemExsit=new ReportDataResultItemVo();
			itemExsit.setCode(EnumLiuCunLvItem.item_exsit_user.getCode());
			itemExsit.setName(EnumLiuCunLvItem.item_exsit_user.getName());
			itemExsit.setReportNum("0%");
			itemList.add(itemExsit);
			
			return BaseResponse.success(vo);
		}
		
		//计算时间跨度
		Long subTime=data.getEndDate().getTime()-data.getStartDate().getTime();
		Date firstDate=new Date(data.getStartDate().getTime()-subTime);
		data.setFirstDate(firstDate);
		
		/*
		 * 查询正常留存率
		 */
		List<String> tokenObjListAll=reportUserNumService.searchDistinctUserIdList(data);
		if(CollectionUtil.isNotEmpty(tokenObjListAll)) {
			data.setTokenObjList(tokenObjListAll);
			Integer num=reportUserNumService.searchDistinctUserIdsNum(data);
			BigDecimal result = BigDecimal.valueOf(num).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(tokenObjListAll.size()), 2, RoundingMode.HALF_UP);
			vo.setReportNum(result.toString()+"%");
		}
		
		/*
		 * 查询新用户留存率
		 */
		//查询上一个周期的新用户数量
		List<String> tokenObjListNew=reportUserNumService.searchDistinctUserIdHistory(data);
		if(!CollectionUtils.isEmpty(tokenObjListNew)) {
			//查询第一天往后的数量
			data.setTokenObjList(tokenObjListNew);
			Integer num=reportUserNumService.searchDistinctUserIdsNum(data);
			BigDecimal result = BigDecimal.valueOf(num)
				    .multiply(BigDecimal.valueOf(100))
				    .divide(BigDecimal.valueOf(tokenObjListNew.size()), 2, RoundingMode.HALF_UP);
			
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumLiuCunLvItem.item_new_user.getCode());
			item.setName(EnumLiuCunLvItem.item_new_user.getName());
			item.setReportNum(result.toString()+"%");
			itemList.add(item);
		}else {
			//没有数据的时候
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumLiuCunLvItem.item_new_user.getCode());
			item.setName(EnumLiuCunLvItem.item_new_user.getName());
			item.setReportNum("0%");
			itemList.add(item);
		}
		
		/*
		 * 查询老用户留存率 
		 */
		List<String> tokenObjListOld=reportUserNumService.searchDistinctUserIdsExsit(data);
		if(!CollectionUtils.isEmpty(tokenObjListOld)) {
			//查询第一天往后的数量
			data.setTokenObjList(tokenObjListOld);
			Integer num=reportUserNumService.searchDistinctUserIdsNum(data);
			BigDecimal result = BigDecimal.valueOf(num)
				    .multiply(BigDecimal.valueOf(100))
				    .divide(BigDecimal.valueOf(tokenObjListOld.size()), 2, RoundingMode.HALF_UP);
			
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumLiuCunLvItem.item_exsit_user.getCode());
			item.setName(EnumLiuCunLvItem.item_exsit_user.getName());
			item.setReportNum(result.toString()+"%");
			itemList.add(item);
		}else {
			//没有数据的时候
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumLiuCunLvItem.item_exsit_user.getCode());
			item.setName(EnumLiuCunLvItem.item_exsit_user.getName());
			item.setReportNum("0%");
			itemList.add(item);
		}
		
		
		
		
		
		return BaseResponse.success(vo);
	}
	
	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null;
	}

}
