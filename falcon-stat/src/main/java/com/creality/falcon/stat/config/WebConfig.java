package com.creality.falcon.stat.config;

import com.creality.falcon.stat.interceptor.SessionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private CrossConfig config;

    @Autowired
    private SessionInterceptor sessionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义要排除的路径
        //List<String> excludePaths = new ArrayList<String>();
        registry.addInterceptor(sessionInterceptor)
                .addPathPatterns("/**"); // 拦截所有请求
        //.excludePathPatterns(excludePaths);
    }

    //跨域配置
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 所有路径
                .allowedOrigins(config.getUrls().toArray(String[]::new)) // 允许所有来源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的方法
                .allowedHeaders("*") // 允许所有头
                .allowCredentials(true) // 允许凭证
                .maxAge(3600); // 预检请求缓存时间(秒)
    }

}
