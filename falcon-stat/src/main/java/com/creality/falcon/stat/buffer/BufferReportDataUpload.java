package com.creality.falcon.stat.buffer;

import java.util.concurrent.LinkedBlockingDeque;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportUserNumUpByClientIdDto;
import com.creality.falcon.stat.servicemvc.IReportStopTimeService;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;
import com.creality.falcon.stat.servicemvc.IReportViewNumService;
import com.creality.falcon.stat.utils.GeoLiteUtil;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 独立用户数据上报缓冲
 */
@Slf4j
@Component
public class BufferReportDataUpload {
	@Autowired
	private IReportUserNumService reportUserNumService;
	@Autowired
	private IReportStopTimeService reportStopTimeService;
	@Autowired
	private IReportViewNumService reportViewNumService;
	
	private LinkedBlockingDeque<ReportUserNumUpByClientIdDto> queue=new LinkedBlockingDeque<ReportUserNumUpByClientIdDto>(1000);
	
	/**
	 * 将数据添加到队列
	 * @param reportData
	 * @return
	 */
	public boolean addReportData(ReportUserNumUpByClientIdDto reportData) {
		if(reportData==null) {
			return false;
		}
		//添加消息
		try {
			queue.put(reportData);
			log.info("当前数据队列长度：{}",queue.size());
		} catch (InterruptedException e) {
			e.printStackTrace();
			log.info("上报数据缓冲报错{}信息{}",JSON.toJSONString(reportData),e.getMessage());
		}
		return true;
	}
	
	public void startConsumerReportData() {
		while(true) {
			ReportUserNumUpByClientIdDto msg=null;
			try {
				msg=queue.take();
			} catch (Exception e) {
				log.error("队列消费消息出错:{}",e.getMessage());
			}
			//消费消息
			if(msg!=null) {
				try {
					ReportUserNum data=msg.getReportUserNum();
					//计算ip归属地
					try {
						String country=GeoLiteUtil.getCountry(data.getUserIp());
						data.setUserAddress(StrUtil.isNotEmpty(country)?country:"未知");
					} catch (Exception e) {
						data.setUserAddress("未知");
					}
					
					//如果是登录状态并携带有clientId则更新最近用户的id信息
					if(data.getTokenType()!=null&&data.getTokenType()==1&&StrUtil.isNotEmpty(msg.getClientId())) {
						reportUserNumService.updateUserTokenObjByClientId(msg.getClientId(),data.getTokenObj());
						reportStopTimeService.updateUserTokenObjByClientId(msg.getClientId(),data.getTokenObj());
						reportViewNumService.updateUserTokenObjByClientId(msg.getClientId(),data.getTokenObj());
						log.info("跟新独立用户数据完成:{},{}",msg.getClientId(),data.getTokenObj());
					}
					reportUserNumService.addReportUserNum(data);
				} catch (Exception e) {
					log.error("队列消费消息{}出错:{}",JSON.toJSONString(msg),e.getMessage());
				}
			}
		}
	}
}
