package com.creality.falcon.stat.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.util.Date;

/**
 * EventReportEntity
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventReportEntity implements Serializable {

    @MongoId
    private ObjectId id;
    private String reportDay;
    private String userId;
    private String clientId;
    private String reqEndPoint; // 参考枚举：ReqEndPointEnum
    private String endPointGroup; // 参考枚举：EndPointGroupEnum
    private String eventId; // 事件ID（点击按钮的编码/页面的编码）
    private Integer eventValue; // 事件值（默认=1）
    private String eventOrigin; // 事件来源
    private String bizType; // 业务类型
    private String bizId; // 业务ID
    private String ip; // IP地址
    private String version; // 版本号
    private Integer versionCode; // 版本号
    private Date createTime; // 创建时间

}
