package com.creality.falcon.stat.servicemvc;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.falcon.stat.entity.point.model.ModleKV;
import com.creality.falcon.stat.entity.report.ReportStopTime;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;

public interface IReportStopTimeService  extends IService<ReportStopTime> {

	public int addReportStopTime(ReportStopTime reportStopTime);
	
	public int delReportStopTimeById(String id);
	
	public ReportStopTime selReportStopTimeById(String id);
	
	public int upReportStopTime(ReportStopTime reportStopTime);
	
	public List<ReportStopTime> selReportStopTimeList(ReportStopTime reportStopTime);

	public Integer reportUserNum(ReportWebSearchDto data);

	public Long reportStopTime(ReportWebSearchDto data);

	public Integer reportItemCount(ReportWebSearchDto data);

	public void updateUserTokenObjByClientId(String clientId, String tokenObj);

	public List<ModleKV> reportStopTimeGroupByTime(ReportWebSearchDto data);
	

}

