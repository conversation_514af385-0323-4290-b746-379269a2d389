package com.creality.falcon.stat.service.mongo.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.creality.falcon.stat.base.EndPointGroupEnum;
import com.creality.falcon.stat.entity.bo.EventIdStatBo;
import com.creality.falcon.stat.entity.common.EventReportEntity;
import com.creality.falcon.stat.framework.spring.SpringContextHolder;
import com.creality.falcon.stat.pojo.request.EventReportReqParam;
import com.creality.falcon.stat.service.mongo.EventReportService;
import com.creality.falcon.stat.service.mongo.EventStatService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * EventReportServiceImpl
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class EventReportServiceImpl implements EventReportService {

    @Resource
    private MongoTemplate mongoTemplate;

    private String getCollectionName(String month) {
        // 按月维度创表，如：common_event_report_202505
        return "event_report_" + month;
    }

    @Override
    public void report(EventReportReqParam param) {
        if (CollectionUtil.isEmpty(param.getItems())) {
            return;
        }
        // 数据处理
        Date now = new Date();
        String reportDay = DateUtil.format(now, "yyyy-MM-dd");
        EndPointGroupEnum endPointGroupEnum = EndPointGroupEnum.getEndPointGroup(param.getApiReqEndPoint());
        String endPointGroup = endPointGroupEnum == null ? null : endPointGroupEnum.getCode();
        List<EventReportEntity> list = new ArrayList<>(param.getItems().size());
        param.getItems().forEach(item -> {
            // 校验非空的eventOrigin是否属于eventId
            if (item.getEventOrigin() == null || Objects.equals(item.getEventId(), item.getEventOrigin().getEventId())) {
                list.add(EventReportEntity.builder()
                        .reportDay(reportDay)
                        .userId(param.getApiUserId())
                        .clientId(param.getApiClientId())
                        .reqEndPoint(param.getApiReqEndPoint())
                        .endPointGroup(endPointGroup)
                        .eventId(item.getEventId().name())
                        .eventValue(item.getEventValue() == null ? 1 : item.getEventValue())
                        .eventOrigin(item.getEventOrigin() == null ? null : item.getEventOrigin().name())
                        .bizType(item.getBizType() == null ? null : item.getBizType().name())
                        .bizId(item.getBizId() == null ? null : item.getBizId())
                        .ip(param.getApiIp())
                        .version(param.getApiVersion())
                        .versionCode(param.getApiVersionCode())
                        .createTime(now)
                        .build());
                // 事件处理
                List<Class<? extends EventStatService>> clazzList = item.getEventId().getClazzList();
                if (CollectionUtil.isNotEmpty(clazzList)) {
                    clazzList.forEach(clazz -> {
                        CompletableFuture.runAsync(() -> {
                            Objects.requireNonNull(SpringContextHolder.getBean(clazz)).saveEventStat(reportDay, param.getBaseRequestParam(), item);
                        });
                    });
                }
            }
        });
        mongoTemplate.insert(list, getCollectionName(DateUtil.format(now, "yyyyMM")));
    }

    @Override
    public List<EventIdStatBo> selectEventIdCountData(String statDay) {
        if (StrUtil.isBlank(statDay)) {
            return null;
        }
        // 构建查询参数，统计事件ID数据，需要根据三个维度分组：eventId, eventOrigin, endPointGroup, reqEndPoint
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("reportDay").is(statDay)),
                Aggregation.group("eventId", "eventOrigin", "reqEndPoint", "endPointGroup")
                        .count().as("statNum"),
                Aggregation.project()
                        .and("_id.eventId").as("eventId")
                        .and("_id.eventOrigin").as("eventOrigin")
                        .and("_id.reqEndPoint").as("reqEndPoint")
                        .and("_id.endPointGroup").as("endPointGroup")
                        .and("statNum").as("statNum")
        );
        String collectionMonth = DateUtil.format(DateUtil.parse(statDay, "yyyy-MM-dd"), "yyyyMM");
        List<EventIdStatBo> result = mongoTemplate.aggregate(aggregation, getCollectionName(collectionMonth), EventIdStatBo.class).getMappedResults();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }
}
