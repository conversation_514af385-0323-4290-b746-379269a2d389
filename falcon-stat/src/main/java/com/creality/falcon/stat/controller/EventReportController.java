package com.creality.falcon.stat.controller;

import com.creality.falcon.stat.pojo.request.EventReportReqParam;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;
import com.creality.falcon.stat.service.mongo.EventReportService;
import com.creality.falcon.stat.utils.ParamUtil;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * EventReportController
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/event")
public class EventReportController {

    @Resource
    private EventReportService eventReportService;

    @RequestMapping("/report")
    public BaseResponse<?> report(@RequestBody @Validated EventReportReqParam param) {
        // 参数校验
        if (!ParamUtil.checkHeader(param)) {
            return BaseResponse.error(ResultEnum.VALIDATE_ERROR.getCode(), "缺少请求头参数");
        }
        // TODO 后续使用MQ异步处理
        eventReportService.report(param);
        return BaseResponse.success();
    }

}
