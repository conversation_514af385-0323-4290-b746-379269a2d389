package com.creality.falcon.stat.global;

import cn.hutool.json.JSONUtil;
import com.creality.falcon.stat.base.RequestHeaderConstant;
import com.creality.falcon.stat.cache.CacheManager;
import com.creality.falcon.stat.interceptor.SessionUser;
import com.creality.falcon.stat.utils.IpUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * SecurityFilter
 *
 * <AUTHOR>
 * @since 2023-06-08
 */
@Slf4j
@Component
@WebFilter(urlPatterns = "/*")
public class SecurityFilter implements Filter {

    @Autowired
    private CacheManager cacheManager;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        if ("OPTIONS".equals(request.getMethod())) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        CustomHttpServletRequestWrapper requestWrapper = new CustomHttpServletRequestWrapper(request);
        String sessionId = requestWrapper.getHeader(RequestHeaderConstant.SESSION_ID);

        String userId = null;
        String userName = null;
        if (StringUtils.isNotBlank(sessionId)) {
            SessionUser currentUser = cacheManager.getUserBySessionId(sessionId);
            if (currentUser != null) {
                userId = currentUser.getUserId();
                userName = currentUser.getUserName();
            }
        }
        // 补充参数到header中
        if (StringUtils.isNotBlank(userId) && !userId.equalsIgnoreCase("null")) {
            requestWrapper.addHeader(RequestHeaderConstant.HEADER_USER_ID, userId);
        }
        if (StringUtils.isNotBlank(userName) && userName.equalsIgnoreCase("null")) {
            requestWrapper.addHeader(RequestHeaderConstant.HEADER_USER_NAME, userName);
        }
        requestWrapper.addHeader(RequestHeaderConstant.HEADER_IP, IpUtils.getClientIp(request));
        log.info("请求接口中的 header信息 = \n{}", JSONUtil.toJsonStr(requestWrapper.getHeaderMap()));
        filterChain.doFilter(requestWrapper, response);
    }

}
