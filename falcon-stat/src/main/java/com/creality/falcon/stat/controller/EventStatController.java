package com.creality.falcon.stat.controller;

import com.creality.falcon.stat.pojo.request.funnel.EventFunnelStatReqParam;
import com.creality.falcon.stat.pojo.response.funnel.EventFunnelStatRespParam;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;
import com.creality.falcon.stat.service.mongo.EventIdStatService;
import com.creality.falcon.stat.service.mongo.EventIdUserBehaviorStatService;
import com.creality.falcon.stat.utils.ParamUtil;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * EventStatController
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@RequestMapping("/event-stat")
public class EventStatController {

    @Resource
    private EventIdStatService eventIdStatService;
    @Resource
    private EventIdUserBehaviorStatService eventIdUserBehaviorStatService;

    @RequestMapping("/funnel-stat")
    public BaseResponse<EventFunnelStatRespParam> funnelStat(@RequestBody @Validated EventFunnelStatReqParam param) {
        // 参数校验
        if (!ParamUtil.checkHeader(param)) {
            return BaseResponse.error(ResultEnum.VALIDATE_ERROR.getCode(), "缺少请求头参数");
        }
        // TODO 后续使用MQ异步处理
//        eventReportService.report(param);
        return BaseResponse.success();
    }

}
