package com.creality.falcon.stat.domain.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.buffer.BufferReportDataUpload;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportUserNumUpByClientIdDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultItemVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.enums.EnumReportItemShow;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;
import com.creality.falcon.stat.utils.IdUtil;
/**
 * 活跃用户数
 */
@Component
public class ReportHandleAliveUserNum extends AbsReportHandle {
	
	@Autowired
	private IReportUserNumService reportUserNumService;
	@Autowired
	private IdUtil idUtil;
	@Autowired
	private BufferReportDataUpload bufferReportDataUpload;
	
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto dto) {
		ReportUserNum report=new ReportUserNum();
		report.setId(idUtil.createId());
		report.setReportCode(this.getReportCode().getCode());
		report.setCreateTime(new Date());
		report.setTokenType(getTokenType(dto));
		report.setFromType(dto.getFromType());
		report.setTokenObj(getDataToken(dto));
		report.setReportType(0);
		report.setUserIp(dto.getIpAds());
		
		ReportUserNumUpByClientIdDto data=new ReportUserNumUpByClientIdDto();
		data.setReportUserNum(report);
		data.setClientId(dto.getClientId());
		bufferReportDataUpload.addReportData(data);
		
		return BaseResponse.success();
	}
	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_alive_user_num;
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		if(data==null) {
			return false;
		}
		return true;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		data.setReportCode(this.getReportCode().getCode());
		data.setReportType(0);
		Integer numAlive=reportUserNumService.reportUserNum(data);
		
		data.setReportCode(EnumDataReportCode.code_user_num.getCode());
		data.setReportType(1);
		data.setTokenType(1);
		Integer numLogin=reportUserNumService.reportUserNum(data);
		Integer numAll=numLogin+numAlive;//EnumDataReportFromType.isFromApp(data.getFromType())?numLogin:numLogin+numAlive;
		vo.setReportNum(numAll.toString());
		//如果不是app登录则显示登录用户数量 app
		if(!EnumDataReportFromType.isFromApp(data.getFromType())) {
			ReportDataResultItemVo item=new ReportDataResultItemVo();
			item.setCode(EnumReportItemShow.show_login_usre_num.getCode());
			item.setName(EnumReportItemShow.show_login_usre_num.getShowName());
			item.setReportNum(numLogin.toString());
			List<ReportDataResultItemVo> itemList=new ArrayList<ReportDataResultItemVo>();
			itemList.add(item);
			vo.setItemList(itemList);
		}
		return BaseResponse.success(vo);
	}

	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null;
	}

}
