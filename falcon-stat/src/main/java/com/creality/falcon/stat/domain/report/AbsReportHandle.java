package com.creality.falcon.stat.domain.report;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;

import com.creality.falcon.stat.entity.point.model.ModleKV;
import com.creality.falcon.stat.entity.report.FalconHomeDataReport;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataStopTimeItemVo;
import com.creality.falcon.stat.entity.report.vo.ReportStopTimeDetail;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.enums.EnumStopTimeGroup;
import com.creality.falcon.stat.repository.FalconHomeDataReportRepository;
import com.creality.falcon.stat.service.report.FalconHomeDataReportService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

public abstract class AbsReportHandle implements IReportHandle{
	
	@Autowired
	private FalconHomeDataReportRepository falconHomeDataReportRepository;
	
	@Autowired
	protected FalconHomeDataReportService falconHomeDataReportService;
	
	public String getCollectionName() {
		return "falcon_home_data_report_"+YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
	}
	
	public String getShowNameByType(String type) {
		String typeName=EnumDataReportFromType.getSshowNameByType(type);
		StringBuilder show=new StringBuilder(typeName);
		show.append(this.getReportCode().getCodeName());
		return show.toString();
	}

	public void saveReprotData(FalconHomeDataReport report) {
		falconHomeDataReportRepository.save(report);
	}
	
	public int getTokenType(ReportWebUploadDto data) {
		return data.getUserToken()!=null?1:0;
	}
	
	protected  boolean  tokenTypeIsLogin(Integer tokenType) {
		return tokenType!=null&&tokenType==1;
	}
	
	public String getDataToken(ReportWebUploadDto data) {
		return data.getUserToken()!=null?data.getUserToken():data.getClientId()!=null?data.getClientId():data.getIpAds();
	}
	/**创建数据并赋值*/
	public FalconHomeDataReport createBaseReportData(ReportWebUploadDto data) {
		FalconHomeDataReport report=new FalconHomeDataReport();
		report.setReprotCode(this.getReportCode().getCode());
		report.setReportIndex(this.getReportCode().getIndex());
		report.setReportDate(data.getCreateDate());
		//不用用户来统计的需要在子类重写覆盖
		report.setDistinctKey(getDataToken(data));
		
		report.setTokenObj(getDataToken(data));
		report.setTokenType(getTokenType(data));
		report.setIpAds(data.getIpAds());
		report.setFromType(data.getFromType());
		report.setCreateTime(data.getCreateDate());
		
		report.setProjectId(data.getProjectId());
		report.setReportNum(data.getReportNum());
		report.setReportTime(data.getReportTime());
		
		return report;
	}
	protected String getPrefixName(String fromType) {
		return EnumDataReportFromType.getSshowNameByType(fromType);
	}
	
	protected String timeMillesToShow(Long times) {
		if(times!=null) {
			Duration duration = Duration.ofMillis(times);
			long days = duration.toDays();
			long hours = duration.toHours() % 24;
			long minutes = duration.toMinutes() % 60;
			long seconds=duration.toSeconds()%60;
			if(times<1000L) {
				seconds+=1;
			}
			StringBuilder showTime=new StringBuilder();
			if(days>0) {
				showTime.append(days+"天");
			}
			if(hours>0) {
				showTime.append(hours+"时");
			}
			if(minutes>0) {
				showTime.append(minutes+"分");
			}
			if(seconds>0) {
				showTime.append(seconds+"秒");
			}
			return showTime.toString();
		}
		
		return new String("");
	}
	
	
	/**
	 * 根据停留时间统计每个时间段内的人数和百分比
	 * @param scoreList
	 * @return
	 */
	public ReportStopTimeDetail createStopTimeItems(List<ModleKV> scoreList) {
		ReportStopTimeDetail detail=new ReportStopTimeDetail();
		List<ReportDataStopTimeItemVo> itemList=new ArrayList<ReportDataStopTimeItemVo>();
		
		if(CollectionUtil.isNotEmpty(scoreList)) {
			//将时间转为分钟
			scoreList.forEach(s->{
				Long value=s.getScore();
				value=value/(1000*60);
				if(value<=1) {
					value=1L;
				}
				String showScore=EnumStopTimeGroup.getShowScore(value);
				//根据分钟设置展示时间
				if(StrUtil.isNotEmpty(showScore)) {
					s.setShowScore(showScore);
				}
				s.setScore(value);
			});
			//统计数量
			Map<String,Long> scoreMap=scoreList.stream().filter(s->{
				return StrUtil.isNotEmpty(s.getShowScore());
			}).collect(Collectors.groupingBy(ModleKV::getShowScore,Collectors.counting()));
			
			//组合展示结果
			if(CollectionUtil.isNotEmpty(scoreMap)) {
				for(EnumStopTimeGroup e:EnumStopTimeGroup.values()) {
					ReportDataStopTimeItemVo vo=new ReportDataStopTimeItemVo();
					vo.setShowName(e.getShowName());
					//计算百分比
					Long num=scoreMap.get(e.getShowName());
					if(num!=null) {
						vo.setUserNum(num+"人");
						vo.setPercent(new BigDecimal(100.0 * num /scoreList.size()).setScale(2, RoundingMode.DOWN).toString()+"%");
					}else {
						vo.setUserNum("0人");
						vo.setPercent("0%");
					}
					itemList.add(vo);
				}
				detail.setStopTimeItems(itemList);
				return detail;
			}
		}
		
		for(EnumStopTimeGroup e:EnumStopTimeGroup.values()) {
			ReportDataStopTimeItemVo vo=new ReportDataStopTimeItemVo();
			vo.setShowName(e.getShowName());
			vo.setUserNum("0");
			vo.setPercent("0%");
			itemList.add(vo);
		}
		detail.setStopTimeItems(itemList);
		return detail;
	}
	
	

}
