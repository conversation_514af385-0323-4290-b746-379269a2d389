package com.creality.falcon.stat.domain.report;

import java.math.BigDecimal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.service.mongo.MongoService;

import cn.hutool.core.date.DateUtil;
/**
 * app云空间使用量
 */
@Component
public class ReportHandleCloudSpaceUsed extends AbsReportHandle{

	@Autowired
	private MongoService mongoService;
	
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return null;
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_cloud_space_used;
	}

	@Override
	public boolean matchFromType(String fromType) {
		return EnumDataReportFromType.isFromApp(fromType);
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		//计算人均停留时常
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(this.getReportCode().getCodeName());
		vo.setFromType(data.getFromType());
		vo.setReportNum("0");
		if(data.getStartDate()!=null&&data.getEndDate()!=null) {
			BigDecimal used=mongoService.selectCloudSpaceAvgUse(DateUtil.format(data.getStartDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(data.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
			vo.setReportNum(used!=null?used.toString():"0");
		}
		return BaseResponse.success(vo);
	}

}
