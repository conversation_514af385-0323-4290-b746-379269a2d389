package com.creality.falcon.stat.servicemvc.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.falcon.stat.entity.point.PointPageViewNum;
import com.creality.falcon.stat.entity.point.dto.PointPageViewSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.vo.PointReprotKvVo;
import com.creality.falcon.stat.mapper.PointPageViewNumMapper;
import com.creality.falcon.stat.servicemvc.IPointPageViewNumService;

@Service
public class PointPageViewNumService extends ServiceImpl<PointPageViewNumMapper, PointPageViewNum> implements IPointPageViewNumService{
	
	@Autowired
	private PointPageViewNumMapper pointPageViewNumMapper;

	@Override
	public int addPointPageViewNum(PointPageViewNum pointPageViewNum) {
		return pointPageViewNumMapper.addPointPageViewNum(pointPageViewNum);
	}

	@Override
	public Integer searchCountBySearchDto(PointReportResultSearchDto dto) {
		return pointPageViewNumMapper.searchCountBySearchDto(dto);
	}

	@Override
	public List<PointReprotKvVo> reportPageViewFromType(PointPageViewSearchDto dto) {
		return pointPageViewNumMapper.reportPageViewFromType(dto);
	}

}
