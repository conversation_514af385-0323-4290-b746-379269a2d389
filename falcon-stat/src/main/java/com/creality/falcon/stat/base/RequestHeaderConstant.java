package com.creality.falcon.stat.base;

/**
 * RequestHeaderConstant
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
public class RequestHeaderConstant {

    // -------------------------

    public static final String PARAM_USER_ID = "apiUserId";
    public static final String PARAM_USER_NAME = "apiUserName";
    public static final String PARAM_REQ_END_POINT = "apiReqEndPoint";
    public static final String PARAM_VERSION = "apiVersion";
    public static final String PARAM_VERSION_CODE = "apiVersionCode";
    public static final String PARAM_IP = "apiIp";
    public static final String PARAM_CLIENT_ID = "apiClientId";

    public static final String SESSION_ID = "sessionId";
    public static final String TOKEN = "x-token";

    public static final String HEADER_USER_ID = "userId"; // 系统补充
    public static final String HEADER_USER_NAME = "userName"; // 系统补充
    public static final String HEADER_REQ_END_POINT = "reqEndPoint";
    public static final String HEADER_VERSION = "version";
    public static final String HEADER_VERSION_CODE = "versionCode";
    public static final String HEADER_IP = "ip"; // 系统补充
    public static final String HEADER_CLIENT_ID = "clientId";

    // -------------------------

    public static final String CONTENT_TYPE = "application/json;charset=UTF-8";

}
