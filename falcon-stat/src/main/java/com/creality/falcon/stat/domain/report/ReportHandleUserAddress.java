package com.creality.falcon.stat.domain.report;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.entity.report.vo.ReportKvModel;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;

import cn.hutool.core.collection.CollectionUtil;

@Component
public class ReportHandleUserAddress extends AbsReportHandle {
	@Autowired
	private IReportUserNumService reportUserNumService;

	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return null;
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_user_address;
	}

	@Override
	public boolean matchFromType(String fromType) {
		return true;
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(this.getReportCode().getCodeName());
		vo.setFromType(data.getFromType());
		
		//独立用户数据改为登录+未登录+活跃用户
		List<String> codeList=new ArrayList<String>();
		codeList.add(EnumDataReportCode.code_user_num.getCode());
		codeList.add(EnumDataReportCode.code_alive_user_num.getCode());
		//去掉默认的统计code-否侧查询不准确
		data.setReportCode(null);
		data.setReportCodeList(codeList);
		
		List<ReportKvModel> modelList=reportUserNumService.reportUserAddressNum(data);
		if(CollectionUtil.isNotEmpty(modelList)) {
			modelList=modelList.stream().filter(m->{
				return m.getKeyName()!=null&&m.getKeyNum()!=null;
			}).collect(Collectors.toList());
		}
		vo.setKvModelList(modelList);
		return BaseResponse.success(vo);
	}

}
