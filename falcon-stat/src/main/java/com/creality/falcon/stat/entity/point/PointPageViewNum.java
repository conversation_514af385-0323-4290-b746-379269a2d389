package com.creality.falcon.stat.entity.point;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 埋点页面浏览量
 * <AUTHOR>
 */
@Data
public class PointPageViewNum implements Serializable {

    private static final long serialVersionUID = 1L;

    /**创建时间*/
    private Date createTime;
    private Long id;
    /**来源标识*/
    private String pointFromCode;
    /**来源类型*/
    private String fromType;
    /**0未登录1已登录*/
    private Integer loginState;
    /**用户ID或者ip地址标识*/
    private String userIpId;
    /**查看数量*/
    private Integer viewNum;
}
