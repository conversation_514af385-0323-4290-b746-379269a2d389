package com.creality.falcon.stat.domain.report;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.point.model.ModleKV;
import com.creality.falcon.stat.entity.report.ReportStopTime;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportStopTimeService;
import com.creality.falcon.stat.utils.IdUtil;

@Component
public class ReportHandleStopTime extends AbsReportHandle {
	
	@Autowired
	private IReportStopTimeService reportStopTimeService;
	
	@Autowired
	private IdUtil idUtil;

	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		ReportStopTime saveData=new ReportStopTime();
		saveData.setId(idUtil.createId());
		saveData.setCreateTime(new Date());
		saveData.setReportCode(this.getReportCode().getCode());
		saveData.setFromType(data.getFromType());
		saveData.setReportTime(data.getReportTime());
		saveData.setTokenObj(getDataToken(data));
		saveData.setTokenType(getTokenType(data));
		reportStopTimeService.save(saveData);
		return BaseResponse.success();
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_stop_time;
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		if(data==null) {
			return false;
		}
		if(data.getReportTime()==null) {
			return false;
		}
		return true;
	}

	/**
	 * 计算平均在线时长
	 */
	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		//计算人均停留时常
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		
		//从用户处查询
		//data.setReportCode(EnumDataReportCode.code_user_num.getCode());
		//Integer userNum=reportUserNumService.reportUserNum(data);
		
		data.setReportCode(this.getReportCode().getCode());
		Integer userNum=reportStopTimeService.reportUserNum(data);
		if(userNum<=0) {
			vo.setReportNum("0");
		}else {
			Long timeAll=reportStopTimeService.reportStopTime(data);
			Long score=timeAll/userNum;
			vo.setReportNum(timeMillesToShow(score));
			//统计停留时长分布
			List<ModleKV> scoreList=reportStopTimeService.reportStopTimeGroupByTime(data);
			vo.setStopTimeDetail(createStopTimeItems(scoreList));
		}
		return BaseResponse.success(vo);
	}

	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null;
	}
}
