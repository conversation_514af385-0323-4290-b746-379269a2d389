package com.creality.falcon.stat.base;

import lombok.Getter;

import java.util.List;


/**
 * EndPointGroupEnum
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Getter
public enum EndPointGroupEnum {

    WEB("WEB", List.of(ReqEndPointEnum.WEBSITE)),

    PC("PC", List.of(ReqEndPointEnum.WINDOWS_CLIENT, ReqEndPointEnum.MAC_CLIENT, ReqEndPointEnum.WEBSITE_QT)),

    APP("APP", List.of(ReqEndPointEnum.ANDROID_APP, ReqEndPointEnum.IOS_APP));

    private final String code;
    private final List<ReqEndPointEnum> reqEndPointEnums;

    EndPointGroupEnum(String code, List<ReqEndPointEnum> reqEndPointEnums) {
        this.code = code;
        this.reqEndPointEnums = reqEndPointEnums;
    }

    public static EndPointGroupEnum getEndPointGroup(ReqEndPointEnum reqEndPoint) {
        for (EndPointGroupEnum endPointGroup : EndPointGroupEnum.values()) {
            if (endPointGroup.getReqEndPointEnums().contains(reqEndPoint)) {
                return endPointGroup;
            }
        }
        return null;
    }

    public static EndPointGroupEnum getEndPointGroup(String reqEndPoint) {
        for (EndPointGroupEnum endPointGroup : EndPointGroupEnum.values()) {
            if (endPointGroup.getReqEndPointEnums().stream().anyMatch(item -> item.equals(reqEndPoint))) {
                return endPointGroup;
            }
        }
        return null;
    }

}
