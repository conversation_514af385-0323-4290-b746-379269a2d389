package com.creality.falcon.stat.entity.report;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 系统菜单
 * <AUTHOR>
 */
@Data
public class ReportUserNum implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**主键*/
    private Long id;
    /**创建时间*/
    private Date createTime;
    /**数据编码*/
    private String reportCode;
    /**来源类型*/
    private String fromType;
    /**用户id*/
    private String tokenObj;
    /**用胡类型0未登录1登录*/
    private Integer tokenType;
    /**1登录或访客 0停留10分钟以上*/
    private Integer reportType;
    /**用户ip地址*/
    private String userIp;
    /**用户IP所属国家*/
    private String userAddress;
    
}
