package com.creality.falcon.stat.domain.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.buffer.BufferReportDataUpload;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportUserNumUpByClientIdDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;
import com.creality.falcon.stat.utils.IdUtil;
/**
 * 独立用户数
 */
@Component
public class ReportHandleUserNum extends AbsReportHandle {
	@Autowired
	private IReportUserNumService reportUserNumService;
	@Autowired
	private BufferReportDataUpload bufferReportDataUpload;
	@Autowired
	private IdUtil idUtil;
	
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto dto) {
		ReportUserNum report=new ReportUserNum();
		report.setId(idUtil.createId());
		report.setReportCode(this.getReportCode().getCode());
		report.setCreateTime(new Date());
		report.setTokenObj(getDataToken(dto));
		report.setTokenType(getTokenType(dto));
		report.setFromType(dto.getFromType());
		report.setReportType(1);
		report.setUserIp(dto.getIpAds());
		
		ReportUserNumUpByClientIdDto data=new ReportUserNumUpByClientIdDto();
		data.setReportUserNum(report);
		data.setClientId(dto.getClientId());
		bufferReportDataUpload.addReportData(data);
		
		return BaseResponse.success();
	}
	
	
	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_user_num;
	}
	/**数据校验*/
	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		if(data==null) {
			return false;
		}
		return true;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		data.setReportCode(this.getReportCode().getCode());
		//独立用户数据改为登录+未登录+活跃用户
		List<String> codeList=new ArrayList<String>();
		codeList.add(EnumDataReportCode.code_user_num.getCode());
		codeList.add(EnumDataReportCode.code_alive_user_num.getCode());
		//去掉默认的统计code-否侧查询不准确
		data.setReportCode(null);
		
		data.setReportCodeList(codeList);
		Integer num=reportUserNumService.reportUserNum(data);
		vo.setReportNum(num.toString());
		return BaseResponse.success(vo);
	}
	
	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null;
	}
	
}
