package com.creality.falcon.stat.domain.point.result;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.point.dto.PointBtnClickSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointLoginDialogSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.model.ModelReportScore;
import com.creality.falcon.stat.entity.point.vo.PointDataReportButtonClickItem;
import com.creality.falcon.stat.entity.point.vo.PointDataReportButtonClickResult;
import com.creality.falcon.stat.entity.point.vo.PointDataReportButtonClickVo;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.enums.point.EnumButton;
import com.creality.falcon.stat.enums.point.EnumEventFromType;
import com.creality.falcon.stat.enums.point.EnumPointCode;
import com.creality.falcon.stat.servicemvc.IPointButtonClickNumService;
import com.creality.falcon.stat.servicemvc.IPointPageViewNumService;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

/**
 * craft首页跳转按钮统计结果查询
 */
@Component
public class PointDataReportButtonClick implements IPointDataReport{
	
	@Autowired
	private IPointButtonClickNumService pointButtonClickNumService;
	@Autowired
	private IReportUserNumService reportUserNumService;
	@Autowired
	private IPointPageViewNumService pointPageViewNumService;

	@Override
	public boolean inputParamIsOk(PointReportResultSearchDto searchDto) {
		return StrUtil.isNotEmpty(searchDto.getFromType());
	}

	@Override
	public IPointReportResult doReport(PointReportResultSearchDto dto) {
		PointDataReportButtonClickResult vo=new PointDataReportButtonClickResult();
		
		List<EnumButton> buttonList=EnumButton.getPointHomePageButtonList();
		
		PointBtnClickSearchDto searchDto=new PointBtnClickSearchDto();
		BeanUtils.copyProperties(dto, searchDto);
		searchDto.setBtnCodeList(buttonList.stream().map(EnumButton::getBtnCode).collect(Collectors.toList()));
		List<ModelReportScore> score=pointButtonClickNumService.reportItemSum(searchDto);
		
		if(CollectionUtil.isEmpty(score)) {
			return vo;
		}else {
			//点击数
			Map<String, ModelReportScore> dataMap=score.stream().filter(m->{
				return m.getType().equals(1);
			}).collect(Collectors.toMap(ModelReportScore::getKeyName, m->m,(m1,m2)->m1));
			//用户数
			Map<String, ModelReportScore> userMap=score.stream().filter(m->{
				return m.getType().equals(0);
			}).collect(Collectors.toMap(ModelReportScore::getKeyName, m->m,(m1,m2)->m1));
			
			//拿到活跃用户数 活跃用户=登录用户+活跃用户（10分钟未登录用户）
			ReportWebSearchDto searchUser=new ReportWebSearchDto();
			searchUser.setStartDate(dto.getStartDate());
			searchUser.setEndDate(dto.getEndDate());
			searchUser.setFromType(dto.getFromType());
			Integer aliveUserNum=reportUserNumService.reportUserNumDistinct(searchUser);
			
			//拿到浏览page总量
			PointLoginDialogSearchDto searchPage=new PointLoginDialogSearchDto();
			searchPage.setPointFromCode(EnumEventFromType.type_button_show.getType());
			searchPage.setStartDate(dto.getStartDate());
			searchPage.setEndDate(dto.getEndDate());
			searchPage.setFromType(dto.getFromType());
			Integer countPageViewNumAll=pointPageViewNumService.searchCountBySearchDto(searchPage);
			
			List<PointDataReportButtonClickVo> resultList=new ArrayList<PointDataReportButtonClickVo>();
			for(EnumButton btn:buttonList) {
				//点击次数
				ModelReportScore data=dataMap.get(btn.getBtnCode());
				ModelReportScore userNum=userMap.get(btn.getBtnCode());
				//有点击数
				if(data!=null) {
					PointDataReportButtonClickVo item=new PointDataReportButtonClickVo();
					
					Integer clickNum=data.getNumAll();
					item.setClickNum(data.getNumAll().toString());
					item.setClickLv(data.getNumAll()==0||countPageViewNumAll<=0?"0%":new BigDecimal(100.0 * clickNum /countPageViewNumAll).setScale(2, RoundingMode.DOWN).toString()+"%");
					if(userNum!=null) {
						item.setPenetrateLv(userNum.getNumAll()==0||aliveUserNum<=0?"0%":new BigDecimal(100.0 * userNum.getNumAll() /aliveUserNum).setScale(2, RoundingMode.DOWN).toString()+"%");
					}else {
						item.setPenetrateLv("0%");
					}
					item.setFromTypeName(btn.getBtnName());
					
					List<PointDataReportButtonClickItem> itemList=new ArrayList<PointDataReportButtonClickItem>();
					PointDataReportButtonClickItem it=new PointDataReportButtonClickItem();
					it.setClickNum(clickNum.toString());
					it.setFromType(btn.getBtnCode());
					it.setFromTypeName(btn.getBtnName());
					it.setClickLv(item.getClickLv());
					it.setPenetrateLv(item.getPenetrateLv());
					itemList.add(it);
					
					item.setItemList(itemList);
					resultList.add(item);
				//无点击数
				}else {
					PointDataReportButtonClickVo item=new PointDataReportButtonClickVo();
					item.setClickNum("0");
					item.setClickLv("0%");
					item.setPenetrateLv("0%");
					item.setFromTypeName(btn.getBtnName());
					
					List<PointDataReportButtonClickItem> itemList=new ArrayList<PointDataReportButtonClickItem>();
					PointDataReportButtonClickItem it=new PointDataReportButtonClickItem();
					it.setClickNum("0");
					it.setFromType(btn.getBtnCode());
					it.setFromTypeName(btn.getBtnName());
					it.setClickLv(item.getClickLv());
					it.setPenetrateLv(item.getPenetrateLv());
					itemList.add(it);
					
					item.setItemList(itemList);
					
					resultList.add(item);
				}
			}
			vo.setDataList(resultList);
		}
		return vo;
	}

	@Override
	public EnumPointCode getPointCode() {
		return EnumPointCode.code_craft_button_click;
	}

}
