package com.creality.falcon.stat.domain.report;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.ReportViewNum;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportViewNumService;
import com.creality.falcon.stat.utils.IdUtil;

/**
 * pc访问量
 */
@Component
public class ReportHandlePcViewNum extends AbsReportHandle{
	@Autowired
	private IReportViewNumService reportViewNumService;
	@Autowired
	private IdUtil idUtil;

	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		ReportViewNum saveData=new ReportViewNum();
		saveData.setId(idUtil.createId());
		saveData.setCreateTime(new Date());
		saveData.setReportCode(this.getReportCode().getCode());
		saveData.setFromType(data.getFromType());
		saveData.setReportNum(data.getReportNum());
		saveData.setTokenObj(getDataToken(data));
		saveData.setTokenType(getTokenType(data));
		reportViewNumService.save(saveData);
		return BaseResponse.success();
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_pc_view_num;
	}

	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null&&fromType.equals(EnumDataReportFromType.type_pc.getType());
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		if(data==null) {
			return false;
		}
		if(data!=null) {
			if(data.getReportNum()==null) {
				return false;
			}
		}
		return true;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		//pc访问量
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(this.getReportCode().getCodeName());
		vo.setFromType(data.getFromType());
		
		data.setReportCode(EnumDataReportCode.code_pc_view_num.getCode());
		Integer viewNum=reportViewNumService.reportViewNum(data);
		vo.setReportNum(viewNum.toString());
		return BaseResponse.success(vo);
	}
	
}
