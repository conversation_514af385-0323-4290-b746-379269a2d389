package com.creality.falcon.stat.domain.point;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.creality.falcon.stat.entity.point.PointPageViewNum;
import com.creality.falcon.stat.entity.point.dto.PointInputDataDto;
import com.creality.falcon.stat.entity.point.model.ModelCraftPageViewNum;
import com.creality.falcon.stat.entity.point.model.ModelPointDataWarp;
import com.creality.falcon.stat.entity.point.vo.PointOutputDataVo;
import com.creality.falcon.stat.enums.point.EnumPageFromType;
import com.creality.falcon.stat.enums.point.EnumPointCode;
import com.creality.falcon.stat.exception.ExceptionPointDataParse;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;
import com.creality.falcon.stat.servicemvc.IPointPageViewNumService;
import com.creality.falcon.stat.utils.IdUtil;

import cn.hutool.core.util.StrUtil;

/**
 * 流量来源统计
 */
@Component
public class PointDataInputHandlePageViewEmailType implements IPointDatainputHandle<ModelCraftPageViewNum,PointPageViewNum>{

	@Autowired
	private IPointPageViewNumService pointPageViewNumService;
	
	@Autowired
	private IdUtil idUtil;
	
	@Override
	public EnumPointCode getPointCode() {
		return EnumPointCode.code_craft_page_view_num_fromtype;
	}

	@Override
	public ModelPointDataWarp<ModelCraftPageViewNum> parseData(PointInputDataDto inputData)
			throws ExceptionPointDataParse {
		ModelCraftPageViewNum model=new ModelCraftPageViewNum();
		if(StrUtil.isEmpty(inputData.getUploadJson())) {
			model.setPointFromCode(EnumPageFromType.type_common.getType());
			model.setViewNum(1);
		}else {
			//解析
			model=JSONObject.parseObject(inputData.getUploadJson(),ModelCraftPageViewNum.class);
			//判断访问来源是否正确
			if(model.getPointFromCode()==null) {
				model.setPointFromCode(EnumPageFromType.type_common.getType());
			}
			if(model.getViewNum()==null) {
				model.setViewNum(1);
			}
		}
		return new ModelPointDataWarp<ModelCraftPageViewNum>(model);
	}

	@Override
	public ModelPointDataWarp<PointPageViewNum> createSaveData(PointInputDataDto dto, ModelPointDataWarp<?> inputData) {
		ModelCraftPageViewNum model=(ModelCraftPageViewNum) inputData.getData();
		if(model!=null) {
			PointPageViewNum data=new PointPageViewNum();
			data.setCreateTime(new Date());
			data.setFromType(dto.getFromType());
			data.setPointFromCode(model.getPointFromCode());
			data.setViewNum(model.getViewNum());
			data.setLoginState(dto.getUserId()!=null?1:0);
			data.setUserIpId(StrUtil.isNotEmpty(dto.getUserId())?dto.getUserId():StrUtil.isNotEmpty(dto.getClientId())?dto.getClientId():dto.getIpAddress());
			return new ModelPointDataWarp<PointPageViewNum>(data);
		}
		return null;
	}

	@Override
	public BaseResponse<PointOutputDataVo> handleData(ModelPointDataWarp<?> saveData) {
		PointPageViewNum data=(PointPageViewNum) saveData.getData();
		if(data!=null) {
			data.setId(idUtil.createId());
			pointPageViewNumService.addPointPageViewNum(data); 
			return BaseResponse.success(new PointOutputDataVo().ok());
		}
		return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
	}


}
