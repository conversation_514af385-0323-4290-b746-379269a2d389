package com.creality.falcon.stat.enums.point;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.util.StrUtil;

/**
 * 流量来源
 */
public enum EnumPageFromType {

	type_common("type_common","正常访问","正常访问来源"),
	type_from_email("type_from_email","邮件访问","从邮件连接跳转访问");

	EnumPageFromType(String type,String name,String remark){
		this.type=type;
		this.name=name;
		this.remark=remark;
	}
	
	private String type;
	
	private String name;
	
	private String remark;

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public static boolean typeIsOk(String pointFromCode) {
		if(StrUtil.isNotEmpty(pointFromCode)) {
			for(EnumPageFromType type:EnumPageFromType.values()) {
				if(type.getType().equals(pointFromCode)) {
					return true;
				}
			}
		}
		
		return false;
	}

	public static List<String> createTypeList() {
		List<String> list=new ArrayList<String>();
		for(EnumPageFromType type:EnumPageFromType.values()) {
			list.add(type.getType());
		}
		return list;
	}

	public static String getNameByType(String keyName) {
		if(StrUtil.isNotEmpty(keyName)) {
			for(EnumPageFromType type:EnumPageFromType.values()) {
				if(type.getType().equals(keyName)) {
					return type.getName();
				}
			}
		}
		return null;
	}

}
