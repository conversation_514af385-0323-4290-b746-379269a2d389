package com.creality.falcon.stat.utils;

import cn.hutool.core.util.StrUtil;
import com.creality.falcon.stat.base.BaseRequestParam;

/**
 * ParamUtil
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public class ParamUtil {

    public static boolean checkHeader(BaseRequestParam param) {
        // web没有数字版本号，所以不校验
        return StrUtil.isAllNotBlank(param.getApiReqEndPoint(), param.getApiVersion(), param.getApiClientId()) && param.getApiVersionCode() != null;
    }

}
