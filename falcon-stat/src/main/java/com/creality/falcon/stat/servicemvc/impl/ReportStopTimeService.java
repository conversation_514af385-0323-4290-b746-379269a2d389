package com.creality.falcon.stat.servicemvc.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.falcon.stat.entity.point.model.ModleKV;
import com.creality.falcon.stat.entity.report.ReportStopTime;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.mapper.ReportStopTimeMapper;
import com.creality.falcon.stat.servicemvc.IReportStopTimeService;

@Service
public class ReportStopTimeService extends ServiceImpl<ReportStopTimeMapper, ReportStopTime> implements IReportStopTimeService{

	
	@Autowired
	private ReportStopTimeMapper reportStopTimeMapper;

	@Override
	public int addReportStopTime(ReportStopTime reportStopTime) {
		return reportStopTimeMapper.addReportStopTime(reportStopTime);
	}

	@Override
	public int delReportStopTimeById(String id) {
		return reportStopTimeMapper.delReportStopTimeById(id);
	}

	@Override
	public ReportStopTime selReportStopTimeById(String id) {
		return reportStopTimeMapper.selectById(id);
	}

	@Override
	public int upReportStopTime(ReportStopTime reportStopTime) {
		return reportStopTimeMapper.upReportStopTime(reportStopTime);
	}

	@Override
	public List<ReportStopTime> selReportStopTimeList(ReportStopTime reportStopTime) {
		return reportStopTimeMapper.selReportStopTimeList(reportStopTime);
	}

	@Override
	public Integer reportUserNum(ReportWebSearchDto data) {
		return reportStopTimeMapper.reportUserNum(data);
	}

	@Override
	public Long reportStopTime(ReportWebSearchDto data) {
		return reportStopTimeMapper.reportStopTime(data);
	}

	@Override
	public Integer reportItemCount(ReportWebSearchDto data) {
		return reportStopTimeMapper.reportItemCount(data);
	}

	@Override
	public void updateUserTokenObjByClientId(String clientId, String tokenObj) {
		reportStopTimeMapper.updateUserTokenObjByClientId(clientId,tokenObj);
	}

	@Override
	public List<ModleKV> reportStopTimeGroupByTime(ReportWebSearchDto data) {
		return reportStopTimeMapper.reportStopTimeGroupByTime(data);
	}

}
