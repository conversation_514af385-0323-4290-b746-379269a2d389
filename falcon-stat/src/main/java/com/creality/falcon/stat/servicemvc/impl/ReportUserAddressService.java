package com.creality.falcon.stat.servicemvc.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.falcon.stat.entity.report.ReportUserAddress;
import com.creality.falcon.stat.mapper.ReportUserAddressMapper;
import com.creality.falcon.stat.servicemvc.IReportUserAddressService;

@Service
public class ReportUserAddressService extends ServiceImpl<ReportUserAddressMapper, ReportUserAddress> implements IReportUserAddressService{

	
	@Autowired
	private ReportUserAddressMapper reportUserAddressMapper;

	@Override
	public int addReportUserAddress(ReportUserAddress reportUserAddress) {
		return reportUserAddressMapper.addReportUserAddress(reportUserAddress);
	}

}
