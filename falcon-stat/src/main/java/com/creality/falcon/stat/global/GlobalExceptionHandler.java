package com.creality.falcon.stat.global;

import com.alibaba.fastjson.JSON;
import com.creality.falcon.stat.exception.BizException;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * GlobalExceptionHandler
 *
 * <AUTHOR>
 * @since 2020-12-05 08:11
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${spring.application.name}")
    private String applicationName;

    // 业务异常
    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public BaseResponse<?> serviceExceptionHandler(HttpServletRequest req, BizException ex) {
        logger.debug("[serviceExceptionHandler]", ex);
        return BaseResponse.error(ex.getCode(), ex.getMessage());
    }

    // Spring MVC 参数不正确
    @ResponseBody
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public BaseResponse<?> missingServletRequestParameterExceptionHandler(HttpServletRequest req, MissingServletRequestParameterException ex) {
        logger.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return BaseResponse.error(ResultEnum.PARAMETER_ERROR.getCode(), ResultEnum.PARAMETER_ERROR.getMsg() + ":" + ex.getMessage());
    }

    // 缺少请求头参数
    @ResponseBody
    @ExceptionHandler(value = MissingRequestHeaderException.class)
    public BaseResponse<?> missingRequestHeaderExceptionHandler(HttpServletRequest req, MissingRequestHeaderException ex) {
        logger.warn("[missingRequestHeaderExceptionHandler]", ex);
        return BaseResponse.error(ResultEnum.PARAMETER_ERROR.getCode(), "缺少请求头参数");
    }

    // 参数校验失败
    @ResponseBody
    @ExceptionHandler(value = InvalidFormatException.class)
    public BaseResponse<?> invalidFormatExceptionHandler(HttpServletRequest req, InvalidFormatException ex) {
        logger.warn("[invalidFormatExceptionHandler]", ex);
        return BaseResponse.error(ResultEnum.VALIDATE_ERROR.getCode(), "参数校验失败：" + ex.getOriginalMessage());
    }

    // 参数校验失败
    @ResponseBody
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public BaseResponse<?> httpMessageNotReadableExceptionHandler(HttpServletRequest req, HttpMessageNotReadableException ex) {
        logger.warn("[httpMessageNotReadableExceptionHandler]", ex);
        return BaseResponse.error(ResultEnum.VALIDATE_ERROR.getCode(), "参数校验失败：" + ex.getMessage());
    }

    // @Validated 校验
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public BaseResponse<?> methodArgumentNotValidExceptionHandler(HttpServletRequest req, MethodArgumentNotValidException ex) {
        logger.warn("[methodArgumentNotValidExceptionHandler]", ex);
        BindingResult bindingResult = ex.getBindingResult();
        // 所有参数异常信息
        List<ObjectError> allErrors = bindingResult.getAllErrors();
        return BaseResponse.error(ResultEnum.VALIDATE_ERROR.getCode(), ResultEnum.VALIDATE_ERROR.getMsg() + ":" + allErrors.get(0).getDefaultMessage());
    }

    // TODO 应该还有其它的异常，需要进行翻译
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public BaseResponse<?> exceptionHandler(HttpServletRequest req, Exception e) {
        logger.error("[exceptionHandler]", e);
        try {
            // TODO 这里可以做日志记录到：MySQL/MongoDB/ELK（等集成MongoDB后将日志存储在MongoDB（search模块））
        } catch (Throwable th) {
            logger.error("[exceptionHandler][插入访问日志({}) 发生异常({})", JSON.toJSONString(""), ExceptionUtils.getRootCauseMessage(th));
        }
        // 返回 ERROR CommonResult
        return BaseResponse.error(ResultEnum.SERVER_ERROR);
    }
}
