package com.creality.falcon.stat.enums;

/**
 * 停留时长分组
 */
public enum EnumStopTimeGroup {
	group_0_15("小于15分",0,15),
	group_15_30("15-30分",15,30),
	group_30_60("30分-1小时",30,60),
	group_60_120("1小时-2小时",60,120),
	group_120_180("2小时-3小时",120,180),
	group_180_240("3小时-4小时",180,240),
	group_240_300("4小时-5小时",240,300),
	group_300_360("5小时-6小时",300,360),
	group_360_480("6小时-8小时",360,480),
	group_480_max("大于8小时",480,Integer.MAX_VALUE);
	
	public static String getShowScore(Long value) {
		if(value!=null) {
			for(EnumStopTimeGroup s:EnumStopTimeGroup.values()) {
				if(value>=s.getMinMinute()&&value<s.getMaxMinute()) {
					return s.getShowName();
				}
			}
		}
		return null;
	}
	private String showName;
	
	private Integer minMinute;
	
	private Integer maxMinute;
	
	EnumStopTimeGroup(String showName,Integer minMinute,Integer maxMinute){
		this.showName=showName;
		this.minMinute=minMinute;
		this.maxMinute=maxMinute;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public Integer getMinMinute() {
		return minMinute;
	}

	public void setMinMinute(Integer minMinute) {
		this.minMinute = minMinute;
	}

	public Integer getMaxMinute() {
		return maxMinute;
	}

	public void setMaxMinute(Integer maxMinute) {
		this.maxMinute = maxMinute;
	}

	
}
