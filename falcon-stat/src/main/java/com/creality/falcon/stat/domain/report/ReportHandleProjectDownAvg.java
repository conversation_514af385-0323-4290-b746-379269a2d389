package com.creality.falcon.stat.domain.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportProjectNumService;

/**
 * 人均项目下载量
 */
@Component
public class ReportHandleProjectDownAvg extends AbsReportHandle {
	@Autowired
	private IReportProjectNumService reportProjectNumService;
	
	//@Autowired
	//private IReportUserNumService reportUserNumService;
	
	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_project_down_avg;
	}
	/**只做数据统计不做报表*/
	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return null;
	}
	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null;
	}
	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		//从项目下载量处获取数据
		data.setReportCode(EnumDataReportCode.code_project_down_num.getCode());
		data.setReportType(0);
		//项目下载人数
		/*ReportWebSearchDto userSearch=new ReportWebSearchDto();
		userSearch.setStartDate(data.getStartDate());
		userSearch.setEndDate(data.getEndDate());
		userSearch.setReportCode(EnumDataReportCode.code_user_num.getCode());
		userSearch.setTokenType(1);
		userSearch.setReportType(1);
		Integer userNum=reportUserNumService.reportUserNum(userSearch);*/
		
		Integer userNum=reportProjectNumService.selDistinctUserNum(data);
		if(userNum<=0) {
			vo.setReportNum("0");
		}else {
			//项目下载量
			Integer num=reportProjectNumService.reportProjectNum(data);
			Integer avgDownNum=num/userNum;
			vo.setReportNum(avgDownNum.toString());
		}
		return BaseResponse.success(vo);
	}

}
