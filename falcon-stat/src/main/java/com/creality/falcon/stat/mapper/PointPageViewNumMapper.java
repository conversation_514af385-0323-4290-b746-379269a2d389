package com.creality.falcon.stat.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.creality.falcon.stat.entity.point.PointPageViewNum;
import com.creality.falcon.stat.entity.point.dto.PointPageViewSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.vo.PointReprotKvVo;

@Mapper
public interface PointPageViewNumMapper extends BaseMapper<PointPageViewNum> {

	int addPointPageViewNum(PointPageViewNum pointPageViewNum);

	Integer searchCountBySearchDto(PointReportResultSearchDto dto);

	List<PointReprotKvVo> reportPageViewFromType(PointPageViewSearchDto dto);

}
