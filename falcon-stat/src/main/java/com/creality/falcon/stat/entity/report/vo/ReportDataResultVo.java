package com.creality.falcon.stat.entity.report.vo;

import java.util.List;

public class ReportDataResultVo {
	//编码
	private String code;
	//名称
	private String name;
	//数量
	private String reportNum;
	//来源类型
	private String fromType;
	/**下载量等明细*/
	private List<ReportDataResultItemVo> itemList;
	/**停留时长明细*/
	private ReportStopTimeDetail stopTimeDetail;
	/**占比统计*/
	private List<ReportKvModel> kvModelList;
	
	public String getFromType() {
		return fromType;
	}
	public void setFromType(String fromType) {
		this.fromType = fromType;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getReportNum() {
		return reportNum;
	}
	public void setReportNum(String reportNum) {
		this.reportNum = reportNum;
	}
	public List<ReportDataResultItemVo> getItemList() {
		return itemList;
	}
	public void setItemList(List<ReportDataResultItemVo> itemList) {
		this.itemList = itemList;
	}
	public ReportStopTimeDetail getStopTimeDetail() {
		return stopTimeDetail;
	}
	public void setStopTimeDetail(ReportStopTimeDetail stopTimeDetail) {
		this.stopTimeDetail = stopTimeDetail;
	}
	public List<ReportKvModel> getKvModelList() {
		return kvModelList;
	}
	public void setKvModelList(List<ReportKvModel> kvModelList) {
		this.kvModelList = kvModelList;
	}
	
}
