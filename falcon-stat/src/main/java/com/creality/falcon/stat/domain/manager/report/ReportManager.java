package com.creality.falcon.stat.domain.manager.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.domain.report.IReportHandle;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.interceptor.LocalSession;
import com.creality.falcon.stat.interceptor.SessionUser;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;
import com.creality.falcon.stat.utils.IpUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据上报处理员
 */

@Slf4j
@Component
public class ReportManager {
	
	@Autowired
	private List<IReportHandle> handleList;
	
	private final String client_id_name="clientId";
	/**
	 * 用于控制看板统计结果
	 */
	private static Map<String,List<String>> codeMap=new HashMap<String,List<String>>();
	static {
		//web端口
		List<String> codeWeb=new ArrayList<String>();
		codeWeb.add(EnumDataReportCode.code_web_view_num.getCode());
		codeWeb.add(EnumDataReportCode.code_user_num.getCode());
		codeWeb.add(EnumDataReportCode.code_alive_user_num.getCode());
		codeWeb.add(EnumDataReportCode.code_user_retention_rate.getCode());
		codeWeb.add(EnumDataReportCode.code_project_num.getCode());
		codeWeb.add(EnumDataReportCode.code_project_down_num.getCode());
		codeWeb.add(EnumDataReportCode.code_project_down_avg.getCode());
		codeWeb.add(EnumDataReportCode.code_stop_time.getCode());
		codeWeb.add(EnumDataReportCode.code_view_page_num.getCode());
		codeMap.put(EnumDataReportFromType.type_web.getType(), codeWeb);
		
		//pc端
		List<String> codePc=new ArrayList<String>();
		codePc.add(EnumDataReportCode.code_pc_start_times.getCode());
		codePc.add(EnumDataReportCode.code_user_num.getCode());
		codePc.add(EnumDataReportCode.code_soft_down_num.getCode());
		codePc.add(EnumDataReportCode.code_pc_running_time.getCode());
		
		codePc.add(EnumDataReportCode.code_pc_view_num.getCode());
		codePc.add(EnumDataReportCode.code_user_retention_rate.getCode());
		codePc.add(EnumDataReportCode.code_alive_user_num.getCode());
		codePc.add(EnumDataReportCode.code_project_down_avg.getCode());
		codePc.add(EnumDataReportCode.code_pc_view_num_avg.getCode());
		codeMap.put(EnumDataReportFromType.type_pc.getType(), codePc);
		
		//app端
		List<String> codeApp=new ArrayList<String>();
		codeApp.add(EnumDataReportCode.code_app_down_num.getCode());
		
		codeApp.add(EnumDataReportCode.code_soft_start_times.getCode());
		codeApp.add(EnumDataReportCode.code_user_num.getCode());
		codeApp.add(EnumDataReportCode.code_stop_time.getCode());
		codeApp.add(EnumDataReportCode.code_user_retention_rate.getCode());
		codeApp.add(EnumDataReportCode.code_web_view_num.getCode());
		codeApp.add(EnumDataReportCode.code_alive_user_num.getCode());
		codeApp.add(EnumDataReportCode.code_project_down_avg.getCode());
		codeApp.add(EnumDataReportCode.code_view_page_num.getCode());
		
		codeApp.add(EnumDataReportCode.code_cloud_space_used.getCode());
		
		codeMap.put(EnumDataReportFromType.type_app.getType(), codeApp);
	}
	
	/**
	 * 处理上报数据看报上报数据
	 * @param data
	 * @return
	 */
	public BaseResponse<ReportDataBackVo> uploadData(ReportWebUploadDto data,HttpServletRequest req){
		data.setIpAds(IpUtils.getClientIp(req));
		data.setCreateDate(new Date());
		setClientIdToUploadData(data,req);
		//如果是登录用户则设置用户id
		SessionUser user=LocalSession.getCurrentUser();
		if(user!=null) {
			data.setUserToken(user.getUserId());
		}
		//若没有用户特征信息则返回
		if(data.getUserToken()==null&&data.getIpAds()==null) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		//校验数据来源类型是否正确
		if(!EnumDataReportFromType.fromTypeIsOk(data.getFromType())) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		//处理保存数据
		for(IReportHandle handle:handleList) {
			if(handle.getReportCode().getCode().equals(data.getReportCode())) {
				//检测数据输入是否正确
				if(!handle.inputDataIsOk(data)) {
					return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
				}
				try {
					return handle.handleUploadData(data);
				} catch (Exception e) {
					log.error("看板数据上报报错:{}",e.getMessage());
					e.printStackTrace();
					return BaseResponse.success(new ReportDataBackVo("数据上报出错请检查"));
				}
			}
		}
		return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
	}
	
	/**设置用户标识值*/
	private void setClientIdToUploadData(ReportWebUploadDto data,HttpServletRequest req) {
		if(data!=null&&req!=null) {
			if(StrUtil.isEmpty(data.getClientId())) {
				String clientId=req.getHeader(client_id_name);
				if(StrUtil.isNotEmpty(clientId)) {
					data.setClientId(clientId);
				}
			}
		}
	}

	public BaseResponse<List<ReportDataResultVo>> reportData(ReportWebSearchDto data) {
		//返回值
		List<ReportDataResultVo> resultData=new ArrayList<ReportDataResultVo>();
		
		List<String> codes=data.getReportCodes();
		if(CollectionUtil.isEmpty(codes)) {
			codes=codeMap.get(data.getFromType());
		}
		if(data.getFromType()!=null&&CollectionUtil.isNotEmpty(codes)) {
			for(String code:codes) {
				for(IReportHandle handle:handleList) {
					EnumDataReportCode reportCode=handle.getReportCode();
					if(reportCode==null) {
						continue;
					}
					if(code.equals(handle.getReportCode().getCode())) {
						if(handle.matchFromType(data.getFromType())) {
							ReportWebSearchDto dto=new ReportWebSearchDto();
							BeanUtil.copyProperties(data, dto);
							try {
								BaseResponse<ReportDataResultVo> result=handle.doReport(dto);
								ReportDataResultVo backData=result.getData();
								resultData.add(backData);
							} catch (Exception e) {
								log.error("统计report数据异常：{}",e.getMessage());
							}
						}
					}
				}
			}
			return BaseResponse.success(resultData);
		}
		return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
	}
}
