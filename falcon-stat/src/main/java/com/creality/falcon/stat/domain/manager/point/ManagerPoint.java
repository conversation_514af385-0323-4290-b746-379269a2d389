package com.creality.falcon.stat.domain.manager.point;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.domain.point.IPointDatainputHandle;
import com.creality.falcon.stat.domain.point.bar.IPointBarResult;
import com.creality.falcon.stat.domain.point.result.IPointDataReport;
import com.creality.falcon.stat.domain.point.result.IPointReportResult;
import com.creality.falcon.stat.entity.point.dto.PointBarResultSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointInputDataDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.model.ModelPointDataWarp;
import com.creality.falcon.stat.entity.point.vo.PointBarResultVo;
import com.creality.falcon.stat.entity.point.vo.PointOutputDataVo;
import com.creality.falcon.stat.enums.point.EnumPointCode;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.response.ResultEnum;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
/**
 * 指挥调度层
 */
@Slf4j
@Component
public class ManagerPoint {
	
	private Logger logger=LoggerFactory.getLogger(ManagerPoint.class);
	
	@Autowired
	private List<IPointDatainputHandle<?,?>> handleList;
	@Autowired
	private List<IPointDataReport> reportList;
	@Autowired
	private List<IPointBarResult> pointBarResultList;

	/**
	 * 处理埋点上报数据
	 * @param data
	 * @return
	 */
	public BaseResponse<PointOutputDataVo> handleUploadData(PointInputDataDto data){
		if(data==null||data.getUploadJson()==null) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		for(IPointDatainputHandle<?,?> h:handleList) {
			EnumPointCode code=h.getPointCode();
			if(code==null) {
				continue;
			}
			if(code.getCode().equals(data.getPointCode())) {
				try {
					//解析数据
					ModelPointDataWarp<?> parsedData = h.parseData(data);
					if((parsedData!=null&&parsedData.getData()!=null) || (parsedData != null && CollectionUtil.isNotEmpty(parsedData.getDataList()))) {
						//创建持久化数据
						ModelPointDataWarp<?> finalData=h.createSaveData(data, parsedData);
						if((finalData!=null&&finalData.getData()!=null) || (finalData != null && CollectionUtil.isNotEmpty(finalData.getDataList()))) {
							//处理数据
							return h.handleData(finalData);
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
					//不影响用户体验 埋点上报出错记录日志 不返回错误状态导致弹框
					log.error("埋点数据上报异常:{}",e.getMessage());
					return BaseResponse.success(new PointOutputDataVo().error("埋点数据上报异常"));
					//return BaseResponse.error(ResultEnum.POINT_INPUT_DATA_PARSE_ERROR);
				}
			}
		}
		return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
	}

	/**
	 * 查询统计结果
	 * @param dto
	 * @return
	 */
	public BaseResponse<IPointReportResult> pointReportResult(PointReportResultSearchDto dto) {
		if(CollectionUtil.isNotEmpty(reportList)) {
			for(IPointDataReport re:reportList) {
				if(re.getPointCode()!=null&&re.getPointCode().getCode().equals(dto.getPointCode())) {
					boolean dataIsOk=re.inputParamIsOk(dto);
					if(!dataIsOk) {
						logger.info("统计{} 请求参数不对",dto.getPointCode());
						return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
					}
					try {
						IPointReportResult result=re.doReport(dto);
						if(result!=null) {
							return BaseResponse.success(result);
						}
					} catch (Exception e) {
						e.printStackTrace();
						logger.error("查询埋点统计数据出错：{}",e.getMessage());
					}
					return BaseResponse.error(ResultEnum.POINT_DATA_NOT_EXSIT);
				}
			}
		}
		return BaseResponse.success();
	}

	
	/**
	 * 查询柱状图统计数据
	 * @param dto
	 * @return
	 */
	public BaseResponse<List<PointBarResultVo>> pointReportBar(PointBarResultSearchDto dto) {
		List<PointBarResultVo> data=new ArrayList<PointBarResultVo>();
		//需要统计的数据
		List<String> codes=dto.getPointCodes();
		if(CollectionUtil.isNotEmpty(codes)) {
			for(String code:codes) {
				for(IPointBarResult p:pointBarResultList) {
					EnumPointCode pcode= p.getPointCode();
					if(pcode!=null&&pcode.getCode().equals(code)) {
						List<PointBarResultVo> items=p.createBarList(dto);
						if(CollectionUtil.isNotEmpty(items)) {
							data.addAll(items);
						}
					}
				}
			}
		}
		return BaseResponse.success(data);
	}
}
