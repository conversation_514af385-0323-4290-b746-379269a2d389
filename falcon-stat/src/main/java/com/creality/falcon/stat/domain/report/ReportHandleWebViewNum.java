package com.creality.falcon.stat.domain.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportViewNumService;

/**
 * web总访问量统计
 * 数据使用report_view_num表中code=code_view_page_num的数据
 */

@Component
public class ReportHandleWebViewNum extends AbsReportHandle{
	
	@Autowired
	private IReportViewNumService reportViewNumService;

	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return null;
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_web_view_num;
	}

	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null&&(fromType.equals(EnumDataReportFromType.type_web.getType())||fromType.equals(EnumDataReportFromType.type_app.getType()));
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		//计算人均停留时常
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		data.setReportCode(EnumDataReportCode.code_view_page_num.getCode());
		Integer viewNum=reportViewNumService.reportViewNum(data);
		vo.setReportNum(viewNum.toString());
		return BaseResponse.success(vo);
	}

}
