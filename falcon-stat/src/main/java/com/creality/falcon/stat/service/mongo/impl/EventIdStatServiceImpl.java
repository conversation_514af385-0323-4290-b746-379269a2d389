package com.creality.falcon.stat.service.mongo.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.creality.falcon.stat.entity.bo.EventIdStatBo;
import com.creality.falcon.stat.entity.common.EventIdStatEntity;
import com.creality.falcon.stat.service.mongo.EventIdStatService;
import com.creality.falcon.stat.service.mongo.EventReportService;
import com.mongodb.client.result.UpdateResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * EventIdStatServiceImpl
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class EventIdStatServiceImpl implements EventIdStatService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private EventReportService eventReportService;

    @Override
    public void statEventIdDataJob(String statDay) {
        log.info("开始统计事件ID数据，日期：{}", statDay);
        // 按端统计事件ID数据，需要根据三个维度分组：eventId, eventOrigin, endPointGroup
        List<EventIdStatBo> list = eventReportService.selectEventIdCountData(statDay);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 这里list不会很大
        list.forEach(item -> {
            // 创建条件对象
            Criteria criteria = Criteria.where("eventId").is(item.getEventId());
            criteria.and("statDay").is(statDay);
            criteria.and("reqEndPoint").is(item.getReqEndPoint());
            criteria.and("endPointGroup").is(item.getEndPointGroup());
            if (StrUtil.isNotBlank(item.getEventOrigin())) {
                criteria.and("eventOrigin").is(item.getEventOrigin());
            }
            // 创建查询对象，然后将条件对象添加到其中
            Query query = new Query(criteria);
            // 创建更新对象,并设置更新的内容
            Update update = new Update()
                    .setOnInsert("eventId", item.getEventId())
                    .setOnInsert("eventOrigin", StrUtil.isBlank(item.getEventOrigin()) ? "" : item.getEventOrigin())
                    .setOnInsert("statDay", statDay)
                    .setOnInsert("reqEndPoint", item.getReqEndPoint())
                    .setOnInsert("endPointGroup", item.getEndPointGroup())
                    .setOnInsert("createTime", new Date())
                    .set("statNum", item.getStatNum() == null ? 0 : item.getStatNum());
            UpdateResult result = mongoTemplate.upsert(query, update, EventIdStatEntity.class);
            log.info("更新结果：{}", "匹配到" + result.getMatchedCount() + "条数据,对第一条数据进行了更改");
        });
    }

}
