package com.creality.falcon.stat.enums;

public enum EnumDataReportCode {
	code_user_address("code_user_address",20,"用户IP归属地"),
	
	code_cloud_space_used("code_cloud_space_used",19,"云空间平均使用量"),
	
	code_soft_start_times("code_soft_start_times",18,"启动次数"),
	
	code_pc_view_num_avg("code_pc_view_num_avg",17,"人均读页数"),
	code_pc_view_num("code_pc_view_num",16,"PC访问量"),
	
	code_web_view_page_all("code_web_view_page_all",15,"访问量"),
	code_pc_start_times("code_pc_start_times",14,"PC启动次数"),
	code_web_view_num("code_web_view_num",13,"访问量"),
	
	code_user_num("code_user_num",1,"独立用户数"),
	code_project_num("code_project_num",2,"项目数量"),
	code_project_down_num("code_project_down_num",3,"项目下载量"),
	code_alive_user_num("code_alive_user_num",4,"活跃用户数"),
	code_stop_time("code_stop_time",5,"人均停留时长"),
	code_view_page_num("code_view_page_num",6,"人均读页数"),
	code_user_retention_rate("code_user_retention_rate",7,"滚动留存率"),
	code_soft_down_num("code_soft_down_num",8,"软件下载量"),
	
	//软件运行时长
	code_pc_running_time("code_pc_running_time",9,"PC人均使用时长"),
	code_app_down_num("code_app_down_num",10,"App软件下载量"),
	code_soft_view_num_all("code_soft_view_num_all",11,"软件总访问量"),
	code_project_down_avg("code_project_down_avg",12,"人均项目下载次数");
	
    private String code;
    /**index值将会使用到数据分区中*/
    private Integer index;
    /**名称*/
    private String codeName;
    
    public static Integer getIndexByCode(String co) {
    	for(EnumDataReportCode c:EnumDataReportCode.values()) {
    		if(c.getCode().equals(co)) {
    			return c.getIndex();
    		}
    	}
    	return 0;
    }
    
    public static boolean codeIsOk(String co) {
    	if(co==null) {
    		return false;
    	}
    	for(EnumDataReportCode c:EnumDataReportCode.values()) {
    		if(c.getCode().equals(co)) {
    			return true;
    		}
    	}
    	return false;
    }
    
    
    EnumDataReportCode(String code,Integer index,String codeName) {
    	this.code=code;
    	this.index=index;
    	this.codeName=codeName;
    }

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	public Integer getIndex() {
		return index;
	}


	public void setIndex(Integer index) {
		this.index = index;
	}


	public String getCodeName() {
		return codeName;
	}

	public void setCodeName(String codeName) {
		this.codeName = codeName;
	}
    
    
    
}
