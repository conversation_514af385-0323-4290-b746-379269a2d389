package com.creality.falcon.stat.exception;


import com.creality.falcon.stat.response.ResultEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BusinessException
 *
 * <AUTHOR>
 * @since 2020-06-23 11:46
 * <p>
 * 服务异常
 * <p>
 * 参考 https://www.kancloud.cn/onebase/ob/484204 文章
 * <p>
 * 一共 10 位，分成四段
 * <p>
 * 第一段，1 位，类型
 * 1 - 业务级别异常
 * 2 - 系统级别异常
 * <p>
 * 第二段，3 位，系统类型
 * 001 - 客户系统
 * 002 - 单据系统
 * <p>
 * 第三段，3 位，模块
 * 不限制规则。
 * 一般建议，每个系统里面，可能有多个模块，可以再去做分段。以crm-customer系统为例子：
 * 001 - company 模块
 * 002 - person 模块
 * 003 - follow 模块
 * <p>
 * 第四段，3 位，错误码
 * 不限制规则。
 * 一般建议，每个模块自增。
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class BizException extends RuntimeException {

    /**
     * 错误码
     */
    private final Integer code;

    public BizException(String message) {
        super(message);
        this.code = ResultEnum.SERVER_ERROR.getCode();
    }

    public BizException(ResultEnum error) {
        super(error.getMsg());
        this.code = error.getCode();
    }

    public BizException(Throwable throwable) {
        super(throwable.getMessage(), throwable);
        this.code = ResultEnum.SERVER_ERROR.getCode();
    }

    public BizException(Integer code, String errorMsg) {
        super(errorMsg);
        this.code = code;
    }

    public BizException(Integer code) {
        super("");
        this.code = code;
    }

}
