package com.creality.falcon.stat.domain.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.servicemvc.IReportViewNumService;

/**
 * PC人均读页数
 */
@Component
public class ReportHandlePcViewNumAvg extends AbsReportHandle{
	@Autowired
	private IReportViewNumService reportViewNumService;

	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		return null;
	}

	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_pc_view_num_avg;
	}

	@Override
	public boolean matchFromType(String fromType) {
		return fromType!=null&&fromType.equals(EnumDataReportFromType.type_pc.getType());
	}

	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return false;
	}

	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		
		data.setReportCode(EnumDataReportCode.code_pc_view_num.getCode());
		Integer userNum=reportViewNumService.reportUserNum(data);
		
		if(userNum<=0) {
			vo.setReportNum("0");
		}else {
			Integer num=reportViewNumService.reportViewNum(data);
			Integer score=num/userNum;
			vo.setReportNum(score.toString());
		}
		return BaseResponse.success(vo);
	}
	
}
