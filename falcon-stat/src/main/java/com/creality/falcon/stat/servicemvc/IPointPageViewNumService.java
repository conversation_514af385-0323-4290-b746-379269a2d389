package com.creality.falcon.stat.servicemvc;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.falcon.stat.entity.point.PointPageViewNum;
import com.creality.falcon.stat.entity.point.dto.PointPageViewSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.vo.PointReprotKvVo;

public interface IPointPageViewNumService  extends IService<PointPageViewNum> {

	public int addPointPageViewNum(PointPageViewNum pointPageViewNum);

	public Integer searchCountBySearchDto(PointReportResultSearchDto dto);
	/**统计流量来源*/
	public List<PointReprotKvVo> reportPageViewFromType(PointPageViewSearchDto dto);
	

}

