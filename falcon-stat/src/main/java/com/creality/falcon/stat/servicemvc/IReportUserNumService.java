package com.creality.falcon.stat.servicemvc;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.vo.ReportKvModel;

public interface IReportUserNumService  extends IService<ReportUserNum> {

	public int addReportUserNum(ReportUserNum reportUserNum);
	
	public int delReportUserNumById(String id);
	
	public ReportUserNum selReportUserNumById(String id);
	
	public int upReportUserNum(ReportUserNum reportUserNum);
	
	public List<ReportUserNum> selReportUserNumList(ReportUserNum reportUserNum);
	
	public Integer reportUserNumDistinct(ReportWebSearchDto data);

	public Integer reportUserNum(ReportWebSearchDto data);

	public List<String> searchDistinctUserIds(ReportWebSearchDto data);

	public Integer searchDistinctUserIdsNum(ReportWebSearchDto data);

	public List<String> searchDistinctUserIdHistory(ReportWebSearchDto data);

	public void updateUserTokenObjByClientId(String clientId, String tokenObj);

	public List<ReportUserNum> loadDataLimit(int start, int size);
	/**查询周期内的所有用户*/
	public List<String> searchDistinctUserIdList(ReportWebSearchDto data);

	public List<String> searchDistinctUserIdsExsit(ReportWebSearchDto data);

	public List<ReportKvModel> reportUserAddressNum(ReportWebSearchDto data);
	
}

