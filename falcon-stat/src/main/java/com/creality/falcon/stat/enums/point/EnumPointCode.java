package com.creality.falcon.stat.enums.point;

public enum EnumPointCode {

	code_soft_start("code_soft_start","软件启动"),
	code_soft_close("code_soft_close","软件关闭"),
	code_soft_crash("code_soft_crash","软件崩溃"),
	code_user_login("code_user_login","登录"),
	code_project("code_project","项目互动"),
	code_delayed_photography("code_delayed_photography","延时摄影"),
	code_cloud_space("code_cloud_space","云空间"),

	code_user_3d_to_2d("code_user_3d_to_2d","3D转2D"),
	code_processing("code_processing","加工环节"),
	code_auto_fill("code_auto_fill","智能填充"),
	code_canvas("code_canvas","画布"),

	
	code_craft_main_page("code_craft_main_page","网站首页"),
	
	code_craft_login_dialog("code_craft_login_dialog","登录弹框"),
	code_craft_project_dialog("code_craft_project_dialog","项目弹框"),
	
	code_craft_page_view_num("code_craft_page_view_num","网页访问"),
	code_craft_page_view_num_fromtype("code_craft_page_view_num_fromtype","流量来源"),
	code_craft_page_view_time("code_craft_page_view_time","网页访问时长"),
	
	code_craft_register_dialog("code_craft_register_dialog","账户注册"),
	
	code_craft_button_click("code_craft_button_click","按钮点击"),
	
	code_craft_page_view_from("code_craft_page_view_from","流量来源")
	;
	
	EnumPointCode(String code,String codeName){
		this.code=code;
		this.codeName=codeName;
	}
	
	private String code;
	
	private String codeName;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getCodeName() {
		return codeName;
	}

	public void setCodeName(String codeName) {
		this.codeName = codeName;
	}
}
