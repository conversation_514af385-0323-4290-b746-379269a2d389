package com.creality.falcon.stat.base;

import lombok.Data;

import java.io.Serializable;

/**
 * BaseRequestParam
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Data
public class BaseRequestParam implements Serializable {

    private String apiUserId;

    private String apiUserName; // 系统填充

    private String apiReqEndPoint; // 请求头，参考枚举：ReqEndPointEnum

    private String apiVersion;

    private Integer apiVersionCode;

    private String apiIp; // 系统填充

    // private String apiIpInfo; // 系统填充

    private String apiClientId; // 客户端唯一标识

    public void overrideBaseRequestParam(BaseRequestParam param) {
        if (param != null) {
            this.apiUserId = param.getApiUserId();
            this.apiUserName = param.getApiUserName();
            this.apiReqEndPoint = param.getApiReqEndPoint();
            this.apiVersion = param.getApiVersion();
            this.apiVersionCode = param.getApiVersionCode();
            this.apiIp = param.getApiIp();
            this.apiClientId = param.getApiClientId();
        }
    }

    public BaseRequestParam getBaseRequestParam() {
        BaseRequestParam baseRequestParam = new BaseRequestParam();
        baseRequestParam.setApiUserId(this.apiUserId);
        baseRequestParam.setApiUserName(this.apiUserName);
        baseRequestParam.setApiReqEndPoint(this.apiReqEndPoint);
        baseRequestParam.setApiVersion(this.apiVersion);
        baseRequestParam.setApiVersionCode(this.apiVersionCode);
        baseRequestParam.setApiIp(this.apiIp);
        baseRequestParam.setApiClientId(this.apiClientId);
        return baseRequestParam;
    }

}