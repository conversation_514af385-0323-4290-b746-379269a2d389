package com.creality.falcon.stat.domain.report;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.report.ReportViewNum;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.dto.ReportWebUploadDto;
import com.creality.falcon.stat.entity.report.vo.ReportDataBackVo;
import com.creality.falcon.stat.entity.report.vo.ReportDataResultVo;
import com.creality.falcon.stat.enums.EnumDataReportCode;
import com.creality.falcon.stat.enums.EnumDataReportFromType;
import com.creality.falcon.stat.response.BaseResponse;
import com.creality.falcon.stat.service.mongo.MongoService;
import com.creality.falcon.stat.servicemvc.IReportViewNumService;
import com.creality.falcon.stat.utils.IdUtil;

import cn.hutool.core.util.StrUtil;

/**
 * 软件启动次数
 */
@Component
public class ReportHandleSoftStartTimes extends AbsReportHandle{
	
	@Autowired
	private MongoService mongoService;

	@Autowired
	private IReportViewNumService reportViewNumService;
	@Autowired
	private IdUtil idUtil;
	
	@Override
	public BaseResponse<ReportDataBackVo> handleUploadData(ReportWebUploadDto data) {
		ReportViewNum saveData=new ReportViewNum();
		saveData.setId(idUtil.createId());
		saveData.setCreateTime(new Date());
		saveData.setReportCode(this.getReportCode().getCode());
		saveData.setFromType(data.getFromType());
		saveData.setReportNum(data.getReportNum()!=null?data.getReportNum():1);
		saveData.setTokenObj(getDataToken(data));
		saveData.setTokenType(getTokenType(data));
		reportViewNumService.save(saveData);
		return BaseResponse.success();
	}
	
	@Override
	public EnumDataReportCode getReportCode() {
		return EnumDataReportCode.code_soft_start_times;
	}
	
	@Override
	public boolean matchFromType(String fromType) {
		return StrUtil.isNotEmpty(fromType)&&(fromType.equals(EnumDataReportFromType.type_app.getType())||fromType.equals(EnumDataReportFromType.type_pc.getType()));
	}
	
	@Override
	public boolean inputDataIsOk(ReportWebUploadDto data) {
		return true;
	}
	
	@Override
	public BaseResponse<ReportDataResultVo> doReport(ReportWebSearchDto data) {
		ReportDataResultVo vo=new ReportDataResultVo();
		vo.setCode(this.getReportCode().getCode());
		vo.setName(getShowNameByType(data.getFromType()));
		vo.setFromType(data.getFromType());
		//app的启动次数调用接口获取
		if(EnumDataReportFromType.isFromApp(data.getFromType())) {
			SimpleDateFormat smp=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Long startTimes= mongoService.selectSoftStartNum(data.getStartDate()!=null?smp.format(data.getStartDate()):null, data.getEndDate()!=null?smp.format(data.getEndDate()):null);
			vo.setReportNum(startTimes!=null?startTimes.toString():"0");
		}else {
			data.setReportCode(this.getReportCode().getCode());
			Integer num=reportViewNumService.reportViewNum(data);
			vo.setReportNum(num.toString());
		}
		return BaseResponse.success(vo);
	}
}
