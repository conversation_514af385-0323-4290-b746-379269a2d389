package com.creality.falcon.stat.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.creality.falcon.stat.entity.point.model.ModleKV;
import com.creality.falcon.stat.entity.report.ReportStopTime;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;

@Mapper
public interface ReportStopTimeMapper extends BaseMapper<ReportStopTime> {

	int addReportStopTime(ReportStopTime reportStopTime);

	int delReportStopTimeById(String id);

	ReportStopTime findReportStopTimeById(String id);

	int upReportStopTime(ReportStopTime reportStopTime);

	List<ReportStopTime> selReportStopTimeList(ReportStopTime reportStopTime);

	Integer reportUserNum(ReportWebSearchDto data);

	Long reportStopTime(ReportWebSearchDto data);

	Integer reportItemCount(ReportWebSearchDto data);

	void updateUserTokenObjByClientId(@Param("clientId") String clientId,@Param("tokenObj") String tokenObj);

	List<ModleKV> reportStopTimeGroupByTime(ReportWebSearchDto data);

}
