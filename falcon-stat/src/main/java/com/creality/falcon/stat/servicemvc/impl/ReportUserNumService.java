package com.creality.falcon.stat.servicemvc.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.vo.ReportKvModel;
import com.creality.falcon.stat.mapper.ReportUserNumMapper;
import com.creality.falcon.stat.servicemvc.IReportUserNumService;

@Service
public class ReportUserNumService extends ServiceImpl<ReportUserNumMapper, ReportUserNum> implements IReportUserNumService{

	
	@Autowired
	private ReportUserNumMapper reportUserNumMapper;

	@Override
	public int addReportUserNum(ReportUserNum reportUserNum) {
		return reportUserNumMapper.addReportUserNum(reportUserNum);
	}

	@Override
	public int delReportUserNumById(String id) {
		return reportUserNumMapper.delReportUserNumById(id);
	}

	@Override
	public ReportUserNum selReportUserNumById(String id) {
		return reportUserNumMapper.selectById(id);
	}

	@Override
	public int upReportUserNum(ReportUserNum reportUserNum) {
		return reportUserNumMapper.upReportUserNum(reportUserNum);
	}

	@Override
	public List<ReportUserNum> selReportUserNumList(ReportUserNum reportUserNum) {
		return reportUserNumMapper.selReportUserNumList(reportUserNum);
	}

	@Override
	public Integer reportUserNum(ReportWebSearchDto data) {
		return reportUserNumMapper.reportUserNum(data);
	}

	@Override
	public List<String> searchDistinctUserIds(ReportWebSearchDto data) {
		return reportUserNumMapper.searchDistinctUserIds(data);
	}

	@Override
	public Integer searchDistinctUserIdsNum(ReportWebSearchDto data) {
		return reportUserNumMapper.searchDistinctUserIdsNum(data);
	}

	@Override
	public Integer reportUserNumDistinct(ReportWebSearchDto data) {
		return reportUserNumMapper.reportUserNumDistinct(data);
	}

	@Override
	public List<String> searchDistinctUserIdHistory(ReportWebSearchDto data) {
		return reportUserNumMapper.searchDistinctUserIdHistory(data);
	}

	@Override
	public void updateUserTokenObjByClientId(String clientId, String tokenObj) {
		reportUserNumMapper.updateUserTokenObjByClientId(clientId,tokenObj);
	}

	@Override
	public List<ReportUserNum> loadDataLimit(int start, int size) {
		return reportUserNumMapper.loadDataLimit(start,size);
	}

	@Override
	public List<String> searchDistinctUserIdList(ReportWebSearchDto data) {
		return reportUserNumMapper.searchDistinctUserIdList(data);
	}

	@Override
	public List<String> searchDistinctUserIdsExsit(ReportWebSearchDto data) {
		return reportUserNumMapper.searchDistinctUserIdsExsit(data);
	}

	@Override
	public List<ReportKvModel> reportUserAddressNum(ReportWebSearchDto data) {
		return reportUserNumMapper.reportUserAddressNum(data);
	}

}
