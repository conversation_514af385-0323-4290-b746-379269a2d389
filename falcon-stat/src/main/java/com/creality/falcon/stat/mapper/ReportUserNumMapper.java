package com.creality.falcon.stat.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.creality.falcon.stat.entity.report.ReportUserNum;
import com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto;
import com.creality.falcon.stat.entity.report.vo.ReportKvModel;


@Mapper
public interface ReportUserNumMapper extends BaseMapper<ReportUserNum> {

	int addReportUserNum(ReportUserNum reportUserNum);

	int delReportUserNumById(String id);

	ReportUserNum findReportUserNumById(String id);

	int upReportUserNum(ReportUserNum reportUserNum);

	List<ReportUserNum> selReportUserNumList(ReportUserNum reportUserNum);

	Integer reportUserNum(ReportWebSearchDto data);

	List<String> searchDistinctUserIds(ReportWebSearchDto data);

	Integer searchDistinctUserIdsNum(ReportWebSearchDto data);

	Integer reportUserNumDistinct(ReportWebSearchDto data);

	List<String> searchDistinctUserIdHistory(ReportWebSearchDto data);

	void updateUserTokenObjByClientId(@Param("clientId") String clientId,@Param("tokenObj") String tokenObj);

	List<ReportUserNum> loadDataLimit(@Param("start") Integer start,@Param("size") Integer size);

	List<String> searchDistinctUserIdList(ReportWebSearchDto data);

	List<String> searchDistinctUserIdsExsit(ReportWebSearchDto data);

	List<ReportKvModel> reportUserAddressNum(ReportWebSearchDto data);

}
