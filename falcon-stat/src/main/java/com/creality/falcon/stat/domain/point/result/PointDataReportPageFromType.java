package com.creality.falcon.stat.domain.point.result;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.creality.falcon.stat.entity.point.dto.PointPageViewSearchDto;
import com.creality.falcon.stat.entity.point.dto.PointReportResultSearchDto;
import com.creality.falcon.stat.entity.point.vo.PointReprotKvVo;
import com.creality.falcon.stat.entity.point.vo.PointReprotPageViewFromTypeVo;
import com.creality.falcon.stat.enums.point.EnumPageFromType;
import com.creality.falcon.stat.enums.point.EnumPointCode;
import com.creality.falcon.stat.servicemvc.IPointPageViewNumService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 网站流量来源分析
 */
@Component
public class PointDataReportPageFromType implements IPointDataReport{
	
	@Autowired
	private IPointPageViewNumService pointPageViewNumService;

	@Override
	public boolean inputParamIsOk(PointReportResultSearchDto searchDto) {
		return StrUtil.isNotEmpty(searchDto.getFromType());
	}

	@Override
	public IPointReportResult doReport(PointReportResultSearchDto dto) {
		PointReprotPageViewFromTypeVo data=new PointReprotPageViewFromTypeVo();
		//查询来源
		PointPageViewSearchDto searchDto=new PointPageViewSearchDto();
		BeanUtil.copyProperties(dto, searchDto);
		
		List<String> pageViewFromTypeList=EnumPageFromType.createTypeList();
		searchDto.setPointCodeList(pageViewFromTypeList);
		
		List<PointReprotKvVo> resultList=pointPageViewNumService.reportPageViewFromType(searchDto);
		if(CollectionUtil.isNotEmpty(resultList)) {
			resultList.stream().forEach(vo->{
				vo.setShowName(EnumPageFromType.getNameByType(vo.getKeyName()));
			});
		}
		data.setResultList(resultList);
		return data;
	}

	@Override
	public EnumPointCode getPointCode() {
		return EnumPointCode.code_craft_page_view_from;
	}

}
