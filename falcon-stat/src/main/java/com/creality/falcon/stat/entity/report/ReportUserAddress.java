package com.creality.falcon.stat.entity.report;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 系统菜单
 * <AUTHOR>
 */
@Data
public class ReportUserAddress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键id*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**时间*/
    private Date createTime;
    /**国家*/
    private String provinces;
    /**市*/
    private String city;
    /**县城*/
    private String county;
    /**乡镇*/
    private Integer town;
}
