package com.creality.falcon.stat.enums.event;

import lombok.Getter;

/**
 * EventConstant
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public class EventConstant {

    // 项目相关事件
    private final static String page0101 = "page0101";
    private final static String button0101 = "button0101";
    private final static String button0102 = "button0102";
    private final static String button0103 = "button0103";
    private final static String button0104 = "button0104";
    private final static String button0105 = "button0105";

    // 用户登录/登出相关事件
    private final static String trigger0201 = "trigger0201";
    private final static String button0202 = "button0202";
    private final static String origin0201 = "origin0201";
    private final static String origin0202 = "origin0202";
    private final static String origin0203 = "origin0203";
    private final static String origin0204 = "origin0204";
    private final static String origin0205 = "origin0205";
    private final static String origin0206 = "origin0206";
    private final static String origin0207 = "origin0207";
    private final static String origin0208 = "origin0208";
    private final static String origin0209 = "origin0209";
    private final static String origin0210 = "origin0210";

    // AI craft页 03开头
    private final static String page0301 = "page0301";
    private final static String page0302 = "page0302";

    // 3D转2D页 04开头
    private final static String page0401 = "page0401";
    private final static String page0402 = "page0402";
    private final static String button0401 = "button0401";
    private final static String button0402 = "button0402";
    private final static String button0403 = "button0403";
    private final static String button0404 = "button0404";
    private final static String button0405 = "button0405";
    private final static String button0406 = "button0406";
    private final static String button0407 = "button0407";
    private final static String button0408 = "button0408";
    private final static String button0409 = "button0409";
    private final static String origin0401 = "origin0401";

    // 软件事件
    private final static String software0501 = "software0501";
    private final static String software0502 = "software0502";

    // 设备事件
    private final static String device0601 = "device0601";

    // 页面展示
    private final static String page0701 = "page0701";
    private final static String page0702 = "page0702";

    // 画布相关
    private final static String page0801 = "page0801";
    private final static String button0801 = "button0801";
    private final static String origin0801 = "origin0801";
    private final static String origin0802 = "origin0802";
    private final static String origin0803 = "origin0803";

    // 加工相关
    private final static String button0901 = "button0901";
    private final static String button0902 = "button0902";
    private final static String button0903 = "button0903";
    private final static String button0904 = "button0904";
    private final static String button0905 = "button0905";
    private final static String button0906 = "button0906";
    private final static String button0907 = "button0907";
    private final static String button0908 = "button0908";
    private final static String button0909 = "button0909";
    private final static String button0910 = "button0910";
    private final static String button0911 = "button0911";
    private final static String button0912 = "button0912";
    private final static String button0913 = "button0913";
    private final static String button0914 = "button0914";
    private final static String button0915 = "button0915";
    private final static String button0916 = "button0916";
    private final static String button0917 = "button0917";
    private final static String button0918 = "button0918";
    private final static String origin0901 = "origin0901";
    private final static String origin0902 = "origin0902";
    private final static String origin0903 = "origin0903";

    // AI滤镜
    private final static String page1001 = "page1001";
    private final static String page1002 = "page1002";
    private final static String button1001 = "button1001";

    @Getter
    public enum EventId {
        // 事件ID，event=唯一标识，fieldName=字段名，desc=描述

        // 项目相关 01开头
        pageShowProjectDetail(page0101, true, "详情页展示"),

        buttonClickProjectDetailLike(button0101, true, "点赞"),
        buttonClickProjectDetailComment(button0102, true, "评论"),
        buttonClickProjectDetailShare(button0103, true, "分享"),
        buttonClickProjectDetailDownload(button0104, true, "下载"),
        buttonClickProjectDetailMake(button0105, true, "制作"),

        // 用户登录相关事件 02开头
        triggerUserLogin(trigger0201, false, "用户登录"),
//        buttonClickUserLogout(button0202, false, "用户登出"),

        // AI craft页 03开头
        pageShowAiCraft(page0301, false, "AICraft页展示"),
        pageStayAiCraft(page0302, false, "AICraft页停留时间"),

        // 3D转2D页 04开头
        pageShow3DTo2D(page0401, false, "3D转2D页展示"),
        pageStay3DTo2D(page0402, false, "3D转2D页页停留时间"),
        buttonClick3DTo2D(button0401, false, "3D转2D点击"), // 这里需要来源（AiCraft）
        buttonClickImport3DModel(button0402, true, "导入3D模型"),
        buttonClickDiscover3DModel(button0403, true, "发现3D模型"),
        buttonClickStacked(button0404, true, "堆叠模式"),
        buttonClickCrossSlot(button0405, true, "十字模式"),
        buttonClickAngled(button0406, true, "斜拼模式"),
        buttonClickVertical(button0407, true, "垂直模式"),
        buttonClickGenerateSlice(button0408, true, "生成切片"),
        buttonClickDownloadSVGResources(button0409, true, "下载SVG资源"),
        buttonClickImportCanvas(button0409, true, "导入画布"),


        // 软件事件 05开头
//        softwareStart(software0501, false, "软件启动"),
//        softwareClose(software0502, false, "软件关闭"),

        // 设备事件
//        deviceConnectClick(device0601, false, "设备连接"),

        // 页面展示事件
        pageShowHome(page0701, true, "首页展示"),
        pageShowProject(page0702, true, "项目页展示"),

        // 画布相关 08开头
        pageShowCanvas(page0801, true, "画布页展示"),
        buttonClickCanvasMakeIt(button0801, true, "画布制作点击"),

        // 加工 09开头
        buttonClickProcessing(button0901, true, "加工点击"),
        buttonClickPlaneProcessingStandardPlate(button0902, true, "平面加工-标准板"),
        buttonClickPlaneProcessingHoneycombPlate(button0903, true, "平面加工-蜂巢板"),
        buttonClickRotaryProcessingRollerKit(button0904, true, "旋转加工-滚轮套件"),
        buttonClickRotaryProcessingChuckKit(button0905, true, "旋转加工卡盘套件"),
        buttonClickProcessingTypeLineEngraving(button0906, false, "加工类型/雕刻图层-线条雕刻"),
        buttonClickProcessingTypeFillEngraving(button0907, false, "加工类型/雕刻图层-填充雕刻"),
        buttonClickProcessingTypeLineCutting(button0908, false, "加工类型/雕刻图层-线条切割"),
        buttonClickProcessingTypeLineEngravingAndFillEngraving(button0909, false, "加工类型/雕刻图层-线条雕刻和填充雕刻"),
        buttonClickProcessingTypeLineEngravingAndLineCutting(button0910, false, "加工类型/雕刻图层-线条雕刻和线条切割"),
        buttonClickProcessingTypeFillEngravingAndLineCutting(button0911, false, "加工类型/雕刻图层-填充雕刻和线条切割"),
        buttonClickProcessingTypeAll(button0912, false, "加工类型/雕刻图层-全都有"),
        buttonClickProcessingMaterial(button0913, false, "加工材料"),
        buttonClickTrace(button0914, true, "轮廓追踪"),
        buttonClickSmartFill(button0915, true, "智能填充"),
        buttonClickMaterialTestArray(button0916, true, "材料测试阵列"),
        buttonClickOnlineVideo(button0917, true, "在线监控"),
        buttonClickDelayPhotography(button0918, true, "延时摄影"),

        // AI滤镜 10开头
        pageShowAiLens(page1001, false, "AI滤镜页展示"),
        pageStayAiLens(page1002, false, "AI滤镜页停留时间"),
        buttonClickAiLens(button1001, false, "AI滤镜点击"),

        ;

        private final String fieldName;
        private final boolean fieldStat; // 是否需要统计用户行为
        private final String desc;

        EventId(String fieldName, boolean fieldStat, String desc) {
            this.fieldName = fieldName;
            this.fieldStat = fieldStat;
            this.desc = desc;
        }
    }

    @Getter
    public enum EventOrigin {
        //  用户登录相关事件 编号对应事件开头
        triggerUserLoginByClickLogin(origin0201, true, EventId.triggerUserLogin, "登录触发"),
        triggerUserLoginByClickLike(origin0202, true, EventId.triggerUserLogin, "点赞触发"),
        triggerUserLoginByClickComment(origin0203, true, EventId.triggerUserLogin, "评论触发"),
        triggerUserLoginByClickDownload(origin0204, true, EventId.triggerUserLogin, "下载触发"),
        triggerUserLoginByClickMake(origin0205, true, EventId.triggerUserLogin, "制作触发"),
        triggerUserLoginByClickUpload(origin0206, true, EventId.triggerUserLogin, "上传触发"),
        triggerUserLoginByClickImages(origin0207, true, EventId.triggerUserLogin, "我的图像库触发"),
        triggerUserLoginByClickSmartFill(origin0208, true, EventId.triggerUserLogin, "智能填充触发"),
        triggerUserLoginByClick3DTo2D(origin0209, true, EventId.triggerUserLogin, "3D转2D触发"),
        triggerUserLoginByClickAddMaterial(origin0210, true, EventId.triggerUserLogin, "新增自定义材料触发"),

        // 3D转2D来源
        buttonClick3DTo2DByAiCraft(origin0401, false, EventId.buttonClick3DTo2D, "AiCraft页"),
        buttonClick3DTo2DByCanvas(origin0401, false, EventId.buttonClick3DTo2D, "画布页"),

        pageShowProjectDetailByHome(origin0201, false, EventId.pageShowProjectDetail, "首页"),
        pageShowProjectDetailByProject(origin0201, false, EventId.pageShowProjectDetail, "项目页"),

        // 画布
        pageShowCanvasByNewProject(origin0801, false, EventId.pageShowCanvas, "新建项目"),
        pageShowCanvasByOpenProject(origin0802, false, EventId.pageShowCanvas, "打开项目"),
        pageShowCanvasByClickWorkSpace(origin0803, false, EventId.pageShowCanvas, "点击工作台"),

        // 加工
        buttonClickProcessingByOriginProject(origin0901, false, EventId.buttonClickProcessing, "案例库项目"),
        buttonClickProcessingByOriginNewNotImport(origin0902, false, EventId.buttonClickProcessing, "新建无导入"),
        buttonClickProcessingByOriginNewImport(origin0903, false, EventId.buttonClickProcessing, "新建有导入"),

        ;

        private final String fieldName;
        private final boolean fieldStat;
        private final EventId eventId;
        private final String desc;

        EventOrigin(String fieldName, boolean fieldStat, EventId eventId, String desc) {
            this.fieldName = fieldName;
            this.fieldStat = fieldStat; // 是否需要统计用户行为
            this.eventId = eventId;
            this.desc = desc;
        }
    }

    @Getter
    public enum BizType {
        project("project", "项目"),
        material("material", "材料"),
        ;

        private final String code;
        private final String desc;

        BizType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
