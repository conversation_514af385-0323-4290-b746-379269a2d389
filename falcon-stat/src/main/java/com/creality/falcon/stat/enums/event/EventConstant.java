package com.creality.falcon.stat.enums.event;

import com.creality.falcon.stat.service.mongo.EventIdUserBehaviorStatService;
import com.creality.falcon.stat.service.mongo.EventStatService;
import lombok.Getter;

import java.util.List;

/**
 * EventConstant
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public class EventConstant {

    // 项目相关事件
    private final static String page0101 = "page0101";
    private final static String button0101 = "button0101";
    private final static String button0102 = "button0102";
    private final static String button0103 = "button0103";
    private final static String button0104 = "button0104";
    private final static String button0105 = "button0105";

    // 用户登录/登出相关事件
    private final static String trigger0201 = "trigger0201";
    private final static String status0202 = "status0202";
    private final static String button0202 = "button0202";
    private final static String origin0201 = "origin0201";
    private final static String origin0202 = "origin0202";
    private final static String origin0203 = "origin0203";
    private final static String origin0204 = "origin0204";
    private final static String origin0205 = "origin0205";
    private final static String origin0206 = "origin0206";
    private final static String origin0207 = "origin0207";
    private final static String origin0208 = "origin0208";
    private final static String origin0209 = "origin0209";
    private final static String origin0210 = "origin0210";
    private final static String origin0211 = "origin0211";

    // AI craft页 03开头
    private final static String page0301 = "page0301";
    private final static String page0302 = "page0302";

    // 3D转2D页 04开头
    private final static String page0401 = "page0401";
    private final static String page0402 = "page0402";
    private final static String button0401 = "button0401";
    private final static String button0402 = "button0402";
    private final static String button0403 = "button0403";
    private final static String button0404 = "button0404";
    private final static String button0405 = "button0405";
    private final static String button0406 = "button0406";
    private final static String button0407 = "button0407";
    private final static String button0408 = "button0408";
    private final static String button0409 = "button0409";
    private final static String origin0401 = "origin0401";

    // 软件事件
    private final static String software0501 = "software0501";
    private final static String software0502 = "software0502";

    // 设备事件
    private final static String device0601 = "device0601";

    // 页面展示
    private final static String page0701 = "page0701";
    private final static String page0702 = "page0702";

    // 画布相关
    private final static String page0801 = "page0801";
    private final static String button0801 = "button0801";
    private final static String origin0801 = "origin0801";
    private final static String origin0802 = "origin0802";
    private final static String origin0803 = "origin0803";

    // 加工相关
    private final static String button0901 = "button0901";
    private final static String button0902 = "button0902";
    private final static String button0903 = "button0903";
    private final static String button0904 = "button0904";
    private final static String button0905 = "button0905";
    private final static String button0906 = "button0906";
    private final static String button0907 = "button0907";
    private final static String button0908 = "button0908";
    private final static String button0909 = "button0909";
    private final static String button0910 = "button0910";
    private final static String button0911 = "button0911";
    private final static String button0912 = "button0912";
    private final static String button0913 = "button0913";
    private final static String button0914 = "button0914";
    private final static String button0915 = "button0915";
    private final static String button0916 = "button0916";
    private final static String button0917 = "button0917";
    private final static String button0918 = "button0918";
    private final static String button0919 = "button0919";
    private final static String origin0901 = "origin0901";
    private final static String origin0902 = "origin0902";
    private final static String origin0903 = "origin0903";

    // AI滤镜
    private final static String page1001 = "page1001";
    private final static String page1002 = "page1002";
    private final static String button1001 = "button1001";
    private final static String button1002 = "button1002";
    private final static String button1003 = "button1003";
    private final static String button1004 = "button1004";
    private final static String button1005 = "button1005";
    private final static String button1006 = "button1006";

    @Getter
    public enum EventId {
        // 事件ID，event=唯一标识，fieldName=字段名，desc=描述

        // 项目相关 01开头
        pageShowProjectDetail(page0101, List.of(EventIdUserBehaviorStatService.class), "详情页展示"),

        buttonClickProjectDetailLike(button0101, List.of(EventIdUserBehaviorStatService.class), "点赞"),
        buttonClickProjectDetailComment(button0102, List.of(EventIdUserBehaviorStatService.class), "评论"),
        buttonClickProjectDetailShare(button0103, List.of(EventIdUserBehaviorStatService.class), "分享"),
        buttonClickProjectDetailDownload(button0104, List.of(EventIdUserBehaviorStatService.class), "下载"),
        buttonClickProjectDetailMake(button0105, List.of(EventIdUserBehaviorStatService.class), "制作"),

        // 用户登录相关事件 02开头
        triggerUserLogin(trigger0201, List.of(EventIdUserBehaviorStatService.class), "用户登录"),
        userLoginSuccess(status0202, List.of(EventIdUserBehaviorStatService.class), "用户登录成功"),
//        buttonClickUserLogout(button0202, false, "用户登出"),

        // AI craft页 03开头
        pageShowAiCraft(page0301, List.of(EventIdUserBehaviorStatService.class), "AICraft页展示"),
        pageStayAiCraft(page0302, null, "AICraft页停留时间"),

        // 3D转2D页 04开头
        pageShow3DTo2D(page0401, List.of(EventIdUserBehaviorStatService.class), "3D转2D页展示"),
        pageStay3DTo2D(page0402, null, "3D转2D页页停留时间"),
        buttonClick3DTo2D(button0401, null, "3D转2D点击"), // 这里需要来源（AiCraft）
        buttonClickImport3DModel(button0402, null, "导入3D模型"),
        buttonClickDiscover3DModel(button0403, null, "发现3D模型"),
        buttonClickStacked(button0404, List.of(EventIdUserBehaviorStatService.class), "堆叠模式"),
        buttonClickCrossSlot(button0405, List.of(EventIdUserBehaviorStatService.class), "十字模式"),
        buttonClickAngled(button0406, List.of(EventIdUserBehaviorStatService.class), "斜拼模式"),
        buttonClickVertical(button0407, List.of(EventIdUserBehaviorStatService.class), "垂直模式"),
        buttonClickGenerateSlice(button0408, List.of(EventIdUserBehaviorStatService.class), "生成切片"),
        buttonClickDownloadSVGResources(button0409, List.of(EventIdUserBehaviorStatService.class), "下载SVG资源"),
        buttonClickImportCanvas(button0409, List.of(EventIdUserBehaviorStatService.class), "导入画布"),


        // 软件事件 05开头
//        softwareStart(software0501, false, "软件启动"),
//        softwareClose(software0502, false, "软件关闭"),

        // 设备事件
//        deviceConnectClick(device0601, false, "设备连接"),

        // 页面展示事件
        pageShowHome(page0701, List.of(EventIdUserBehaviorStatService.class), "首页展示"),
        pageShowProject(page0702, List.of(EventIdUserBehaviorStatService.class), "项目页展示"),

        // 画布相关 08开头
        pageShowCanvas(page0801, List.of(EventIdUserBehaviorStatService.class), "画布页展示"),
        buttonClickCanvasMakeIt(button0801, List.of(EventIdUserBehaviorStatService.class), "画布制作点击"),

        // 加工 09开头
        buttonClickProcessing(button0901, List.of(EventIdUserBehaviorStatService.class), "加工点击"),
        buttonClickPlaneProcessingStandardPlate(button0902, null, "平面加工-标准板"),
        buttonClickPlaneProcessingHoneycombPlate(button0903, null, "平面加工-蜂巢板"),
        buttonClickRotaryProcessingRollerKit(button0904, null, "旋转加工-滚轮套件"),
        buttonClickRotaryProcessingChuckKit(button0905, null, "旋转加工卡盘套件"),
        buttonClickProcessingTypeLineEngraving(button0906, null, "加工类型/雕刻图层-线条雕刻"),
        buttonClickProcessingTypeFillEngraving(button0907, null, "加工类型/雕刻图层-填充雕刻"),
        buttonClickProcessingTypeLineCutting(button0908, null, "加工类型/雕刻图层-线条切割"),
        buttonClickProcessingTypeLineEngravingAndFillEngraving(button0909, null, "加工类型/雕刻图层-线条雕刻和填充雕刻"),
        buttonClickProcessingTypeLineEngravingAndLineCutting(button0910, null, "加工类型/雕刻图层-线条雕刻和线条切割"),
        buttonClickProcessingTypeFillEngravingAndLineCutting(button0911, null, "加工类型/雕刻图层-填充雕刻和线条切割"),
        buttonClickProcessingTypeAll(button0912, null, "加工类型/雕刻图层-全都有"),
        buttonClickProcessingMaterial(button0913, null, "加工材料"),
        buttonClickTrace(button0914, List.of(EventIdUserBehaviorStatService.class), "轮廓追踪"),
        buttonClickSmartFill(button0915, List.of(EventIdUserBehaviorStatService.class), "智能填充"),
        buttonClickMaterialTestArray(button0916, List.of(EventIdUserBehaviorStatService.class), "材料测试阵列"),
        buttonClickOnlineVideo(button0917, List.of(EventIdUserBehaviorStatService.class), "在线监控"),
        buttonClickDelayPhotography(button0918, List.of(EventIdUserBehaviorStatService.class), "延时摄影"),
        buttonClickAIImageCutout(button0919, List.of(EventIdUserBehaviorStatService.class), "AI抠图"),

        // AI滤镜 10开头
        pageShowAiLens(page1001, null, "AI滤镜页展示"),
        pageStayAiLens(page1002, null, "AI滤镜页停留时间"),
        buttonClickAiLens(button1001, null, "AI滤镜"),
        buttonClickAiLensMake(button1001, null, "AI滤镜生成"),
        buttonClickAiLensDownload(button1002, null, "AI滤镜下载"),
        buttonClickAiLensImportCanvas(button1003, null, "AI滤镜导入画布"),
        buttonClickAiLensRelief(button1004, List.of(EventIdUserBehaviorStatService.class), "浮雕"),
        buttonClickAiLensLine(button1005, List.of(EventIdUserBehaviorStatService.class), "线稿"),
        buttonClickAiLensVector(button1006, List.of(EventIdUserBehaviorStatService.class), "矢量艺术"),

        ;

        private final String fieldName;
        private final List<Class<? extends EventStatService>> clazzList; // 实时调用数据预处理
        private final String desc;

        EventId(String fieldName, List<Class<? extends EventStatService>> clazzList, String desc) {
            this.fieldName = fieldName;
            this.clazzList = clazzList;
            this.desc = desc;
        }
    }

    @Getter
    public enum EventOrigin {
        //  用户登录相关事件 编号对应事件开头
        triggerUserLoginByClickLogin(origin0201, null, EventId.triggerUserLogin, "登录触发"),
        triggerUserLoginByClickLike(origin0202, null, EventId.triggerUserLogin, "点赞触发"),
        triggerUserLoginByClickComment(origin0203, null, EventId.triggerUserLogin, "评论触发"),
        triggerUserLoginByClickDownload(origin0204, null, EventId.triggerUserLogin, "下载触发"),
        triggerUserLoginByClickMake(origin0205, null, EventId.triggerUserLogin, "制作触发"),
        triggerUserLoginByClickUpload(origin0206, null, EventId.triggerUserLogin, "上传触发"),
        triggerUserLoginByClickImages(origin0207, null, EventId.triggerUserLogin, "我的图像库触发"),
        triggerUserLoginByClickSmartFill(origin0208, null, EventId.triggerUserLogin, "智能填充触发"),
        triggerUserLoginByClick3DTo2D(origin0209, null, EventId.triggerUserLogin, "3D转2D触发"),
        triggerUserLoginByClickAddMaterial(origin0210, null, EventId.triggerUserLogin, "新增自定义材料触发"),
        triggerUserLoginByClickAiLens(origin0211, null, EventId.triggerUserLogin, "AI滤镜触发"),

        // 3D转2D来源
        buttonClick3DTo2DByAiCraft(origin0401, null, EventId.buttonClick3DTo2D, "AiCraft页"),
        buttonClick3DTo2DByCanvas(origin0401, null, EventId.buttonClick3DTo2D, "画布页"),

        pageShowProjectDetailByHome(origin0201, null, EventId.pageShowProjectDetail, "首页"),
        pageShowProjectDetailByProject(origin0201, null, EventId.pageShowProjectDetail, "项目页"),

        // 画布
        pageShowCanvasByNewProject(origin0801, null, EventId.pageShowCanvas, "新建项目"),
        pageShowCanvasByOpenProject(origin0802, null, EventId.pageShowCanvas, "打开项目"),
        pageShowCanvasByClickWorkSpace(origin0803, null, EventId.pageShowCanvas, "点击工作台"),

        // 加工
        buttonClickProcessingByOriginProject(origin0901, null, EventId.buttonClickProcessing, "案例库项目"),
        buttonClickProcessingByOriginNewNotImport(origin0902, null, EventId.buttonClickProcessing, "新建无导入"),
        buttonClickProcessingByOriginNewImport(origin0903, null, EventId.buttonClickProcessing, "新建有导入"),

        ;

        private final String fieldName;
        private final List<Class<? extends EventStatService>> clazzList; // 实时调用数据预处理
        private final EventId eventId;
        private final String desc;

        EventOrigin(String fieldName, List<Class<? extends EventStatService>> clazzList, EventId eventId, String desc) {
            this.fieldName = fieldName;
            this.clazzList = clazzList;
            this.eventId = eventId;
            this.desc = desc;
        }
    }

    @Getter
    public enum BizType {
        project("项目"),
        material("材料"),
        ;

        private final String desc;

        BizType(String desc) {
            this.desc = desc;
        }
    }

}
