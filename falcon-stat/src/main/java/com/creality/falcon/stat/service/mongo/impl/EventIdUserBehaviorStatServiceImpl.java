package com.creality.falcon.stat.service.mongo.impl;

import cn.hutool.core.util.StrUtil;
import com.creality.falcon.stat.base.BaseRequestParam;
import com.creality.falcon.stat.base.EndPointGroupEnum;
import com.creality.falcon.stat.pojo.request.EventReportReqParam;
import com.creality.falcon.stat.service.mongo.EventIdUserBehaviorStatService;
import com.mongodb.client.result.UpdateResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * EventIdUserBehaviorStatServiceImpl
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class EventIdUserBehaviorStatServiceImpl implements EventIdUserBehaviorStatService {

    @Resource
    private MongoTemplate mongoTemplate;

    private String getCollectionName() {
        // 按月维度创表，如：common_event_report_202505
        return "event_id_user_behavior_stat";
    }

    /**
     * 表核心字段：（其他的则是动态字段，参考枚举 EventConstant.EventId）
     * 1. statDay：日期
     * 2. clientId：客户端ID
     * 3. userId：用户ID
     * 4. reqEndPoint：请求端点
     * 5. endPointGroup：端点组
     * 6. createTime：创建时间
     */
    @Override
    public void saveEventStat(String statDay, BaseRequestParam param, EventReportReqParam.EventReportItem item) {
        log.info("保存用户行为数据，日期：{}", statDay);
        // 创建条件对象
        Criteria criteria = Criteria.where("clientId").is(param.getApiClientId());
        criteria.and("statDay").is(statDay);
        criteria.and("reqEndPoint").is(param.getApiReqEndPoint());
        // 创建查询对象，然后将条件对象添加到其中
        Query query = new Query(criteria);
        // 创建更新对象,并设置更新的内容
        Update update = new Update()
                .setOnInsert("statDay", statDay)
                .setOnInsert("clientId", param.getApiClientId())
                .setOnInsert("reqEndPoint", param.getApiReqEndPoint())
                .setOnInsert("endPointGroup", Objects.requireNonNull(EndPointGroupEnum.getEndPointGroup(param.getApiReqEndPoint())).name())
                .setOnInsert("createTime", new Date());
        if (StrUtil.isNotBlank(param.getApiUserName())) {
            update.set("userId", param.getApiUserId());
        }
        update.inc(item.getEventId().getFieldName(), item.getEventValue());
        UpdateResult result = mongoTemplate.upsert(query, update, getCollectionName());
        log.info("更新结果：{}", "匹配到" + result.getMatchedCount() + "条数据,对第一条数据进行了更改");
    }

}
