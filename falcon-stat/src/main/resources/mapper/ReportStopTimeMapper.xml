<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.falcon.stat.mapper.ReportStopTimeMapper">

	<resultMap type="com.creality.falcon.stat.entity.report.ReportStopTime" id="ReportStopTimeResult">
		<result property="id"    column="id"/>
		<result property="createTime"    column="create_time"/>
		<result property="reportCode"    column="report_code"/>
		<result property="fromType"    column="from_type"/>
		<result property="reportTime"    column="report_time"/>
		<result property="tokenObj"    column="token_obj"/>
		<result property="tokenType"    column="token_type"/>
	</resultMap>
	<sql id="selReportStopTime">
		select id,create_time,report_code,from_type,report_time,token_obj,token_type from report_stop_time
	</sql>
	<insert id="addReportStopTime" parameterType="com.creality.falcon.stat.entity.report.ReportStopTime">
		insert into report_stop_time 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="createTime != null">create_time,</if>
			<if test="reportCode != null">report_code,</if>
			<if test="fromType != null">from_type,</if>
			<if test="reportTime != null">report_time,</if>
			<if test="tokenObj != null">token_obj,</if>
			<if test="tokenType != null">token_type,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="reportCode != null">#{reportCode},</if>
			<if test="fromType != null">#{fromType},</if>
			<if test="reportTime != null">#{reportTime},</if>
			<if test="tokenObj != null">#{tokenObj},</if>
			<if test="tokenType != null">#{tokenType},</if>
		</trim>
	</insert>
	<update id="upReportStopTime" parameterType="com.creality.falcon.stat.entity.report.ReportStopTime">
		update report_stop_time 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="createTime != null">create_time=#{createTime},</if>
			<if test="reportCode != null">report_code=#{reportCode},</if>
			<if test="fromType != null">from_type=#{fromType},</if>
			<if test="reportTime != null">report_time=#{reportTime},</if>
			<if test="tokenObj != null">token_obj=#{tokenObj},</if>
			<if test="tokenType != null">token_type=#{tokenType},</if>
		</trim>
		where id = #{id}
	</update>
	<select id="selReportStopTimeList" parameterType="com.creality.falcon.stat.entity.report.ReportStopTime" resultMap="ReportStopTimeResult">
		<include refid="selReportStopTime"/>
		<where>
			<if test="createTime != null  and createTime != ''"> and create_time=#{createTime}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="reportTime != null  and reportTime != ''"> and report_time=#{reportTime}</if>
			<if test="tokenObj != null  and tokenObj != ''"> and token_obj=#{tokenObj}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		</where>
	</select>
	<delete id="delReportStopTimeById" parameterType="String">
		delete from report_stop_time where id = #{id}
	</delete>
	

	<select id="reportUserNum" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Integer">
		select count(distinct token_obj) num from report_stop_time
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		</where>
	</select>
	
	<select id="reportItemCount" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Integer">
		select count(distinct token_obj) num from report_stop_time
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		</where>
	</select>
	
	
	<select id="reportStopTime" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Long">
		select ifnull(sum(report_time),0) num from report_stop_time
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		</where>
	</select>
	
	<update id="updateUserTokenObjByClientId">
	update report_stop_time set token_obj=#{tokenObj},token_type=1 where create_time&gt;=DATE_SUB(now(), INTERVAL 7 DAY) and token_obj=#{clientId}
	</update>
	
	<select id="reportStopTimeGroupByTime" resultType="com.creality.falcon.stat.entity.point.model.ModleKV" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto">
		select token_obj keyName,sum(report_time) score
		from report_stop_time
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		</where>
		group by token_obj
	</select>
	
</mapper>
