<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.falcon.stat.mapper.ReportUserNumMapper">

	<resultMap type="com.creality.falcon.stat.entity.report.ReportUserNum" id="ReportUserNumResult">
		<result property="id"    column="id"/>
		<result property="createTime"    column="create_time"/>
		<result property="reportCode"    column="report_code"/>
		<result property="fromType"    column="from_type"/>
		<result property="tokenObj"    column="token_obj"/>
		<result property="tokenType"    column="token_type"/>
		<result property="reportType"    column="report_type"/>
		<result property="userIp"    column="user_ip"/>
		<result property="userAddress"    column="user_address"/>
	</resultMap>
	<sql id="selReportUserNum">
		select id,create_time,report_code,from_type,token_obj,token_type,report_type,user_ip,user_address from report_user_num
	</sql>
	<insert id="addReportUserNum" parameterType="com.creality.falcon.stat.entity.report.ReportUserNum">
		insert into report_user_num 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="createTime != null">create_time,</if>
			<if test="reportCode != null">report_code,</if>
			<if test="fromType != null">from_type,</if>
			<if test="tokenObj != null">token_obj,</if>
			<if test="tokenType != null">token_type,</if>
			<if test="reportType != null">report_type,</if>
			<if test="userIp != null">user_ip,</if>
			<if test="userAddress != null">user_address,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="reportCode != null">#{reportCode},</if>
			<if test="fromType != null">#{fromType},</if>
			<if test="tokenObj != null">#{tokenObj},</if>
			<if test="tokenType != null">#{tokenType},</if>
			<if test="reportType != null">#{reportType},</if>
			<if test="userIp != null">#{userIp},</if>
			<if test="userAddress != null">#{userAddress},</if>
		</trim>
	</insert>
	<update id="upReportUserNum" parameterType="com.creality.falcon.stat.entity.report.ReportUserNum">
		update report_user_num 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="createTime != null">create_time=#{createTime},</if>
			<if test="reportCode != null">report_code=#{reportCode},</if>
			<if test="fromType != null">from_type=#{fromType},</if>
			<if test="tokenObj != null">token_obj=#{tokenObj},</if>
			<if test="tokenType != null">token_type=#{tokenType},</if>
			<if test="reportType != null">report_type=#{reportType},</if>
			<if test="userIp != null">user_ip=#{userIp},</if>
			<if test="userAddress != null">user_address=#{userAddress},</if>
		</trim>
		where id = #{id}
	</update>
	<select id="selReportUserNumList" parameterType="com.creality.falcon.stat.entity.report.ReportUserNum" resultMap="ReportUserNumResult">
		<include refid="selReportUserNum"/>
		<where>
			<if test="id != null"> and id=#{id}</if>
			<if test="createTime != null  and createTime != ''"> and create_time=#{createTime,jdbcType=TIMESTAMP}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="tokenObj != null  and tokenObj != ''"> and token_obj=#{tokenObj}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
			<if test="reportType != null  and reportType != ''"> and report_type=#{reportType}</if>
			<if test="userIp != null  and userIp != ''"> and user_ip=#{userIp}</if>
			<if test="reportUserNum.userAddress != null  and reportUserNum.userAddress != ''"> and user_address=#{reportUserNum.userAddress}</if>
		</where>
	</select>
	<delete id="delReportUserNumById" parameterType="String">
		delete from report_user_num where id = #{id}
	</delete>
	
	<select id="reportUserNum" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Integer">
		select count(distinct token_obj) num from report_user_num
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
			<if test="reportType != null  and reportType != ''"> and report_type=#{reportType}</if>
			<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
			
			<if test="reportCodeList != null">
				and report_code in
				<foreach collection="reportCodeList" index="index" item="item" open="(" close=")" separator=",">
				   #{item}
				</foreach>
			</if>
			<if test="tokenObjList != null">
				and token_obj in
				<foreach collection="tokenObjList" index="index" item="item" open="(" close=")" separator=",">
				   #{item}
				</foreach>
			</if>
		</where>
	</select>
	<select id="reportUserNumDistinct" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Integer">
		select count( distinct token_obj) num
	    from report_user_num where 1=1
	    <if test="startDate != null"> and create_time&gt;=#{startDate}</if>
		<if test="endDate != null"> and create_time&lt;#{endDate}</if>
		<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
	    and ((report_code='code_user_num' and token_type=1) or (report_code='code_alive_user_num'))
	</select>
	
	<select id="searchDistinctUserIdList" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.String">
			select distinct token_obj from report_user_num
			where 1=1
			<if test="firstDate != null"> and create_time&gt;=#{firstDate}</if>
			<if test="startDate != null"> and create_time&lt;#{startDate}</if>
			and report_code in('code_user_num','code_alive_user_num')
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
	</select>
	
	<select id="searchDistinctUserIdsExsit" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.String">
		select a.token_obj from
		(
			select distinct token_obj from report_user_num
			where 1=1
			<if test="firstDate != null"> and create_time&gt;=#{firstDate}</if>
			<if test="startDate != null"> and create_time&lt;#{startDate}</if>
			and report_code in('code_user_num','code_alive_user_num')
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
		)a
		where exists 
		(
			select 1 from report_user_num n where a.token_obj=n.token_obj
			<if test="firstDate != null"> and n.create_time&lt;#{firstDate}</if>
			<if test="fromType != null  and fromType != ''"> and n.from_type=#{fromType}</if>
			and n.report_code in('code_user_num','code_alive_user_num')
		)
	</select>
	
	<select id="searchDistinctUserIdHistory" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.String">
		select a.token_obj from
		(
			select distinct token_obj from report_user_num
			where 1=1
			<if test="firstDate != null"> and create_time&gt;=#{firstDate}</if>
			<if test="startDate != null"> and create_time&lt;#{startDate}</if>
			and report_code in('code_user_num','code_alive_user_num')
			<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
		)a
		where not exists 
		(
			select 1 from report_user_num n where a.token_obj=n.token_obj
			<if test="firstDate != null"> and n.create_time&lt;#{firstDate}</if>
			<if test="fromType != null  and fromType != ''"> and n.from_type=#{fromType}</if>
			and n.report_code in('code_user_num','code_alive_user_num')
		)
	</select>
	
	<update id="updateUserTokenObjByClientId">
		update report_user_num set token_obj=#{tokenObj},token_type=1 where create_time&gt;=DATE_SUB(now(), INTERVAL 7 DAY) and token_obj=#{clientId}
	</update>
	
	<select id="loadDataLimit" resultType="com.creality.falcon.stat.entity.report.ReportUserNum">
		select create_time createTime,report_code reportCode,from_type fromType,token_obj tokenObj,token_type tokenType,report_type reportType,user_ip userIp
		from report_user_num_back
		order by create_time asc limit #{start},#{size}
	</select>
	
	
	<select id="searchDistinctUserIds" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.String">
		select distinct token_obj from report_user_num
		where report_code='code_user_num' and token_type=1
		<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
		<if test="endDate != null"> and create_time&lt;#{endDate}</if>
		<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
		union
		select distinct token_obj from report_user_num
		where report_code='code_alive_user_num'
		<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
		<if test="endDate != null"> and create_time&lt;#{endDate}</if>
		<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
	</select>
	
	<select id="searchDistinctUserIdsNum" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto" resultType="java.lang.Integer">
		select count( distinct token_obj) num
	    from report_user_num where 1=1
	    <if test="startDate != null"> and create_time&gt;=#{startDate}</if>
		<if test="endDate != null"> and create_time&lt;#{endDate}</if>
		<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
	    and report_code in('code_user_num','code_alive_user_num')
    	<if test="tokenObjList != null">
			and token_obj in
			<foreach collection="tokenObjList" index="index" item="item" open="(" close=")" separator=",">
			   #{item}
			</foreach>
		</if>
	</select>
	
	<select id="reportUserAddressNum" resultType="com.creality.falcon.stat.entity.report.vo.ReportKvModel" parameterType="com.creality.falcon.stat.entity.report.dto.ReportWebSearchDto">
		select user_address keyName,count(user_address) keyNum
		from report_user_num where 1=1
		<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
		<if test="endDate != null"> and create_time&lt;#{endDate}</if>
		<if test="reportCode != null  and reportCode != ''"> and report_code=#{reportCode}</if>
		<if test="fromType != null  and fromType != ''"> and from_type=#{fromType}</if>
		<if test="reportType != null  and reportType != ''"> and report_type=#{reportType}</if>
		<if test="tokenType != null  and tokenType != ''"> and token_type=#{tokenType}</if>
		
		<if test="reportCodeList != null">
			and report_code in
			<foreach collection="reportCodeList" index="index" item="item" open="(" close=")" separator=",">
			   #{item}
			</foreach>
		</if>
		group by user_address
	</select>
	
</mapper>
