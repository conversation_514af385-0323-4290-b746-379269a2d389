<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.falcon.stat.mapper.ReportUserAddressMapper">

	<resultMap type="com.creality.falcon.stat.entity.report.ReportUserAddress" id="ReportUserAddressResult">
		<result property="createTime"    column="create_time"/>
		<result property="id"    column="id"/>
		<result property="provinces"    column="provinces"/>
		<result property="city"    column="city"/>
		<result property="county"    column="county"/>
		<result property="town"    column="town"/>
	</resultMap>
	<sql id="selReportUserAddress">
		select create_time,id,provinces,city,county,town from report_user_address
	</sql>
	<insert id="addReportUserAddress" parameterType="com.creality.falcon.stat.entity.report.ReportUserAddress">
		insert into report_user_address 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="createTime != null">create_time,</if>
			<if test="id != null">id,</if>
			<if test="provinces != null">provinces,</if>
			<if test="city != null">city,</if>
			<if test="county != null">county,</if>
			<if test="town != null">town,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="createTime != null">#{createTime},</if>
			<if test="id != null">#{id},</if>
			<if test="provinces != null">#{provinces},</if>
			<if test="city != null">#{city},</if>
			<if test="county != null">#{county},</if>
			<if test="town != null">#{town},</if>
		</trim>
	</insert>
</mapper>
