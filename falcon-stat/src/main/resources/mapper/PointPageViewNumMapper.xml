<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.falcon.stat.mapper.PointPageViewNumMapper">

	<resultMap type="com.creality.falcon.stat.entity.point.PointPageViewNum" id="PointPageViewNumResult">
		<result property="createTime"    column="create_time"/>
		<result property="id"    column="id"/>
		<result property="pointFromCode"    column="point_from_code"/>
		<result property="fromType"    column="from_type"/>
		<result property="loginState"    column="login_state"/>
		<result property="userIpId"    column="user_ip_id"/>
		<result property="viewNum"    column="view_num"/>
	</resultMap>
	<sql id="selPointPageViewNum">
		select create_time,id,point_from_code,from_type,login_state,user_ip_id,view_num from point_page_view_num
	</sql>
	<insert id="addPointPageViewNum" parameterType="com.creality.falcon.stat.entity.point.PointPageViewNum">
		insert into point_page_view_num 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="createTime != null">create_time,</if>
			<if test="id != null">id,</if>
			<if test="pointFromCode != null">point_from_code,</if>
			<if test="fromType != null">from_type,</if>
			<if test="loginState != null">login_state,</if>
			<if test="userIpId != null">user_ip_id,</if>
			<if test="viewNum != null">view_num,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="createTime != null">#{createTime},</if>
			<if test="id != null">#{id},</if>
			<if test="pointFromCode != null">#{pointFromCode},</if>
			<if test="fromType != null">#{fromType},</if>
			<if test="loginState != null">#{loginState},</if>
			<if test="userIpId != null">#{userIpId},</if>
			<if test="viewNum != null">#{viewNum},</if>
		</trim>
	</insert>
	
	<select id="searchCountBySearchDto" parameterType="com.creality.falcon.stat.entity.point.dto.PointLoginDialogSearchDto" resultType="java.lang.Integer">
		select count(*) num from point_page_view_num
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="pointFromCode != null"> and point_from_code=#{pointFromCode}</if>
			<if test="fromType != null">and from_type=#{fromType}</if>
			<if test="loginState != null">and login_state=#{loginState}</if>
		</where>
	
	</select>
	
	<select id="reportPageViewFromType" parameterType="com.creality.falcon.stat.entity.point.dto.PointPageViewSearchDto" resultType="com.creality.falcon.stat.entity.point.vo.PointReprotKvVo">
		select point_from_code keyName,count(*) keyValue from point_page_view_num
		<where>
			<if test="startDate != null"> and create_time&gt;=#{startDate}</if>
			<if test="endDate != null"> and create_time&lt;#{endDate}</if>
			<if test="pointCodeList!=null">
				and point_from_code in
				<foreach collection="pointCodeList" index="index" item="item" open="(" close=")" separator=",">
				   #{item}
				</foreach>
			</if>
			<if test="fromType != null">and from_type=#{fromType}</if>
		</where>
		group by point_from_code
	</select>
	
</mapper>
