# 双重JSON响应问题修复

## 🔍 **问题描述**

API返回了两个JSON对象拼接在一起：

```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "devices": [
            {
                "deviceSn": "23871FFFFFFF125E26018040ProF98",
                "deviceModel": "Creality Falcon A1 Pro",
                "deviceModelShow": "Creality Falcon A1 Pro-dd:dd",
                "deviceMac": "f4:e0:75:d3:dd:dd",
                "displayStatus": "Off-line",
                "deviceKind": 0,
                "thumbnail": "https://falcon-test.craftseek.com/device/20250312-201615.png",
                "connectTime": null
            }
        ]
    }
}{
    "code": 0,
    "msg": "server error",
    "data": null
}
```

## 🎯 **问题根本原因**

### **双重响应机制**

1. **正常响应**: Controller调用Service方法成功，返回成功的JSON响应
2. **异常响应**: Service方法中抛出异常，被全局异常处理器捕获，又返回一个错误的JSON响应

### **触发场景分析**

#### **场景1: 用户未登录**
```java
// FalconAppDeviceServiceImpl.getUserDeviceList()
FalconAccount account = sessionManage.getUserInfo();
if(Objects.isNull(account)){
    throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN); // 抛出异常
}
```

#### **场景2: 数据库查询异常**
```java
// 在查询设备信息时发生数据库异常
List<FalconDeviceDetail> deviceDetails = falconDeviceDetailMapper.selectList(wrapper);
// 如果数据库连接异常，会抛出异常
```

#### **场景3: 全局异常处理器**
```java
// ExceptionConfig.java
@ExceptionHandler(ApiException.class)
public BaseResponse<Object> handleApiException(ApiException e) {
    return BaseResponse.error(e.getCode(), e.getMessage());
}
```

### **双重响应的产生过程**

```
1. 客户端请求 → Controller.getUserDeviceList()
2. Controller调用 → Service.getUserDeviceList()
3. Service执行过程中抛出异常
4. Controller捕获异常前，响应已经开始写入 → 第一个JSON开始
5. 全局异常处理器捕获异常 → 第二个JSON追加
6. 客户端收到拼接的JSON
```

## 🔧 **解决方案**

### **方案1: Service层异常防护**

```java
// FalconAppDeviceServiceImpl.java
public AppUserDeviceListVo getUserDeviceList() {
    try {
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            log.warn("用户未登录，返回空设备列表");
            // 返回空列表而不是抛异常，避免双重响应
            AppUserDeviceListVo emptyResult = new AppUserDeviceListVo();
            emptyResult.setDevices(Collections.emptyList());
            emptyResult.setAccessories(Collections.emptyList());
            return emptyResult;
        }
        
        // ... 正常业务逻辑
        
        return dlr;
        
    } catch (Exception e) {
        log.error("获取用户设备列表异常", e);
        // 返回空列表而不是抛异常，避免双重响应
        AppUserDeviceListVo errorResult = new AppUserDeviceListVo();
        errorResult.setDevices(Collections.emptyList());
        errorResult.setAccessories(Collections.emptyList());
        return errorResult;
    }
}
```

### **方案2: Controller层异常处理**

```java
// FalconAppDeviceController.java
@GetMapping("/user-device-list")
public BaseResponse<AppUserDeviceListVo> getUserDeviceList() {
    try {
        AppUserDeviceListVo response = falconAppDeviceService.getUserDeviceList();
        return BaseResponse.success(response);
    } catch (Exception e) {
        // 避免双重响应，在Controller层统一处理异常
        return BaseResponse.error(500, "获取设备列表失败");
    }
}
```

## 📊 **修复效果对比**

### **修复前**
```json
// 双重JSON响应
{"code":1,"msg":"success","data":{"devices":[...]}}{"code":0,"msg":"server error","data":null}
```

### **修复后**
```json
// 单一正确响应
{
    "code": 1,
    "msg": "success", 
    "data": {
        "devices": [...],
        "accessories": []
    }
}
```

或者在异常情况下：
```json
{
    "code": 500,
    "msg": "获取设备列表失败",
    "data": null
}
```

## 🚀 **预防措施**

### **1. 统一异常处理策略**

```java
// 在Service层避免抛出异常，返回错误状态
public class ServiceResult<T> {
    private boolean success;
    private String message;
    private T data;
    
    public static <T> ServiceResult<T> success(T data) {
        ServiceResult<T> result = new ServiceResult<>();
        result.success = true;
        result.data = data;
        return result;
    }
    
    public static <T> ServiceResult<T> error(String message) {
        ServiceResult<T> result = new ServiceResult<>();
        result.success = false;
        result.message = message;
        return result;
    }
}
```

### **2. Controller层统一处理**

```java
@GetMapping("/user-device-list")
public BaseResponse<AppUserDeviceListVo> getUserDeviceList() {
    ServiceResult<AppUserDeviceListVo> result = falconAppDeviceService.getUserDeviceList();
    
    if (result.isSuccess()) {
        return BaseResponse.success(result.getData());
    } else {
        return BaseResponse.error(500, result.getMessage());
    }
}
```

### **3. 全局异常处理器优化**

```java
@ExceptionHandler(Exception.class)
public BaseResponse<Object> handleException(Exception e, HttpServletResponse response) {
    // 检查响应是否已经提交
    if (response.isCommitted()) {
        log.warn("响应已提交，无法处理异常: {}", e.getMessage());
        return null; // 不再写入响应
    }
    
    log.error("未处理的异常", e);
    return BaseResponse.error(500, "服务器内部错误");
}
```

## 🔍 **排查工具**

### **1. 日志监控**

```java
// 在关键位置添加日志
log.info("开始处理getUserDeviceList请求");
log.info("Service方法执行完成");
log.info("Controller返回响应");
```

### **2. 响应状态检查**

```java
@ExceptionHandler(ApiException.class)
public BaseResponse<Object> handleApiException(ApiException e, HttpServletResponse response) {
    if (response.isCommitted()) {
        log.warn("响应已提交，异常: {}", e.getMessage());
        return null;
    }
    return BaseResponse.error(e.getCode(), e.getMessage());
}
```

### **3. 请求追踪**

```java
// 添加请求ID追踪
@GetMapping("/user-device-list")
public BaseResponse<AppUserDeviceListVo> getUserDeviceList(HttpServletRequest request) {
    String requestId = UUID.randomUUID().toString();
    request.setAttribute("requestId", requestId);
    log.info("处理请求: {}", requestId);
    
    try {
        // ... 业务逻辑
        log.info("请求处理成功: {}", requestId);
        return BaseResponse.success(response);
    } catch (Exception e) {
        log.error("请求处理失败: {}, 异常: {}", requestId, e.getMessage());
        return BaseResponse.error(500, "获取设备列表失败");
    }
}
```

## ✅ **验证方法**

### **1. 单元测试**

```java
@Test
public void testGetUserDeviceList_UserNotLoggedIn() {
    // 模拟用户未登录
    when(sessionManage.getUserInfo()).thenReturn(null);
    
    AppUserDeviceListVo result = falconAppDeviceService.getUserDeviceList();
    
    // 验证返回空列表而不是抛异常
    assertNotNull(result);
    assertTrue(result.getDevices().isEmpty());
    assertTrue(result.getAccessories().isEmpty());
}
```

### **2. 集成测试**

```java
@Test
public void testGetUserDeviceListAPI() {
    // 发送HTTP请求
    ResponseEntity<String> response = restTemplate.getForEntity("/device/user-device-list", String.class);
    
    // 验证响应格式
    String body = response.getBody();
    assertFalse(body.contains("}{"));  // 确保没有双重JSON
    
    // 验证JSON格式正确
    ObjectMapper mapper = new ObjectMapper();
    JsonNode jsonNode = mapper.readTree(body);
    assertTrue(jsonNode.has("code"));
    assertTrue(jsonNode.has("msg"));
    assertTrue(jsonNode.has("data"));
}
```

## 🎉 **总结**

双重JSON响应问题的根本原因是**异常处理机制的冲突**：

1. **Service层抛出异常**
2. **Controller层开始写入响应**
3. **全局异常处理器又写入错误响应**
4. **导致两个JSON拼接**

**解决方案**：
- ✅ **Service层**: 返回结果而不是抛异常
- ✅ **Controller层**: 统一异常处理
- ✅ **全局处理器**: 检查响应状态

现在API将返回单一、正确的JSON响应，不再出现双重响应问题。
