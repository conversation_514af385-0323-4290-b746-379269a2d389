#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件冲突处理接口测试脚本
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:6428/app-api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_file_conflict_api():
    """测试文件冲突处理接口"""
    
    print("=" * 60)
    print("文件冲突处理接口测试")
    print("=" * 60)
    
    # 测试数据
    test_cases = [
        {
            "name": "测试1: 替换文件",
            "data": {
                "action": "replace",
                "scene": "draft",
                "fileName": "test_file.fds",
                "fdsSize": 1024000,  # 1MB
                "fdsPath": "https://test.oss.com/test_file.fds",
                "fdsImage": "https://test.oss.com/test_image.jpg",
                "gcodePath": "https://test.oss.com/test_gcode.gcode"
            }
        },
        {
            "name": "测试2: 新增文件",
            "data": {
                "action": "add",
                "scene": "draft", 
                "fileName": "new_file.fds",
                "fdsSize": 2048000,  # 2MB
                "fdsPath": "https://test.oss.com/new_file.fds",
                "fdsImage": "https://test.oss.com/new_image.jpg",
                "gcodePath": "https://test.oss.com/new_gcode.gcode"
            }
        },
        {
            "name": "测试3: 取消上传",
            "data": {
                "action": "cancel",
                "scene": "draft",
                "fileName": "cancel_file.fds", 
                "fdsSize": 512000,  # 512KB
                "fdsPath": "https://test.oss.com/cancel_file.fds",
                "fdsImage": "https://test.oss.com/cancel_image.jpg"
            }
        },
        {
            "name": "测试4: 文件名过长",
            "data": {
                "action": "add",
                "scene": "draft",
                "fileName": "a" * 60 + ".fds",  # 超过50字符限制
                "fdsSize": 1024000,
                "fdsPath": "https://test.oss.com/long_name.fds",
                "fdsImage": "https://test.oss.com/long_image.jpg"
            }
        },
        {
            "name": "测试5: 文件过大",
            "data": {
                "action": "add",
                "scene": "draft",
                "fileName": "large_file.fds",
                "fdsSize": 60 * 1024 * 1024,  # 60MB，超过50MB限制
                "fdsPath": "https://test.oss.com/large_file.fds",
                "fdsImage": "https://test.oss.com/large_image.jpg"
            }
        },
        {
            "name": "测试6: 无效操作类型",
            "data": {
                "action": "invalid_action",
                "scene": "draft",
                "fileName": "test.fds",
                "fdsSize": 1024000,
                "fdsPath": "https://test.oss.com/test.fds"
            }
        },
        {
            "name": "测试7: recent场景（不限制大小）",
            "data": {
                "action": "add",
                "scene": "recent",
                "fileName": "recent_file.fds",
                "fdsSize": 60 * 1024 * 1024,  # 60MB
                "fdsPath": "https://test.oss.com/recent_file.fds",
                "fdsImage": "https://test.oss.com/recent_image.jpg"
            }
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送请求
            url = f"{BASE_URL}/space-files/handle-conflict"
            response = requests.post(url, headers=HEADERS, json=test_case['data'], timeout=10)
            
            # 打印请求信息
            print(f"请求URL: {url}")
            print(f"请求数据: {json.dumps(test_case['data'], ensure_ascii=False, indent=2)}")
            
            # 打印响应信息
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 分析结果
                    if result.get('code') == 200:
                        print("✅ 测试成功")
                    else:
                        print(f"❌ 业务错误: {result.get('msg', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效的JSON: {response.text}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"错误内容: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接错误: 无法连接到服务器，请确认应用是否启动")
        except requests.exceptions.Timeout:
            print("❌ 超时错误: 请求超时")
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
        
        # 等待一下再执行下一个测试
        if i < len(test_cases):
            time.sleep(1)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

def test_health_check():
    """测试应用健康状态"""
    print("检查应用健康状态...")
    
    try:
        # 测试健康检查端点
        url = f"{BASE_URL}/actuator/health"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 应用状态: {result.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

if __name__ == "__main__":
    # 先检查应用状态
    if test_health_check():
        print()
        # 执行接口测试
        test_file_conflict_api()
    else:
        print("应用未正常启动，请检查服务状态")
