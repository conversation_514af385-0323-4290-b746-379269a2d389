package com.creality.lcd.falcon.interceptor;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.session.SessionManagerAll;
import com.creality.lcd.falcon.util.IpUtils;
import com.creality.lcd.falcon.util.SessionManage;
import com.creality.lcd.falcon.util.StatisticUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


@Component
public class SessionInterceptor implements HandlerInterceptor {


    private static final Logger log = LogManager.getLogger(SessionInterceptor.class);

    @Autowired
    private SessionManage sessionManage;
    @Autowired
    private SessionManagerAll sessionManagerAll;

    @Autowired
    private StatisticUtils statisticUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //检查请求的 URI
        String sessionId = request.getHeader("sessionId");
        String ip = IpUtils.getClientIp(request);
        log.info("独立web网站--sessionId:{},ip:{}",sessionId,ip);
        String requestURI = request.getRequestURI();
        log.info("独立web网站--requestURI:{}",requestURI);
        if(!"/pc-api/test".equals(requestURI)&&!"/pc-api/firmware/get".equals(requestURI)){
            //统计访问量,以日进行区分
            log.info("独立web网站--统计访问量");
            statisticUtils.recordVisitAndUniqueIpCount("WEB","VISIT",null);
            //统计日活量,以请求IP去重进行区分(不区分是否登录)
            log.info("独立web网站--统计日活量");
            statisticUtils.recordVisitAndUniqueIpCount("WEB","UNIQUE_IP",ip);
        }
        //放行特定接口,使用Set提升效率
        Set<String> ALLOWED_PATHS = new HashSet<>(Arrays.asList(
        		"/pc-api/home/<USER>",
        		"/pc-api/device/deviceList",
        		"/pc-api/home/<USER>",
        		"/pc-api/images/pageList",
        		"/pc-api/images/imageCategoryList",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/firmware/upload",
                "/pc-api/firmware/get",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/software/get",
                "/pc-api/software/download",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/test",
                "/pc-api/error", // 显式添加error路径
                "/pc-api/crash/report-logs",
                "/pc-api/software/get-app",
                "/pc-api/canvas/query-material-detail",
                "/pc-api/canvas/query-image-category",
                "/pc-api/canvas/images-by-category"
        ));

        if (ALLOWED_PATHS.contains(requestURI)) {
            return true;
        }

        // 检查 sessionId 是否为空
        if (StringUtils.isBlank(sessionId)) {
            log.warn("独立web网站--请求sessionId为空");
            throw new ApiException(ResultEnum.PARAMETER_ERROR);
        }

        // 检查 session 是否过期
        if (!sessionManagerAll.checkSessionExpired(sessionId)) {
            log.warn("独立web网站--请求session已过期");
            throw new ApiException(ResultEnum.ACCOUNT_SESSION_EXPIRE_ERROR);
        }
        // 放行请求
        return true;
    }
}
