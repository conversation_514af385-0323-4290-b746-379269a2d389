package com.creality.lcd.falcon.controller.images;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.page.PageAppResult;
import com.creality.lcd.falcon.pojo.dto.pc.FalconMaterialUsersSearchDto;
import com.creality.lcd.falcon.pojo.dto.pc.MaterialUsersSaveDto;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;
import com.creality.lcd.falcon.pojo.vo.pc.MaterialUsersVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.IFalconMaterialUsersParamService;
import com.creality.lcd.falcon.service.IFalconMaterialUsersService;
import com.creality.lcd.falcon.util.SessionManage;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.validation.Valid;

/**
 * 自定义材料库管理
 */
@Controller
@RequestMapping("/materialUsers")
public class FalconMaterialUsersController {

	@Autowired
	private IFalconMaterialUsersService falconMaterialUsersService;
	@Autowired
	private IFalconMaterialUsersParamService falconMaterialUsersParamService;
	@Autowired
	private SessionManage sessionManage;
	
	/**
	 * 查询材料库
	 * @param falconMaterialUsers
	 * @return
	 */
	@RequestMapping("/pageList")
	@ResponseBody
	public BaseResponse<List<MaterialUsersVo>> pageList(FalconMaterialUsers falconMaterialUsers) {
		List<MaterialUsersVo> dataList=new ArrayList<MaterialUsersVo>();
		FalconAccount account=sessionManage.getUserInfo();
		if(account!=null&&account.getUserId()!=null) {
			//查询材料库
			falconMaterialUsers.setUserId(account.getUserId());
			falconMaterialUsers.setDelState(0);
			List<FalconMaterialUsers> materials=falconMaterialUsersService.selFalconMaterialUsersList(falconMaterialUsers);
			
			if(CollectionUtil.isNotEmpty(materials)) {
				//查询材料对应的参数
				List<Long> idList=materials.stream().map(FalconMaterialUsers::getId).collect(Collectors.toList());
				QueryWrapper<FalconMaterialUsersParam> query=new QueryWrapper<FalconMaterialUsersParam>();
				query.in("material_id", idList);
				query.orderByAsc("id");
				List<FalconMaterialUsersParam> paramList=falconMaterialUsersParamService.list(query);
				//材料参数转map
				Map<Long,List<FalconMaterialUsersParam>> pMap=new HashMap<Long, List<FalconMaterialUsersParam>>();
				if(CollectionUtil.isNotEmpty(paramList)) {
					pMap=paramList.stream().collect(Collectors.groupingBy(FalconMaterialUsersParam::getMaterialId));
				}
				//设置返回数据
				for(FalconMaterialUsers m:materials) {
					MaterialUsersVo vo=new MaterialUsersVo();
					BeanUtils.copyProperties(m, vo);
					vo.setParamList(pMap.get(m.getId()));
					dataList.add(vo);
				}
			}
		}
		return BaseResponse.success(dataList);
	}
	/**
	 * 保存自定义材料库
	 * @param falconMaterialUsers
	 * @return
	 */
	@RequestMapping("/save")
	@ResponseBody
	public BaseResponse<String> addFalconMaterialUsers(@Valid @RequestBody MaterialUsersSaveDto saveDto) {
		FalconMaterialUsers meterial=new FalconMaterialUsers();
		BeanUtils.copyProperties(saveDto, meterial);
		//当前用户校验
		FalconAccount account=sessionManage.getUserInfo();
		//当前用户操作属于自己的数据
		if(account==null||account.getUserId()==null) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		//厚度参数校验
		if(saveDto.getThicknessState()==0) {
			if(saveDto.getThickness()==null) {
				return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
			}
		}
		//大小非标准的时候
		if(saveDto.getSizeState()==0) {
			if(saveDto.getMaterialLength()==null||saveDto.getMaterialWidth()==null) {
				return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
			}
		}
		
		//设置默认参数
		meterial.setCreateTime(new Date());
		meterial.setDelState(0);
		meterial.setUserId(account.getUserId());
		
		List<FalconMaterialUsersParam> paramList=saveDto.getParamList();
		falconMaterialUsersService.saveMaterial(meterial,paramList);
		return BaseResponse.success();
	}
	
	@RequestMapping("/selById")
	@ResponseBody
	public BaseResponse<MaterialUsersVo> selById(Long id) {
		FalconMaterialUsers falconMaterialUsers=falconMaterialUsersService.getById(id);
		//未删除情况
		if(falconMaterialUsers!=null&&falconMaterialUsers.getDelState()==0) {
			FalconAccount account=sessionManage.getUserInfo();
			//当前用户操作属于自己的数据
			if(account!=null&&account.getUserId()!=null) {
				if(falconMaterialUsers.getUserId().equals(account.getUserId())) {
					QueryWrapper<FalconMaterialUsersParam> query=new QueryWrapper<FalconMaterialUsersParam>();
					query.eq("material_id", id);
					query.orderByAsc("id");
					List<FalconMaterialUsersParam> paramList=falconMaterialUsersParamService.list(query);
					
					MaterialUsersVo vo=new MaterialUsersVo();
					BeanUtils.copyProperties(falconMaterialUsers, vo);
					vo.setParamList(paramList);
					return BaseResponse.success(vo);
				}
			}
		}
		return BaseResponse.success();
	}
	/**
	 * 更新材料库
	 * @param dto
	 * @return
	 */
	@RequestMapping("/updateById")
	@ResponseBody
	public BaseResponse<String> up(@Valid @RequestBody MaterialUsersSaveDto dto) {
		Long id=dto.getId();
		if(id==null) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		//校验数据是否存在
		FalconMaterialUsers materialUsers=falconMaterialUsersService.getById(id);
		if(materialUsers==null||materialUsers.getDelState()==1) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		//当前用户操作属于自己的数据
		FalconAccount account=sessionManage.getUserInfo();
		if(account==null||account.getUserId()==null||!account.getUserId().equals(materialUsers.getUserId())) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		boolean up=falconMaterialUsersService.updateMateriaById(dto);
		return up?BaseResponse.success():BaseResponse.error(ResultEnum.ENCRYPT_ERROR);
	}
	
	@RequestMapping("/delById")
	@ResponseBody
	public BaseResponse<String> del(Long id) {
		//数据校验
		FalconMaterialUsers materialUsers=falconMaterialUsersService.getById(id);
		FalconAccount account=sessionManage.getUserInfo();
		if(account==null||account.getUserId()==null||!account.getUserId().equals(materialUsers.getUserId())) {
			return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
		}
		falconMaterialUsersService.delFalconMaterialUsersById(id);
		return BaseResponse.success();
	}
	
	
	/**
	 * 分页
	 * @param falconMaterialUsers
	 * @param searchDto
	 * @return
	 */
	@RequestMapping("/page")
	@ResponseBody
	public BaseResponse<PageAppResult<MaterialUsersVo>> pageData(FalconMaterialUsers falconMaterialUsers,FalconMaterialUsersSearchDto searchDto) {
		Page<FalconImageUsers> searchPage=new Page<FalconImageUsers>();
		searchPage.setSize(searchDto.getSize());
		searchPage.setCurrent(searchDto.getPage());
		
		//IPage<FalconMaterialUsers> pg =falconMaterialUsersService.selFalconMaterialUsersPage(falconMaterialUsers,searchPage);
		return BaseResponse.success();
	}
	
	
}
