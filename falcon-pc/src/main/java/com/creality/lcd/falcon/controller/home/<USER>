package com.creality.lcd.falcon.controller.home;

import com.creality.lcd.falcon.page.PageAppResult;
import com.creality.lcd.falcon.pojo.dto.app.AppProjectExtUpdateDto;
import com.creality.lcd.falcon.pojo.dto.app.AppUserProjectUpdateDto;
import com.creality.lcd.falcon.pojo.dto.app.AppUserProjectUploadDto;
import com.creality.lcd.falcon.pojo.dto.app.AppUpdateUserProjectStatusDto;
import com.creality.lcd.falcon.pojo.vo.app.*;
import com.creality.lcd.falcon.pojo.vo.pc.PcTutorialVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.home.FalconPcTutorialsService;
import com.creality.lcd.falcon.service.home.impl.PcHomeService;
import com.creality.lcd.falcon.service.project.FalconAppCategoryService;
import com.creality.lcd.falcon.service.project.FalconPcProjectService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目管理(FalconProject)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-17 14:12:31
 */
@RestController
@RequestMapping("/home")
public class FalconPcHomeController {
    /**
     * 服务对象
     */
    @Autowired
    private PcHomeService pcHomeService;

    @Autowired
    private FalconAppCategoryService falconAppCategoryService;

    @Autowired
    private FalconPcTutorialsService appTutorialsService;

    @Autowired
    private FalconPcProjectService projectService;

    /**
     * 首页项目-精选
     * @return 项目列表
     */
    @GetMapping("/query-featured")
    public BaseResponse<List<AppHomeProjectVo>> queryByFeatured() {
        return BaseResponse.success(pcHomeService.queryByFeatured());
    }

    /**
     * 首页项目-模型分类
     * @return 项目列表
     */
    @GetMapping("/query-model-classification")
    public BaseResponse<PageAppResult<AppProjectVo>> queryByModelClassification(
            @RequestParam(defaultValue = "hot") String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String category,
            @RequestParam(defaultValue = "") Long categoryId) {
        return BaseResponse.success(pcHomeService.queryByModelClassification(type,page,size,category,categoryId));
    }

    /**
     * 获取所有项目分类
     *
     * @return 项目分类列表
     */
    @GetMapping("/query-categories")
    public BaseResponse<List<AppCategoryVo>> queryCategories(
            @RequestParam(defaultValue = "PC") String channel) {
        return BaseResponse.success(falconAppCategoryService.getAllCategories(channel));
    }

    /**
     * 获取首页顶级分类
     * @param channel
     * @return
     */
    @GetMapping("/query-top-category")
    public BaseResponse<List<PcHomeCategoryVo>> queryPcHomeCategories(
            @RequestParam(defaultValue = "PC") String channel) {
        return BaseResponse.success(pcHomeService.queryPcHomeCategories(channel));
    }

    /**
     * 根据项目分类获取项目列表
     *
     * @return 项目列表
     */
    @GetMapping("/list-by-category")
    public BaseResponse<PageAppResult<AppProjectVo>> getProjectList(
            @RequestParam String categoryId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageAppResult<AppProjectVo> response = pcHomeService.listByCategory(categoryId, page, size);
        return BaseResponse.success(response);
    }
    /**
     * 搜索接口：模糊查询name、description和label
     */
    @GetMapping("/query-projects")
    public BaseResponse<PageAppResult<AppProjectVo>> searchProjects(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageAppResult<AppProjectVo> response = pcHomeService.searchProjects(keyword,categoryId, page, size);
        return BaseResponse.success(response);
    }
    /**
     * 获取当前项目案例详情
     */
    @GetMapping("/query-project-detail")
    public BaseResponse<AppProjectDetailResopnseVo> queryProjectDetail(
            @RequestParam String projectId) {
        AppProjectDetailResopnseVo response = pcHomeService.getProjectDetail(projectId);
        return BaseResponse.success(response);
    }
    /**
     * wiki教程列表
     */
    @GetMapping("/query-tutorials")
    public BaseResponse<List<PcTutorialVo>> getTutorialsList(
            @RequestParam(defaultValue = "PC") String channel) {
        List<PcTutorialVo> list = appTutorialsService.getTutorialsList(channel);
        return BaseResponse.success(list);
    }
    /**
     * 点击wiki的时候上报点击
     */
    @GetMapping("/click-tutorials")
    public BaseResponse<List<PcTutorialVo>> clickTutorials(Long id) {
        appTutorialsService.clickTutorials(id);
        return BaseResponse.success();
    }
    
    /**
     * 项目分享
     */
    @PostMapping("/project-share")
    public BaseResponse<Void> shareProject(@RequestBody AppProjectExtUpdateDto dto) {
        projectService.incrementProjectExtension(dto.getProjectId(),false,true,false,false);
        return BaseResponse.success();
    }

    /**
     * 项目下载
     */
    @PostMapping("/project-download")
    public BaseResponse<Void> downloadProject(@RequestBody AppProjectExtUpdateDto dto) {
        projectService.incrementProjectExtension(dto.getProjectId(),false,false,true,false);
        return BaseResponse.success();
    }

    @PostMapping("/getone-project")
    public BaseResponse<AppUserProjectVo> getoneProject(Long id) {
    	
    	return null;
    }
    /**
     * 项目上传 保存 对参数不做校验
     */
    @PostMapping("/save-project")
    public BaseResponse<AppUserProjectVo> saveProject(@RequestBody AppUserProjectUploadDto uploadDto) {
    	//设置为保存标识
    	uploadDto.setOperation("save");
    	return BaseResponse.success(pcHomeService.uploadProject(uploadDto));
    }
    /**
     * 项目上传 保存/发布
     */
    @PostMapping("/upload-project")
    public BaseResponse<AppUserProjectVo> uploadProject(@RequestBody @Valid AppUserProjectUploadDto uploadDto) {
    	return BaseResponse.success(pcHomeService.uploadProject(uploadDto));
    }
    /**
     * 分页查询用户项目列表
     */
    @GetMapping("/page-project")
    public BaseResponse<PageAppResult<AppUserProjectVo>> pageUserProject(
            @RequestParam(defaultValue="1") int page,
            @RequestParam(defaultValue="10") int size
    ) {
        return BaseResponse.success(pcHomeService.pageUserProject(page,size));
    }
    /**
     * 更新用户项目状态 0 私密 1 公开
     */
    @PostMapping("/update-project-status")
    public BaseResponse<AppUserProjectVo> updateUserProjectStatus(
            @RequestBody @Valid AppUpdateUserProjectStatusDto updateUserProjectStatusDto) {
        pcHomeService.updateUserProjectStatus(updateUserProjectStatusDto);
        return BaseResponse.success();
    }
    /**
     * 数据回显
     */
    @GetMapping("/detail-project")
    public BaseResponse<AppUserProjectDetailVo> detailUserProject(
            @RequestParam(value = "id") Long id) {
        return BaseResponse.success(pcHomeService.detailUserProject(id));
    }
    /**
     * 编辑用户项目
     */
    @PostMapping("/update-project")
    public BaseResponse<AppUserProjectDetailVo> updateUserProject(
            @RequestBody @Valid AppUserProjectUpdateDto projectUpdateDto) {
        pcHomeService.updateUserProject(projectUpdateDto);
        return BaseResponse.success();
    }
    /**
     * 删除用户项目
     */
    @DeleteMapping("/del-project")
    public BaseResponse<AppUserProjectDetailVo> delUserProject(
            @RequestParam(value = "id") Long id) {
        pcHomeService.delUserProject(id);
        return BaseResponse.success();
    }
    /**
     * 撤回用户项目
     */
    @DeleteMapping("/withdraw-project")
    public BaseResponse<AppUserProjectDetailVo> withdrawUserProject(
            @RequestParam(value = "id") Long id) {
        pcHomeService.withdrawUserProject(id);
        return BaseResponse.success();
    }
}

