package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.interceptor.SessionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private SessionInterceptor sessionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义要排除的路径
        List<String> excludePaths = Arrays.asList(
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/firmware/upload",
                "/pc-api/firmware/get",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/software/get",
                "/pc-api/software/download",
                "/pc-api/home/<USER>",
                "/pc-api/home/<USER>",
                "/pc-api/test",
                "/pc-api/crash/report-logs",
                "/pc-api/software/get-app",
                "/pc-api/canvas/query-material-detail",
                "/pc-api/canvas/query-image-category",
                "/pc-api/canvas/images-by-category"
        );
        registry.addInterceptor(sessionInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(excludePaths); // 使用列表进行排除
    }

}
