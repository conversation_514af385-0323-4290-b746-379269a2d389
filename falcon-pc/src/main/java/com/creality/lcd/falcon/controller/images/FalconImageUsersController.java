package com.creality.lcd.falcon.controller.images;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.page.PageAppResult;
import com.creality.lcd.falcon.pojo.dto.pc.FalconImageUsersSearchDto;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.IFalconImageUsersService;
import com.creality.lcd.falcon.util.SessionManage;

import jakarta.validation.Valid;

/**
 * QT端 用户头像库
 */
@RestController
@RequestMapping("/imageUsers")
public class FalconImageUsersController {

	@Autowired
	private IFalconImageUsersService falconImageUsersService;
	@Autowired
	private SessionManage sessionManage;
	
	/**
	 * 分页查询
	 * @param falconImageUsers
	 * @param page
	 * @return
	 */
	@RequestMapping("/pageList")
	public BaseResponse<List<FalconImageUsers>> pageList(@RequestBody(required = false) FalconImageUsersSearchDto searchDto) {
		if(searchDto==null) {
			searchDto=new FalconImageUsersSearchDto();
		}
		FalconAccount account=sessionManage.getUserInfo();
		if(account!=null&&account.getUserId()!=null) {
			//设置查询条件
			searchDto.setUserId(account.getUserId());
			//未删除状态
			searchDto.setDelState(1);
			List<FalconImageUsers> dataList=falconImageUsersService.selFalconImageUsersList(searchDto);
			return BaseResponse.success(dataList);
		}
		return BaseResponse.success();
	}
	
	/**
	 * 分页查询
	 * @param falconImageUsers
	 * @param page
	 * @return
	 */
	@RequestMapping("/pageData")
	public BaseResponse<PageAppResult<FalconImageUsers>> pageData(@RequestBody FalconImageUsersSearchDto searchDto) {
		PageAppResult<FalconImageUsers> result=new PageAppResult<FalconImageUsers>();
		result.setTotal(0);
		result.setPage((int)searchDto.getPage());
		result.setSize(searchDto.getSize());
		
		FalconAccount account=sessionManage.getUserInfo();
		if(account!=null&&account.getUserId()!=null) {
			//设置查询条件
			searchDto.setUserId(account.getUserId());
			//未删除状态
			searchDto.setDelState(1);
			
			Page<FalconImageUsers> searchPage=new Page<FalconImageUsers>();
			searchPage.setSize(searchDto.getSize());
			searchPage.setCurrent(searchDto.getPage());
			IPage<FalconImageUsers> pg =falconImageUsersService.selFalconImageUsersPage(searchDto,searchPage);
			result.setList(pg.getRecords());
			result.setTotal(pg.getTotal());
			result.setPage((int)pg.getCurrent());
			result.setSize((int)pg.getSize());
		}
		return BaseResponse.success(result);
	}
	
	/**
	 * 上传
	 * @param falconImageUsers
	 * @return
	 */
	@RequestMapping("/uploadIamge")
	public BaseResponse<FalconImageUsers> uploadIamge(@Valid @RequestBody FalconImageUsers falconImageUsers) {
		//新增
		if(falconImageUsers.getId()==null) {
			FalconAccount account=sessionManage.getUserInfo();
			falconImageUsers.setUserId(account.getUserId());
			falconImageUsers.setCreateTime(new Date());
			falconImageUsers.setDelState(1);
			falconImageUsersService.addFalconImageUsers(falconImageUsers);
		}else {//修改
			falconImageUsersService.upFalconImageUsers(falconImageUsers);
		}
		return BaseResponse.success();
	}
	
	
	/**
	 * 删除
	 * @param id
	 * @return
	 */
	@RequestMapping("/delById")
	public BaseResponse<FalconImageUsers> delById(@RequestBody FalconImageUsers falconImageUsers) {
		FalconAccount account=sessionManage.getUserInfo();
		falconImageUsersService.delFalconImageUsersById(falconImageUsers.getId(),account.getUserId());
		return BaseResponse.success();
	}
	
	/*@RequestMapping("/list")
	public BaseResponse<FalconImageUsers> dataList(FalconImageUsers falconImageUsers) {
		List<FalconImageUsers> dataList=falconImageUsersService.selFalconImageUsersList(falconImageUsers);
		return null;
	}
	
	@RequestMapping("/selOne")
	public BaseResponse<FalconImageUsers> selOne(String id) {
		FalconImageUsers falconImageUsers=falconImageUsersService.getById(id);
		return null;
	}
	
	@RequestMapping("/upById")
	public BaseResponse<FalconImageUsers> upById(FalconImageUsers falconImageUsers) {
		boolean up=falconImageUsersService.updateById(falconImageUsers);
		return null;
	}*/
	
}
