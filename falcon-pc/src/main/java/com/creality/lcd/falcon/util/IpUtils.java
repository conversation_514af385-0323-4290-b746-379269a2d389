package com.creality.lcd.falcon.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @Author: zhangshaoming
 * @Date: 2025/04/14 11:03
 * @Description:
 */
public class IpUtils {

    private static final Logger logger = LoggerFactory.getLogger(IpUtils.class);
    private static final String FALLBACK_IP = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    // 需要检查的请求头列表（按优先级排序）
    private static final String[] PROXY_HEADERS = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "X-Real-IP",
            "CF-Connecting-IP"
    };

    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            logger.warn("HttpServletRequest is null");
            return FALLBACK_IP;
        }

        try {
            // 1. 检查代理头信息
            String proxyHeaderIp = checkProxyHeaders(request);
            if (!FALLBACK_IP.equals(proxyHeaderIp)) {
                return proxyHeaderIp;
            }

            // 2. 回退到RemoteAddr
            return processRemoteAddress(request.getRemoteAddr());
        } catch (Exception e) {
            logger.error("IP解析全局异常: {}", e.getMessage(), e);
            return FALLBACK_IP;
        }
    }

    private static String checkProxyHeaders(HttpServletRequest request) {
        for (String header : PROXY_HEADERS) {
            try {
                String value = request.getHeader(header);
                if (isValidHeaderValue(value)) {
                    String[] ips = value.split("\\s*,\\s*");
                    for (String rawIp : ips) {
                        String cleanedIp = cleanIpAddress(rawIp);
                        if (isValidIp(cleanedIp)) {
                            return normalizeIp(cleanedIp);
                        }
                    }
                }
            } catch (Exception e) {
                logger.debug("解析头[{}]异常: {}", header, e.getMessage());
            }
        }
        return FALLBACK_IP;
    }

    private static String processRemoteAddress(String remoteAddr) {
        try {
            String cleanedIp = cleanIpAddress(remoteAddr);
            return isValidIp(cleanedIp) ? normalizeIp(cleanedIp) : FALLBACK_IP;
        } catch (Exception e) {
            logger.debug("RemoteAddr处理异常: {}", e.getMessage());
            return FALLBACK_IP;
        }
    }

    private static String cleanIpAddress(String ipWithPort) {
        if (ipWithPort == null || ipWithPort.isEmpty()) {
            return FALLBACK_IP;
        }

        // 处理IPv6带端口格式 [::1]:8080
        if (ipWithPort.startsWith("[")) {
            int closingBracketIndex = ipWithPort.indexOf(']');
            if (closingBracketIndex > 0) {
                return ipWithPort.substring(1, closingBracketIndex);
            }
            return ipWithPort;
        }

        // 处理IPv4和普通IPv6带端口 ***********:8080
        int lastColonIndex = ipWithPort.lastIndexOf(':');
        if (lastColonIndex > 0) {
            String possibleIp = ipWithPort.substring(0, lastColonIndex);
            if (isValidIp(possibleIp)) {
                return possibleIp;
            }
        }

        return ipWithPort;
    }

    private static String normalizeIp(String ip) {
        if (ip == null) return FALLBACK_IP;

        // 处理本地环回地址
        if (LOCALHOST_IPV6.equals(ip) || "::1".equals(ip)) {
            return LOCALHOST_IPV4;
        }

        // 处理IPv4映射地址 ::ffff:***********
        if (ip.startsWith("::ffff:")) {
            String potentialIpv4 = ip.substring(7);
            if (isValidIpv4(potentialIpv4)) {
                return potentialIpv4;
            }
        }

        // 返回已验证的有效IP
        return ip;
    }

    private static boolean isValidHeaderValue(String value) {
        return value != null
                && !value.isEmpty()
                && !"unknown".equalsIgnoreCase(value);
    }

    private static boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) return false;

        try {
            // 快速校验IPv4格式
            if (isValidIpv4(ip)) return true;

            // 完整校验其他格式
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        } catch (Exception e) {
            logger.debug("IP校验异常: {}", e.getMessage());
            return false;
        }
    }

    private static boolean isValidIpv4(String ip) {
        if (ip == null) return false;
        return ip.matches("^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}$");
    }
}
