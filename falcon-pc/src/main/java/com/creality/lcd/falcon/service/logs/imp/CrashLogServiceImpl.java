package com.creality.lcd.falcon.service.logs.imp;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.creality.lcd.falcon.mappers.web.FalconPcVersionMapper;
import com.creality.lcd.falcon.pojo.entity.web.FalconPcVersion;
import com.creality.lcd.falcon.service.logs.ICrashLogService;

@Service
public class CrashLogServiceImpl implements ICrashLogService{
	
	@Autowired
	private FalconPcVersionMapper falconPcVersionMapper;

	@Override
	public List<String> searchPcVersionList() {
		QueryWrapper<FalconPcVersion> query=new QueryWrapper<FalconPcVersion>();
		query.orderByDesc("id");
		List<FalconPcVersion> versionList=falconPcVersionMapper.selectList(query);
		
		return Optional.ofNullable(versionList).orElse(new ArrayList<FalconPcVersion>()).stream().map(FalconPcVersion::getVersion).collect(Collectors.toList());
		
	}

}
