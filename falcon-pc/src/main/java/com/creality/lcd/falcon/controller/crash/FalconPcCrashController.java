package com.creality.lcd.falcon.controller.crash;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.creality.lcd.falcon.pojo.vo.pc.FalconCreashLogSearchParamVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.crash.FalconPcCrashService;
import com.creality.lcd.falcon.service.logs.ICrashLogService;
import com.creality.lcd.falcon.util.IpUtils;

import jakarta.servlet.http.HttpServletRequest;

/**
 * PC软件崩溃日志接口
 * @Author: zhangshaoming
 * @Date: 2025/04/22 10:53
 * @Description:
 */
@RestController
@RequestMapping("/crash")
public class FalconPcCrashController {

    @Autowired
    private FalconPcCrashService pcCrashService;
    
    @Autowired
    private ICrashLogService crashLogService;
    
    /**
     * PC软件上报崩溃日志
     */
    @PostMapping("/report-logs")
    public BaseResponse<Void> reportLogs(
            @RequestParam(value = "info") String info,
            @RequestParam(value = "dump", required = false) MultipartFile dump,
            @RequestParam(value = "log", required = false) MultipartFile log,
            HttpServletRequest req) throws IOException {

    	String ipAddress=IpUtils.getClientIp(req);
        // 调用服务层处理日志
        pcCrashService.reportLogs(info,ipAddress,dump, log);
        return BaseResponse.success();
    }
    
    /**
     * 崩溃日志-查询条件-下拉框数据接口
     */
    @PostMapping("/searchParams")
    public BaseResponse<FalconCreashLogSearchParamVo> searchParams(){
    	FalconCreashLogSearchParamVo vo=new FalconCreashLogSearchParamVo();
    	//pc版本
    	List<String> softVersionList=crashLogService.searchPcVersionList();
    	vo.setSoftVersionList(softVersionList);
    	//系统版本
    	List<String> osVersionList=pcCrashService.searchOsVersionList();
    	vo.setOsVersionList(osVersionList);
    	//系统归属地
    	List<String> ipAreaList=pcCrashService.searchIpAreaList();
    	vo.setIpAreaList(ipAreaList);
    	
    	return BaseResponse.success(vo);
    }
    
    
}
