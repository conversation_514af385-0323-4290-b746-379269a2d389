package com.creality.lcd.falcon.service.home.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.creality.lcd.falcon.mappers.web.FalconWikiTutorialsMapper;
import com.creality.lcd.falcon.pojo.entity.web.FalconWikiTutorials;
import com.creality.lcd.falcon.pojo.vo.pc.PcTutorialVo;
import com.creality.lcd.falcon.service.home.FalconPcTutorialsService;
import com.creality.lcd.falcon.util.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: tiantao
 * @Date: 2025/1/5 23:06
 * @Description:
 */
@Service
public class FalconPcTutorialsServiceImpl implements FalconPcTutorialsService {
    @Autowired
    private FalconWikiTutorialsMapper tutorialsMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${cache.expiration.tutorials:900}")
    private Long pcTutorialsCacheExpiration;
    @Override
    public List<PcTutorialVo> getTutorialsList(String channel) {
        String cacheKey = "tutorials:" +channel;
        try {
            @SuppressWarnings("unchecked")
            List<PcTutorialVo> cached =
                    JsonUtils.jsonToList(String.valueOf(redisTemplate.opsForValue().get(cacheKey)),PcTutorialVo.class);
            if (CollectionUtils.isNotEmpty(cached)) {
                return cached;
            }

            // 缓存中无数据，从DB查询
            // 使用LambdaQueryWrapper构造查询条件
            LambdaQueryWrapper<FalconWikiTutorials> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FalconWikiTutorials::getChannel, channel);

            // 执行查询并返回结果
            List<FalconWikiTutorials> list= tutorialsMapper.selectList(queryWrapper);
            if (list.isEmpty()) {
                // 没有数据，也缓存空列表，防止缓存穿透
                redisTemplate.opsForValue().set(cacheKey, Collections.emptyList(), pcTutorialsCacheExpiration, TimeUnit.SECONDS);
                return Collections.emptyList();
            }

            List<PcTutorialVo> result = list.stream().map(pc -> {
                PcTutorialVo vo = new PcTutorialVo();
                vo.setTutorialName(pc.getTutorialName());
                vo.setId(pc.getId());
                vo.setTutorialNameEn(pc.getTutorialNameEn());
                vo.setTutorialDesc(pc.getTutorialDesc());
                vo.setTutorialDescEn(pc.getTutorialDescEn());
                vo.setTutorialLink(pc.getTutorialLink());
                vo.setTutorialLinkCn(pc.getTutorialLinkCn());
                vo.setClickNum(pc.getClickNum());
                vo.setTutorialCover(pc.getTutorialCover());
                return vo;
            }).collect(Collectors.toList());

            // 缓存结果
            String cacheValue = JsonUtils.objectToJson(result);
            redisTemplate.opsForValue().set(cacheKey, cacheValue);

            return result;
        } catch (Exception e) {
            // 记录日志或进行其他异常处理
            //TODO tiantao 后期日志改造
            e.printStackTrace();
            return null;
        }
    }
	@Override
	public void clickTutorials(Long id) {
		tutorialsMapper.clickTutorials(id);
	}
}
