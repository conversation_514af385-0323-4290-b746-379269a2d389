package com.creality.lcd.falcon.util;

import java.io.File;
import java.net.InetAddress;
import java.util.Objects;
import java.util.StringJoiner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.maxmind.geoip2.DatabaseReader;

public class GeoLiteUtil {

    public static final Logger log = LoggerFactory.getLogger(GeoLiteUtil.class);

    private static final String FILE_PATH = "/home/<USER>/falcon-pc/GeoLite2-City.mmdb";

    private static volatile DatabaseReader reader;

    public static DatabaseReader getReader() {
        if (reader == null) {
            synchronized (GeoLiteUtil.class) {
                if (reader == null) {
                    reader = getDatabaseReader();
                }
            }
        }
        return reader;
    }

    private static DatabaseReader getDatabaseReader() {
        File file = null;
        try {
            file = new File(FILE_PATH);
            return new DatabaseReader.Builder(file).build();
        } catch (Exception e) {
            log.error("获取geolite2数据库报错：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取ip所在国家
     *
     * @param ip 需查询的IP
     * @return 返回查询结果
     */
    public static String getCountry(String ip) {
        DatabaseReader reader = getReader();
        try {
            return Objects.nonNull(reader) ? reader.city(InetAddress.getByName(ip)).getCountry().getNames().get("zh-CN") : "";
        } catch (Exception e) {
            log.error("获取ip所在国家异常 ip = {}, message = {}", ip, e.getMessage());
        }
        return null;
    }

    /**
     * 获取ip所在省份
     *
     * @param ip 需查询的IP
     * @return 返回查询结果
     */
    public static String getProvince(String ip) {
        DatabaseReader reader = getReader();
        try {
            return Objects.nonNull(reader) ? reader.city(InetAddress.getByName(ip)).getMostSpecificSubdivision().getNames().get("zh-CN") : "";
        } catch (Exception e) {
            log.error("获取ip所在省份异常 ip = {}, message = {}", ip, e.getMessage());
        }
        return null;
    }

    /**
     * 获取ip所在市
     *
     * @param ip 需查询的IP
     * @return 返回查询结果
     */
    public static String getCity(String ip) {
        DatabaseReader reader = getReader();
        try {
            return Objects.nonNull(reader) ? reader.city(InetAddress.getByName(ip)).getCity().getNames().get("zh-CN") : "";
        } catch (Exception e) {
            log.error("获取ip所在市异常 ip = {}, message = {}", ip, e.getMessage());
        }
        return null;
    }

    /**
     * 获取ip所在经纬度
     *
     * @param ip 需查询的IP
     * @return 返回查询结果
     */
    private static JSONObject getLongitudeAndLatitude(String ip) {
        DatabaseReader reader = getDatabaseReader();
        JSONObject resJson = new JSONObject();
        resJson.put("latitude", 0d);
        resJson.put("longitude", 0d);
        if (Objects.nonNull(reader)) {
            //获取纬度信息
            Double latitude = null;
            //获取经度信息
            Double longitude = null;
            try {
                latitude = reader.city(InetAddress.getByName(ip)).getLocation().getLatitude();
                longitude = reader.city(InetAddress.getByName(ip)).getLocation().getLongitude();
            } catch (Exception e) {
                log.error("获取ip所在经纬度异常 ip = {}, message = {}", ip, e.getMessage());
            }
            resJson.put("latitude", latitude);
            resJson.put("longitude", longitude);
        }
        return resJson;
    }

    /**
     * 根据Ip获取归属信息
     *
     * @param ip 用户Ip
     * @return 返回查询结果
     */
    public static JSONObject getIpToAddress(String ip) throws Exception {
        StringJoiner address = new StringJoiner(",");
        address.add(getCountry(ip));
        address.add(getProvince(ip));
        address.add(getCity(ip));
        JSONObject resJson = GeoLiteUtil.getLongitudeAndLatitude(ip);
        resJson.put("address", String.valueOf(address));
        return resJson;
    }

    public static void main(String[] args) {
        System.out.println(getCountry("**************"));
    }
}