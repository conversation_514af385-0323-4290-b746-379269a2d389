package com.creality.lcd.falcon.controller.images;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.page.PageAppResult;
import com.creality.lcd.falcon.pojo.dto.pc.ImageCollectDto;
import com.creality.lcd.falcon.pojo.dto.pc.ImageSearchDto;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageCollect;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.pojo.entity.web.FalconImageCategory;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectCountVo;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.IFalconImageCollectService;
import com.creality.lcd.falcon.service.ImageService;
import com.creality.lcd.falcon.util.SessionManage;

import jakarta.validation.Valid;

/**
 * 公共图像库查询
 */
@RestController
@RequestMapping("/images")
public class FalconImageController {

	@Autowired
	private ImageService imageService;
	@Autowired
	private SessionManage sessionManage;
	@Autowired
	private IFalconImageCollectService falconImageCollectService;
	
	@RequestMapping("/pageList")
	public BaseResponse<List<ImageCollectVo>> pageList(@RequestBody(required = false) ImageSearchDto searchDto) {
		if(searchDto==null) {
			searchDto=new ImageSearchDto();
		}
		
		FalconAccount account=sessionManage.getUserInfo();
		//如果是查询收藏
		if(searchDto.getCollectType()!=null&&searchDto.getCollectType()==1) {
			if(account==null||account.getUserId()==null) {
				return BaseResponse.error(ResultEnum.VALIDATE_ERROR);
			}
			//设置查询条件
			searchDto.setUserId(account.getUserId());
		}
		//设置查询条件
		List<ImageCollectVo> dataList =imageService.selFalconImageList(searchDto);
		/*
		 * 设置收藏状态和收藏数量
		 */
		if(!CollectionUtils.isEmpty(dataList)) {
			//设置收藏状态
			List<Long> imgIds=dataList.stream().map(ImageCollectVo::getId).collect(Collectors.toList());
			//如果有用户信息则判断是否收藏
			if(account!=null&&account.getUserId()!=null) {
				List<Long> collectImgIds=falconImageCollectService.selCollectedIds(account.getUserId(),imgIds);
				if(!CollectionUtils.isEmpty(collectImgIds)) {
					dataList.forEach(vo->{
						vo.setCollected(collectImgIds.contains(vo.getId()));
					});
				}
			}
			//设置收藏数量
			List<ImageCollectCountVo> collectCountList=falconImageCollectService.selCountByImgIds(imgIds);
			if(!CollectionUtils.isEmpty(collectCountList)) {
				Map<Long,Integer> countMap=collectCountList.stream().collect(Collectors.toMap(ImageCollectCountVo::getImageId, ImageCollectCountVo::getCollectNum,(c1,c2)->c1));
				dataList.forEach(vo->{
					Integer num=countMap.get(vo.getId());
					vo.setCollectCount(num!=null?num:0);
				});
			}
			return BaseResponse.success(dataList);
		}
		return BaseResponse.success();
	}
	
	/**
	 * 分页查询图像库信息
	 */
	@RequestMapping("/page")
	public BaseResponse<PageAppResult<ImageCollectVo>> pageData(@RequestBody ImageSearchDto searchDto) {
		PageAppResult<ImageCollectVo> result=new PageAppResult<ImageCollectVo>();
		result.setTotal(0);
		result.setPage((int)searchDto.getPage());
		result.setSize(searchDto.getSize());
		
		FalconAccount account=sessionManage.getUserInfo();
		if(account!=null&&account.getUserId()!=null) {
			//设置查询条件
			searchDto.setUserId(account.getUserId());
			
			//查询图像数据
			Page<FalconImageUsers> searchPage=new Page<FalconImageUsers>();
			searchPage.setSize(searchDto.getSize());
			searchPage.setCurrent(searchDto.getPage());
			IPage<ImageCollectVo> pg =imageService.selFalconImagePage(searchDto,searchPage);
			
			List<ImageCollectVo> dataList=pg.getRecords();
			if(!CollectionUtils.isEmpty(dataList)) {
				//设置收藏状态
				List<Long> imgIds=dataList.stream().map(ImageCollectVo::getId).collect(Collectors.toList());
				List<Long> collectImgIds=falconImageCollectService.selCollectedIds(account.getUserId(),imgIds);
				if(!CollectionUtils.isEmpty(collectImgIds)) {
					dataList.forEach(vo->{
						vo.setCollected(collectImgIds.contains(vo.getId()));
					});
				}
				//设置收藏数量
				List<ImageCollectCountVo> collectCountList=falconImageCollectService.selCountByImgIds(imgIds);
				if(!CollectionUtils.isEmpty(collectCountList)) {
					Map<Long,Integer> countMap=collectCountList.stream().collect(Collectors.toMap(ImageCollectCountVo::getImageId, ImageCollectCountVo::getCollectNum,(c1,c2)->c1));
					dataList.forEach(vo->{
						Integer num=countMap.get(vo.getId());
						vo.setCollectCount(num!=null?num:0);
					});
				}
			}
			
			result.setList(pg.getRecords());
			result.setTotal(pg.getTotal());
			result.setPage((int)pg.getCurrent());
			result.setSize((int)pg.getSize());
		}
		return BaseResponse.success(result);
	}
	/**
	 * 查询图像分类
	 * @return
	 */
	@RequestMapping("/imageCategoryList")
	public BaseResponse<List<FalconImageCategory>> imageCategoryList(){
		List<FalconImageCategory> cateList=imageService.selImageCategoryList();
		return BaseResponse.success(cateList);
	}
	
	
	/**
	 * 收藏和取消收藏
	 */
	@RequestMapping("/collect")
	public BaseResponse<FalconImageCollect> collect(@Valid @RequestBody ImageCollectDto collect) {
		FalconAccount account=sessionManage.getUserInfo();
		if(account!=null&&account.getUserId()!=null) {
			//收藏
			if(collect.getCollectType()!=null&&collect.getCollectType()==1) {
				FalconImageCollect search=new FalconImageCollect();
				search.setUserId(account.getUserId());
				search.setImgId(collect.getImageId());
				List<FalconImageCollect> exsitList=falconImageCollectService.selFalconImageCollectList(search);
				//未收藏的情况下增加收藏并更新收藏数量
				if(CollectionUtils.isEmpty(exsitList)) {
					FalconImageCollect co=new FalconImageCollect();
					co.setCreateTime(new Date());
					co.setImgId(collect.getImageId());
					co.setUserId(account.getUserId());
					falconImageCollectService.addFalconImageCollect(co);
					
					//增加收藏数
					imageService.upCollectNum(collect.getImageId(),1);
				}
			}else if(collect.getCollectType()!=null&&collect.getCollectType()==0) {//取消收藏 
				Integer del=falconImageCollectService.delFalconImageCollect(account.getUserId(),collect.getImageId());
				if(del>0) {
					//减少收藏数
					imageService.upCollectNum(collect.getImageId(),-1);
				}
			}
		}
		return BaseResponse.success();
	}
}
