package com.creality.lcd.falcon.service.crash.impl;

import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.FalconPcCrashLogMapper;
import com.creality.lcd.falcon.pojo.bo.PcCrashLogReportBo;
import com.creality.lcd.falcon.pojo.dto.pc.PcCrashLogReportDto;
import com.creality.lcd.falcon.pojo.bo.PcCrashLogScreenBo;
import com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog;
import com.creality.lcd.falcon.service.crash.FalconPcCrashService;
import com.creality.lcd.falcon.service.sync.AsyncUploadPcLogFileService;
import com.creality.lcd.falcon.util.BaseUtils;
import com.creality.lcd.falcon.util.GeoLiteUtil;
import com.creality.lcd.falcon.util.ZipUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.hutool.core.util.StrUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * PC软件崩溃日志管理
 * @Author: zhangshaoming
 * @Date: 2025/04/22 10:55
 * @Description:
 */
@Service
public class FalconPcCrashServiceImpl implements FalconPcCrashService {

    private static final Logger logger = LogManager.getLogger(FalconPcCrashServiceImpl.class);

    @Autowired
    private FalconPcCrashLogMapper pcCrashLogMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AsyncUploadPcLogFileService asyncUploadPcLogFileService;

    /**
     * PC软件上报崩溃日志
     * @param info 日志详细信息
     * @param dump dump文件
     * @param log log文件
     */
    @Override
    public void reportLogs(String info, String ipAddress,MultipartFile dump, MultipartFile log) throws IOException {
        logger.info("PC软件上报崩溃日志-开始处理");
        LocalDateTime now = LocalDateTime.now();
        //解析PC软件上报日志信息info字段
        PcCrashLogReportBo reportBo = parseInfo(info);
        if(Objects.isNull(reportBo)) {
            //解析失败，抛出上报信息格式有误异常
            throw new ApiException();
        }
        FalconPcCrashLog pcCrashLog = new FalconPcCrashLog();

        pcCrashLog.setSoftwareName(reportBo.getApp());
        String softwareVersion = getLastWordUsingSubstring(reportBo.getApp());
        pcCrashLog.setSoftwareVersion(softwareVersion);
        pcCrashLog.setOsVersion(reportBo.getOs());
        pcCrashLog.setCpu(reportBo.getCpu());
        pcCrashLog.setMem(reportBo.getMem());
        pcCrashLog.setLang(reportBo.getLang());
        String screenJson = parseScreen(reportBo.getScreen());
        pcCrashLog.setScreen(screenJson);
        pcCrashLog.setHostName(reportBo.getHostName());
        pcCrashLog.setMacAddress(reportBo.getMacAddress());
        pcCrashLog.setUploadTime(now);
        
        //设置ip地址和ip归属地以及设备名称
        pcCrashLog.setDeviceName(reportBo.getDeviceName());
        pcCrashLog.setIpAddress(ipAddress);
        String ipArea=GeoLiteUtil.getCountry(ipAddress);
        pcCrashLog.setIpArea(StrUtil.isNotEmpty(ipArea)?ipArea:"未知");
        
        pcCrashLogMapper.insert(pcCrashLog);

        // 立即读取文件内容到字节数组，脱离临时文件依赖
        byte[] dumpBytes = dump.getBytes();
        String dumpName = dump.getOriginalFilename();
        byte[] logBytes = log.getBytes();
        String logName = log.getOriginalFilename();
        // 压缩文件并获取zipUrl
        List<MultipartFile> zipFiles = Arrays.asList(dump, log);
        byte[] zipBytes = ZipUtils.zipFiles(zipFiles);

        //异步上传dump和log文件
        asyncUploadPcLogFileService.asyncUploadDumpAndLog(pcCrashLog,dumpBytes,dumpName,logBytes,logName,zipBytes);
        logger.info("PC软件上报崩溃日志-处理完成");
    }

    /**
     * 解析PC软件上报日志信息info字段
     * @param info 日志详细信息
     * @return 转换为PcCrashLogReportDto对象
     */
    private PcCrashLogReportBo parseInfo(String info) {
        PcCrashLogReportBo reportBo = null;
        try {
            // 解析主 info JSON
            PcCrashLogReportDto reportDto = objectMapper.readValue(info, PcCrashLogReportDto.class);
            // 解码 Base64 的 screen 字段
            if(Objects.nonNull(reportDto)){
                reportBo = new PcCrashLogReportBo();
                reportBo.setApp(reportDto.getApp());
                reportBo.setCpu(reportDto.getCpu());
                reportBo.setHostName(reportDto.getHostName());
                reportBo.setLang(reportDto.getLang());
                reportBo.setMacAddress(reportDto.getMacAddress());
                reportBo.setMem(reportDto.getMem());
                reportBo.setOs(reportDto.getOs());
                if (StringUtils.isNotBlank(reportDto.getScreen())) {
                    String screenJson = BaseUtils.decodeBase64(reportDto.getScreen());
                    PcCrashLogScreenBo screenDto = objectMapper.readValue(screenJson, PcCrashLogScreenBo.class);
                    reportBo.setScreen(screenDto);
                }
            }

            return reportBo;
        } catch (IOException e) {
            logger.warn("解析PC软件崩溃日志报告失败: " + e.getMessage());
            return reportBo;
        }
    }

    /**
     * 解析PC软件上报日志信息screen字段
     * @param screen 日志详细信息之screen对象
     * @return 转换为json字符串
     **/
    private String parseScreen(PcCrashLogScreenBo screen){
        String screenJson = "";
        try {
            // 序列化 screen 对象为 json字符串
            screenJson = objectMapper.writeValueAsString(screen);
            return screenJson;
        } catch (IOException e) {
            logger.warn("解析PC软件上报日志信息screen字段失败");
        }
        return screenJson;
    }

    /**
     * 获取软件版本号
     * @param input PC软件上报日志信息之app值
     * @return 软件版本号
     */
    public String getLastWordUsingSubstring(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        String trimmed = input.trim(); // 去除首尾空格
        if (trimmed.isEmpty()) {
            return "";
        }
        int lastSpace = trimmed.lastIndexOf(' ');
        return (lastSpace == -1) ? trimmed : trimmed.substring(lastSpace + 1);
    }

	@Override
	public List<String> searchOsVersionList() {
		return pcCrashLogMapper.searchOsVersionList();
	}

	@Override
	public List<String> searchIpAreaList() {
		return pcCrashLogMapper.searchIpAreaList();
	}
}
