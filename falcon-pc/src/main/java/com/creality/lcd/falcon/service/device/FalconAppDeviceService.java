package com.creality.lcd.falcon.service.device;

import com.creality.lcd.falcon.pojo.entity.web.FalconDevice;
import com.creality.lcd.falcon.pojo.vo.app.*;

import java.util.List;

/**
 * @Author: tiantao
 * @Date: 2025/1/13 14:03
 * @Description:
 */
public interface FalconAppDeviceService {
    AppDeviceConnectInfoVo getDeviceStatus(String deviceSn);
    AppDeviceInfoVo getDeviceInfo(String deviceModel);
    AppDeviceDetailVo getDeviceDetail(String deviceSn);
    AppUserDeviceListVo getUserDeviceList();
    /**为qt内嵌web页提供设备列表*/
	List<FalconDevice> selDeviceList();
}
