package com.creality.lcd.falcon.controller.device;

import com.creality.lcd.falcon.pojo.entity.web.FalconDevice;
import com.creality.lcd.falcon.pojo.vo.app.*;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.device.FalconAppDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: tiantao
 * @Date: 2024/12/31 0:15
 * @Description: 设备相关接口
 */
@RestController
@RequestMapping("/device")
public class FalconAppDeviceController {

    @Autowired
    private FalconAppDeviceService falconAppDeviceService;
    
    @RequestMapping("/deviceList")
    @ResponseBody
    public BaseResponse<List<FalconDevice>> deviceList() {
    	List<FalconDevice> deviceList=falconAppDeviceService.selDeviceList();
    	return BaseResponse.success(deviceList);
    }
    
    /**
     * 获取当前设备状态
     */
    @GetMapping("/query-device-status")
    public BaseResponse<AppDeviceConnectInfoVo> queryDeviceStatus(
            @RequestParam String deviceSn) {
        AppDeviceConnectInfoVo response = falconAppDeviceService.getDeviceStatus(deviceSn);
        return BaseResponse.success(response);
    }
    /**
     * 获取根据型号获取设备信息
     */
    @GetMapping("/query-device-info")
    public BaseResponse<AppDeviceInfoVo> queryDeviceInfo(
            @RequestParam String deviceModel) {
        AppDeviceInfoVo response = falconAppDeviceService.getDeviceInfo(deviceModel);
        return BaseResponse.success(response);
    }
    /**
     * 获取当前设备详情
     */
    @GetMapping("/query-device-detail")
    public BaseResponse<AppDeviceDetailVo> queryDeviceDetail(
            @RequestParam String deviceSn) {
        AppDeviceDetailVo response = falconAppDeviceService.getDeviceDetail(deviceSn);
        return BaseResponse.success(response);
    }
    /**
     * 获取当前设备列表
     */
    @GetMapping("/user-device-list")
    public BaseResponse<AppUserDeviceListVo> getUserDeviceList() {
        AppUserDeviceListVo response = falconAppDeviceService.getUserDeviceList();
        return BaseResponse.success(response);
    }
}
