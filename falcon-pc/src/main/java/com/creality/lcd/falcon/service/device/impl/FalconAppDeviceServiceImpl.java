package com.creality.lcd.falcon.service.device.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.creality.lcd.falcon.enums.DeviceKindEnum;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.app.FalconDeviceBindMapper;

import com.creality.lcd.falcon.mappers.app.FalconDeviceDetailMapper;
import com.creality.lcd.falcon.mappers.app.FalconUniversalDeviceMapper;
import com.creality.lcd.falcon.mappers.web.FalconDeviceMapper;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.app.FalconDeviceBind;
import com.creality.lcd.falcon.pojo.entity.app.FalconDeviceDetail;
import com.creality.lcd.falcon.pojo.entity.app.FalconUniversalDevice;
import com.creality.lcd.falcon.pojo.entity.web.FalconDevice;
import com.creality.lcd.falcon.pojo.vo.app.*;
import com.creality.lcd.falcon.service.device.FalconAppDeviceService;
import com.creality.lcd.falcon.util.SessionManage;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: tiantao
 * @Date: 2025/1/13 14:03
 * @Description:
 */
@Service
public class FalconAppDeviceServiceImpl implements FalconAppDeviceService {

    private static final Logger log = LogManager.getLogger(FalconAppDeviceServiceImpl.class);
    @Autowired
    private FalconDeviceBindMapper falconDeviceBindMapper;
    @Autowired
    private FalconDeviceMapper falconDeviceMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${cache.expiration.deviceInfo:60}")
    private Long deviceInfoCacheExpiration;
    @Autowired
    private FalconDeviceDetailMapper falconDeviceDetailMapper;
    @Autowired
    private SessionManage sessionManage;
    @Autowired
    private FalconUniversalDeviceMapper falconUniversalDeviceMapper;

    @Override
    public AppDeviceConnectInfoVo getDeviceStatus(String deviceSn) {
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        // 1) 查 bind
        List<FalconDeviceBind> binds = falconDeviceBindMapper.selectList(
                new LambdaQueryWrapper<FalconDeviceBind>()
                        .eq(FalconDeviceBind::getUserId, account.getUserId())
        );
        if (binds.isEmpty()) {
            // 用户没绑定过任何设备
            return null;
        }

        // 2) “同一用户只可一台雕刻机connected”
        List<FalconDeviceBind> engraverConns = new ArrayList<>();
        for (FalconDeviceBind b : binds) {
            if (b.getDeviceKind() == 0 && b.getStatus() == 1) {
                engraverConns.add(b);
            }
        }
        if (engraverConns.size()>1) {
            // 只留最新
            engraverConns.sort((a,b)-> b.getUpdateTime().compareTo(a.getUpdateTime()));
            for (int i=1; i<engraverConns.size(); i++){
                engraverConns.get(i).setStatus((byte)0);
            }
        }

        // 3) 优先 pick(雕刻机,connected) => pick(净化器,connected) => pick(最近使用)
        FalconDeviceBind engraverConnected = pickBind(binds, 0, 1);
        if (engraverConnected != null) {
            return buildCurrentUsedDTO(engraverConnected, account.getUserId());
        }

        FalconDeviceBind purifierConnected = pickBind(binds, 1, 1);
        if (purifierConnected != null) {
            return buildCurrentUsedDTO(purifierConnected, account.getUserId());
        }

        // 没有连接 => 最近使用(按 update_time desc)
        FalconDeviceBind lastUsed = pickMostRecentlyUsed(binds);
        if (lastUsed != null) {
            return buildCurrentUsedDTO(lastUsed, account.getUserId());
        }
        return null;
    }
    private FalconDeviceBind pickBind(List<FalconDeviceBind> bindList, int deviceKind, int status) {
        // 筛选 => orderByDesc(update_time) => first
        List<FalconDeviceBind> filtered = new ArrayList<>();
        for (FalconDeviceBind b : bindList) {
            if (b.getDeviceKind()==deviceKind && b.getStatus()==status) {
                filtered.add(b);
            }
        }
        if (filtered.isEmpty()) {
            return null;
        }
        filtered.sort((a,b)-> b.getUpdateTime().compareTo(a.getUpdateTime()));
        return filtered.get(0);
    }
    private FalconDeviceBind pickMostRecentlyUsed(List<FalconDeviceBind> bindList) {
        if (bindList.isEmpty()) {
            return null;
        }
        bindList.sort((a,b)-> b.getUpdateTime().compareTo(a.getUpdateTime()));
        return bindList.get(0);
    }

    /**
     * 仅返回: deviceSn, deviceModel, displayStatus, deviceKind
     */
    private AppDeviceConnectInfoVo buildCurrentUsedDTO(FalconDeviceBind bind, String currentUserId) {
        // 查detail
        FalconDeviceDetail detail = falconDeviceDetailMapper.selectOne(
                new LambdaQueryWrapper<FalconDeviceDetail>()
                        .eq(FalconDeviceDetail::getDeviceSn, bind.getDeviceSn())
                        .eq(FalconDeviceDetail::getDeviceKind, bind.getDeviceKind())
        );
        if (detail==null) {
            return null;
        }

        // 查是否有人(非currentUser)连接
        boolean someoneElseConn = isSomeoneElseConnecting(bind.getDeviceSn(), currentUserId);

        // 计算状态
        String st = calcStatus(detail.getStatus(), detail.getWorkStatus(), bind.getStatus(), someoneElseConn);

        AppDeviceConnectInfoVo dto = new AppDeviceConnectInfoVo();
        dto.setDeviceSn(bind.getDeviceSn());
        dto.setDeviceModel(detail.getDeviceModel());
        dto.setDeviceKind(bind.getDeviceKind());
        dto.setDisplayStatus(st);
        return dto;
    }
    /**
     * 查询是否 存在 device_sn=xxx, user_id!= currentUserId, status=1
     */
    private boolean isSomeoneElseConnecting(String deviceSn, String currentUserId) {
        Integer count = Math.toIntExact(falconDeviceBindMapper.selectCount(
                new LambdaQueryWrapper<FalconDeviceBind>()
                        .eq(FalconDeviceBind::getDeviceSn, deviceSn)
                        .ne(FalconDeviceBind::getUserId, currentUserId)
                        .eq(FalconDeviceBind::getStatus, 1)
        ));
        return count != null && count > 0;
    }
    @Override
    public AppDeviceInfoVo getDeviceInfo(String deviceModel) {
        if (StringUtils.isBlank(deviceModel)) {
            return null;
        }
        String cacheKey = "device:deviceModel:" + deviceModel;
        Object cached = redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            if (cached instanceof String && ((String) cached).isEmpty()) {
                // 如果缓存的是空字符串，表示数据不存在，直接返回 null
                return null;
            } else {
                // 如果缓存的是实际数据，返回缓存的数据
                assert cached instanceof AppDeviceInfoVo;
                return (AppDeviceInfoVo) cached;
            }
        }
        // 查询设备信息
        FalconDevice deviceInfo = falconDeviceMapper.selectOne(new LambdaQueryWrapper<FalconDevice>()
                .eq(FalconDevice::getDeviceCode, deviceModel));
        if (deviceInfo == null) {
            // 没有数据，也缓存空列表，防止缓存穿透
            redisTemplate.opsForValue().set(cacheKey, "", deviceInfoCacheExpiration, TimeUnit.SECONDS);
            return null;
        }
        // 创建返回的 VO 对象
        AppDeviceInfoVo vo = new AppDeviceInfoVo();
        // 设置设备编号和设备模型
        BeanUtils.copyProperties(deviceInfo, vo);
        vo.setDeviceModel(deviceInfo.getDeviceCode());
        // 将数据缓存起来
        redisTemplate.opsForValue().set(cacheKey, vo);
        return vo;
    }

    @Override
    public AppDeviceDetailVo getDeviceDetail(String deviceSn) {
        String deviceDetailKey = "device:deviceDetail:" + deviceSn;
        Object detailCached = redisTemplate.opsForValue().get(deviceDetailKey);
        if (detailCached != null) {
            if (detailCached instanceof String && ((String) detailCached).isEmpty()) {
                // 如果缓存的是空字符串，表示数据不存在，直接返回 null
                return null;
            } else {
                // 如果缓存的是实际数据，返回缓存的数据
                assert detailCached instanceof AppDeviceDetailVo;
                return (AppDeviceDetailVo) detailCached;
            }
        }
        // 查询设备详情信息
        FalconDeviceDetail deviceDetail = falconDeviceDetailMapper.selectOne(new LambdaQueryWrapper<FalconDeviceDetail>()
                .eq(FalconDeviceDetail::getDeviceSn, deviceSn));
        if (deviceDetail == null) {
            // 没有数据，也缓存空列表，防止缓存穿透
            redisTemplate.opsForValue().set(deviceDetailKey, "", deviceInfoCacheExpiration, TimeUnit.SECONDS);
            return null;
        }
        // 创建返回的 VO 对象
        AppDeviceDetailVo vo = new AppDeviceDetailVo();
        BeanUtils.copyProperties(deviceDetail, vo);
        if (StringUtils.isBlank(deviceDetail.getDeviceModel())) {
            log.warn("设备详情中设备型号为空，设备SN：{}", deviceSn);
            return vo;
        }
        String deviceKey = "device:deviceModel:" + deviceDetail.getDeviceModel();
        Object deviceCached = redisTemplate.opsForValue().get(deviceKey);
        if (deviceCached != null) {
            if (!(deviceCached instanceof String && ((String) deviceCached).isEmpty())) {
                assert deviceCached instanceof AppDeviceInfoVo;
                AppDeviceInfoVo deviceInfo = (AppDeviceInfoVo) deviceCached;
                vo.setThumbnail(deviceInfo.getThumbnail());
                redisTemplate.opsForValue().set(deviceDetailKey, vo);
                return vo;
            }
        }
        // 查询设备信息
        FalconDevice falconDevice = falconDeviceMapper.selectOne(new LambdaQueryWrapper<FalconDevice>()
                .eq(FalconDevice::getDeviceCode, deviceDetail.getDeviceModel()));
        if (falconDevice == null) {
            log.warn("设备信息表中没有该设备编码的信息，设备编码deviceCode：{}", deviceDetail.getDeviceModel());
            return vo;
        }
        vo.setThumbnail(falconDevice.getThumbnail());
        redisTemplate.opsForValue().set(deviceDetailKey, vo);
        return vo;
    }
    /**
     * 查询流程：
     * 1. 根据 userId 查出 falcon_device_bind 表中的 (device_sn, update_time)
     * 2. 批量查询所有 device_sn 对应的 falcon_device_detail 列表，并形成 map
     * 3. 批量查询所有 device_model 对应的 falcon_device 列表，并形成 map
     * 4. 组装返回结果
     */
    @Override
    public AppUserDeviceListVo getUserDeviceList() {
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        AppUserDeviceListVo result = new AppUserDeviceListVo();

        // 按 update_time desc 拿到该用户的所有 bind
        LambdaQueryWrapper<FalconDeviceBind> bindQuery = new LambdaQueryWrapper<>();
        bindQuery.eq(FalconDeviceBind::getUserId, account.getUserId())
                .orderByDesc(FalconDeviceBind::getUpdateTime);
        List<FalconDeviceBind> bindList = falconDeviceBindMapper.selectList(bindQuery);
        if (bindList.isEmpty()) {
            result.setDevices(Collections.emptyList());
            result.setAccessories(Collections.emptyList());
            return result;
        }

        // 2) 同一用户只能一台雕刻机connected => 处理
        List<FalconDeviceBind> engraverConnected = new ArrayList<>();
        for (FalconDeviceBind b : bindList) {
            if (b.getDeviceKind() == 0 && b.getStatus() == 1) {
                engraverConnected.add(b);
            }
        }
        if (engraverConnected.size() > 1) {
            // 按 updateTime desc, 第一条保留 connected, 其余改为0
            engraverConnected.sort((b1, b2) -> b2.getUpdateTime().compareTo(b1.getUpdateTime()));
            for (int i = 1; i < engraverConnected.size(); i++) {
                FalconDeviceBind old = engraverConnected.get(i);
                // 强制改为0=未连接 (仅内存改,不写回DB)
                old.setStatus((byte)0);
            }
        }

        // 3) 分好sn列表, 并记录 bind.status, bind.update_time
        List<String> engraverSnList = new ArrayList<>();
        List<String> purifierSnList = new ArrayList<>();
        Map<String, Byte> bindStatusMap = new HashMap<>();
        Map<String, LocalDateTime> bindUpdateTimeMap = new HashMap<>();

        for (FalconDeviceBind b : bindList) {
            bindStatusMap.put(b.getDeviceSn(), b.getStatus());
            bindUpdateTimeMap.put(b.getDeviceSn(), b.getUpdateTime());
            if (b.getDeviceKind() == 0) {
                engraverSnList.add(b.getDeviceSn());
            } else {
                purifierSnList.add(b.getDeviceSn());
            }
        }

        // 4) 查 detail
        Map<String, FalconDeviceDetail> engraverDetailMap = new HashMap<>();
        if (!engraverSnList.isEmpty()) {
            List<FalconDeviceDetail> eList = falconDeviceDetailMapper.selectList(
                    new LambdaQueryWrapper<FalconDeviceDetail>()
                            .in(FalconDeviceDetail::getDeviceSn, engraverSnList)
                            .eq(FalconDeviceDetail::getDeviceKind, 0)
            );
            for (FalconDeviceDetail ed : eList) {
                engraverDetailMap.put(ed.getDeviceSn(), ed);
            }
        }

        Map<String, FalconDeviceDetail> purifierDetailMap = new HashMap<>();
        if (!purifierSnList.isEmpty()) {
            List<FalconDeviceDetail> pList = falconDeviceDetailMapper.selectList(
                    new LambdaQueryWrapper<FalconDeviceDetail>()
                            .in(FalconDeviceDetail::getDeviceSn, purifierSnList)
                            .eq(FalconDeviceDetail::getDeviceKind, 1)
            );
            for (FalconDeviceDetail pd : pList) {
                purifierDetailMap.put(pd.getDeviceSn(), pd);
            }
        }

        // 5) 收集 device_model => 查 thumbnail
        //   (注: 你在 detail 里把字段叫 deviceModel, 这等同于 device_code)
        Set<String> engraverCodeSet = new HashSet<>();
        for (FalconDeviceDetail ed : engraverDetailMap.values()) {
            engraverCodeSet.add(ed.getDeviceModel());
        }
        Set<String> purifierCodeSet = new HashSet<>();
        for (FalconDeviceDetail pd : purifierDetailMap.values()) {
            purifierCodeSet.add(pd.getDeviceModel());
        }

        // 5.1 雕刻机型号 => map: device_code -> thumbnail
        Map<String, String> engraverThumbMap = new HashMap<>();
        if (!engraverCodeSet.isEmpty()) {
            List<FalconDevice> eModels = falconDeviceMapper.selectList(
                    new LambdaQueryWrapper<FalconDevice>()
                            .in(FalconDevice::getDeviceCode, engraverCodeSet)
            );
            for (FalconDevice m : eModels) {
                engraverThumbMap.put(m.getDeviceCode(), m.getThumbnail());
            }
        }

        // 5.2 净化器型号 => map: device_model -> thumbnail
        Map<String, String> purifierThumbMap = new HashMap<>();
        if (!purifierCodeSet.isEmpty()) {
            List<FalconUniversalDevice> pModels = falconUniversalDeviceMapper.selectList(
                    new LambdaQueryWrapper<FalconUniversalDevice>()
                            .in(FalconUniversalDevice::getDeviceModel, purifierCodeSet)
            );
            for (FalconUniversalDevice u : pModels) {
                purifierThumbMap.put(u.getDeviceModel(), u.getThumbnail());
            }
        }

        // 6) 还需要知道"是否被别的用户连接" => 先查“除了当前用户”的 binding, device_sn相同 & status=1
        //   收集( sn-> boolean someoneElseConnected )
        //   目的是: 如果我的bind.status=0, 但someoneElseConnected=true => "busy"
        Map<String, Boolean> someoneElseMap = new HashMap<>();
        // 取所有sn => 统计是否有人连接
        List<String> allSn = new ArrayList<>(engraverSnList);
        allSn.addAll(purifierSnList);
        if(!allSn.isEmpty()) {
            // 查询 device_sn in(allSn), user_id != current, status=1
            List<FalconDeviceBind> othersConns = falconDeviceBindMapper.selectList(
                    new LambdaQueryWrapper<FalconDeviceBind>()
                            .in(FalconDeviceBind::getDeviceSn, allSn)
                            .ne(FalconDeviceBind::getUserId, account.getUserId())
                            .eq(FalconDeviceBind::getStatus, 1)
            );
            // sn->true
            for(FalconDeviceBind ob : othersConns) {
                someoneElseMap.put(ob.getDeviceSn(), true);
            }
        }

        // 7) 组装 雕刻机 => key=sn => AppDeviceViewVo
        Map<String, AppDeviceViewVo> engraverVoMap = new HashMap<>();
        for (String sn : engraverSnList) {
            FalconDeviceDetail ed = engraverDetailMap.get(sn);
            if (ed == null) {
                continue;
            }
            AppDeviceViewVo dv = new AppDeviceViewVo();
            dv.setDeviceSn(sn);
            dv.setDeviceKind(ed.getDeviceKind());
            dv.setChildren(new ArrayList<>());
            Byte bindStatus = bindStatusMap.get(sn);
            // 计算状态
            String displaySt = calcStatus(ed.getStatus(), ed.getWorkStatus(), bindStatus,
                    someoneElseMap.getOrDefault(sn, false));
            dv.setDisplayStatus(displaySt);

            dv.setDeviceModel(ed.getDeviceModel());
            dv.setConnectTime(bindUpdateTimeMap.get(sn)); // bind记录的updateTime

            String thumb = engraverThumbMap.get(ed.getDeviceModel());
            dv.setThumbnail(thumb!=null ? thumb : "");

            engraverVoMap.put(sn, dv);
        }

        // 8) 组装 净化器 => 可能挂父, 否则 alone
        List<AppDeviceViewVo> alonePurifiers = new ArrayList<>();
        for (String sn : purifierSnList) {
            FalconDeviceDetail pd = purifierDetailMap.get(sn);
            if (pd == null) {
                continue;
            }
            AppDeviceViewVo pv = new AppDeviceViewVo();
            pv.setDeviceSn(sn);
            pv.setDeviceKind(pd.getDeviceKind());
            pv.setChildren(new ArrayList<>());
            Byte bindStatus = bindStatusMap.get(sn);
            String displaySt = calcStatus(pd.getStatus(), pd.getWorkStatus(), bindStatus,
                    someoneElseMap.getOrDefault(sn, false));
            pv.setDisplayStatus(displaySt);

            pv.setDeviceModel(pd.getDeviceModel());
            pv.setConnectTime(bindUpdateTimeMap.get(sn));

            String pThumb = purifierThumbMap.get(pd.getDeviceModel());
            pv.setThumbnail(pThumb!=null ? pThumb : "");

            // 若 pd.bindParentSn != null => 加到对应雕刻机
            if (pd.getBindParentSn() != null && engraverVoMap.containsKey(pd.getBindParentSn())) {
                engraverVoMap.get(pd.getBindParentSn()).getChildren().add(pv);
            } else {
                alonePurifiers.add(pv);
            }
        }

        // 9) devices => 全部雕刻机 ; accessories => alonePurifiers
        List<AppDeviceViewVo> devices = new ArrayList<>(engraverVoMap.values());

        AppUserDeviceListVo dlr = new AppUserDeviceListVo();
        dlr.setDevices(devices);
        dlr.setAccessories(alonePurifiers);

        return dlr;
    }
    /**
     * 四种状态判定:
     * off-line => detail.status=2
     * connected => detail.status!=2 && bind.status=1
     * free => detail.status!=2 && bind.status=0 && detail.work_status=2 && (!someoneElseConnected)
     * busy => 其他
     */
    private String calcStatus(Byte netStatus, Integer workStatus, Byte bindStatus, boolean someoneElseConnected) {
        if (netStatus == null || netStatus == 2) {
            return "Off-line";
        }
        // 如果当前用户 bind.status=1 => connected
        if (bindStatus!=null && bindStatus == 1) {
            return "Connected";
        }
        // 否则 bind.status=0 => if someoneElseConnected => busy
        if (bindStatus!=null && bindStatus == 0) {
            if (someoneElseConnected) {
                return "Busy";
            }
            // 设备空闲 => free
            if (workStatus != null && workStatus == 2) {
                return "Free";
            }
            // 否则 => busy
            return "Busy";
        }
        // 兜底 => Busy
        return "Busy";
    }

    /**
     * 将 bindList 转为前端需要的 VO 列表
     */
    private List<AppUserDeviceVo> mapBindListToVO(List<FalconDeviceBind> bindList,
                                               Map<String, FalconDevice> deviceMap) {

        List<AppUserDeviceVo> resultList = new ArrayList<>();
        for (FalconDeviceBind bind : bindList) {
            String deviceSn = bind.getDeviceSn();
            String deviceModel = bind.getDeviceModel();
            LocalDateTime updateTime = bind.getUpdateTime();

            // 从 detailMap 中拿到设备详情
//            FalconDeviceDetail detail = detailMap.get(deviceSn);
//
//            // 默认值处理
//            String deviceModel = (detail == null) ? "" : detail.getDeviceModel();

            // 从 deviceMap 中拿到设备信息
            FalconDevice device = null;
            if (deviceMap != null && deviceModel != null) {
                device = deviceMap.get(deviceModel);
            }

            // 取到 thumbnail
            String thumbnail = (device == null) ? "" : device.getThumbnail();

            // 组装VO
            AppUserDeviceVo vo = new AppUserDeviceVo();
            vo.setDeviceSn(deviceSn);
            vo.setDeviceModel(deviceModel);
            vo.setThumbnail(thumbnail);
            vo.setConnectTime(updateTime);

            resultList.add(vo);
        }
        return resultList;
    }
	@Override
	public List<FalconDevice> selDeviceList() {
		QueryWrapper<FalconDevice> que=new QueryWrapper<FalconDevice>();
		que.orderByDesc("create_time");
		return falconDeviceMapper.selectList(que);
	}
}
