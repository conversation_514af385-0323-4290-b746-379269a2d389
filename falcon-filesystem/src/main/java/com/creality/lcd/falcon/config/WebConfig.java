package com.creality.lcd.falcon.config;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Author: zhangshaoming
 * @Date: 2025/02/28 17:19
 * @Description:
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger log = LogManager.getLogger(WebConfig.class);

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 对所有路径应用跨域策略
        registry.addMapping("/**")
                // 允许的源
                .allowedOrigins("*")
                // 允许的方法
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                // 允许的头部
                .allowedHeaders("*");
    }

}
