@echo off
echo ============================================
echo 文件冲突处理接口测试
echo ============================================

echo.
echo 1. 测试健康检查
curl -X GET "http://localhost:6428/app-api/actuator/health" -H "Content-Type: application/json"

echo.
echo.
echo 2. 测试替换文件
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"replace\",\"scene\":\"draft\",\"fileName\":\"test_file.fds\",\"fdsSize\":1024000,\"fdsPath\":\"https://test.oss.com/test_file.fds\",\"fdsImage\":\"https://test.oss.com/test_image.jpg\"}"

echo.
echo.
echo 3. 测试新增文件
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"add\",\"scene\":\"draft\",\"fileName\":\"new_file.fds\",\"fdsSize\":2048000,\"fdsPath\":\"https://test.oss.com/new_file.fds\",\"fdsImage\":\"https://test.oss.com/new_image.jpg\"}"

echo.
echo.
echo 4. 测试取消上传
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"cancel\",\"scene\":\"draft\",\"fileName\":\"cancel_file.fds\",\"fdsSize\":512000,\"fdsPath\":\"https://test.oss.com/cancel_file.fds\"}"

echo.
echo.
echo 5. 测试文件名过长
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"add\",\"scene\":\"draft\",\"fileName\":\"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.fds\",\"fdsSize\":1024000,\"fdsPath\":\"https://test.oss.com/long_name.fds\"}"

echo.
echo.
echo 6. 测试文件过大
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"add\",\"scene\":\"draft\",\"fileName\":\"large_file.fds\",\"fdsSize\":62914560,\"fdsPath\":\"https://test.oss.com/large_file.fds\"}"

echo.
echo.
echo 7. 测试无效操作类型
curl -X POST "http://localhost:6428/app-api/space-files/handle-conflict" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"invalid\",\"scene\":\"draft\",\"fileName\":\"test.fds\",\"fdsSize\":1024000,\"fdsPath\":\"https://test.oss.com/test.fds\"}"

echo.
echo.
echo ============================================
echo 测试完成
echo ============================================
pause
