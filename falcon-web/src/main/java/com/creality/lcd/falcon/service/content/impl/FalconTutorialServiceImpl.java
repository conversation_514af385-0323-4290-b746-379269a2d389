package com.creality.lcd.falcon.service.content.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.constant.redis.RedisKey;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.FalconWikiTutorialsMapper;
import com.creality.lcd.falcon.mapstruct.TutorialMapstruct;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.BackTutorialAddDto;
import com.creality.lcd.falcon.pojo.dto.web.BackTutorialModifyDto;
import com.creality.lcd.falcon.pojo.entity.web.*;
import com.creality.lcd.falcon.pojo.vo.app.AppTutorialVo;
import com.creality.lcd.falcon.pojo.vo.web.BackTutorialPageVo;
import com.creality.lcd.falcon.pojo.vo.web.BackTutorialVo;
import com.creality.lcd.falcon.service.WikiTutorialService;
import com.creality.lcd.falcon.service.content.FalconTutorialService;
import com.creality.lcd.falcon.service.system.FalconStaffService;
import com.creality.lcd.falcon.util.JsonUtils;
import com.creality.lcd.falcon.util.OssUtils;
import com.creality.lcd.falcon.util.S3Utils;
import com.creality.lcd.falcon.utils.TimeUtils;
import com.creality.lcd.falcon.utils.TokenManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.utils.StringUtils;
import com.creality.lcd.falcon.util.EnvironmentUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 15:05
 * @Description: 教程管理接口实现
 */
@Service
public class FalconTutorialServiceImpl implements FalconTutorialService {

    @Autowired
    private FalconWikiTutorialsMapper tutorialsMapper;

    @Autowired
    private TutorialMapstruct tutorialMapstruct;

    @Autowired
    private TokenManage tokenManage;

    @Autowired
    private FalconStaffService staffService;

    @Autowired
    private WikiTutorialService wikiTutorialService;

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private Environment environment;

    @Autowired
    private EnvironmentUtils environmentUtils;

    /**
     * 分页查询教程列表
     * @param pageNum
     * @param pageSize
     * @param channel
     * @param tutorialName
     * @return
     */
    @Override
    public PageResult<BackTutorialPageVo> pageTutorials(int pageNum, int pageSize, String channel, String tutorialName) {
        // 设置分页参数
        Page<FalconWikiTutorials> page = new Page<>(pageNum, pageSize);

        // 创建查询条件
        QueryWrapper<FalconWikiTutorials> tutorialQuery = new QueryWrapper<>();
        // 如果 channel 不为 null 或空，则添加条件
        if (!StringUtils.isBlank(channel)) {
            tutorialQuery.eq("channel", channel);
        }
        Optional.ofNullable(tutorialName).ifPresent(tn -> tutorialQuery.like("tutorial_name", tn));

        // 按创建时间降序排序
        tutorialQuery.orderByDesc("update_time");

        // 查询数据
        Page<FalconWikiTutorials> resultPage = tutorialsMapper.selectPage(page, tutorialQuery);

        // 如果有结果，构建数据
        List<BackTutorialPageVo> deviceVoList = Optional.ofNullable(resultPage.getRecords())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToBackTutorialPageVo)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(resultPage.getTotal(), deviceVoList);
    }

    /**
     * 转换数据
     * @param tutorials
     * @return
     */
    private BackTutorialPageVo convertToBackTutorialPageVo(FalconWikiTutorials tutorials) {
        // 通过创建者和更新者的 ID 获取名字
        Map<String, FalconStaffs> staffsMap = staffService.getStaffsByStaffIds(
                Arrays.asList(tutorials.getCreator(),tutorials.getUpdater()));

        // 构建 VO 对象
        BackTutorialPageVo tutorialPageVo = new BackTutorialPageVo();
        tutorialPageVo.setId(tutorials.getId());
        tutorialPageVo.setChannel(tutorials.getChannel());
        tutorialPageVo.setTutorialName(tutorials.getTutorialName());
        tutorialPageVo.setTutorialNameEn(tutorials.getTutorialNameEn());
        tutorialPageVo.setTutorialDesc(tutorials.getTutorialDesc());
        tutorialPageVo.setTutorialDescEn(tutorials.getTutorialDescEn());
        tutorialPageVo.setTutorialLink(tutorials.getTutorialLink());
        tutorialPageVo.setTutorialLinkCn(tutorials.getTutorialLinkCn());
        tutorialPageVo.setTutorialCover(tutorials.getTutorialCover());
        //点击次数
        tutorialPageVo.setClickNum(tutorials.getClickNum());
        tutorialPageVo.setCreator(Optional.ofNullable(staffsMap.get(tutorials.getCreator()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        tutorialPageVo.setCreateTime(TimeUtils.transferLocalDateTimeToStr(tutorials.getCreateTime()));
        tutorialPageVo.setUpdater(Optional.ofNullable(staffsMap.get(tutorials.getUpdater()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        tutorialPageVo.setUpdateTime(TimeUtils.transferLocalDateTimeToStr(tutorials.getUpdateTime()));

        return tutorialPageVo;
    }

    /**
     * 新增教程
     * @param tutorialAddDto
     * @return
     */
    @Override
    public void addTutorial(BackTutorialAddDto tutorialAddDto) {
        String staffId = tokenManage.getUserId();
        //判断教程名称是否在同一渠道中存在
        QueryWrapper<FalconWikiTutorials> tutorialQuery = new QueryWrapper<>();
        tutorialQuery.eq("channel", tutorialAddDto.getChannel());
        tutorialQuery.eq("tutorial_name", tutorialAddDto.getTutorialName());
        FalconWikiTutorials tutorials = tutorialsMapper.selectOne(tutorialQuery);
        if(Objects.nonNull(tutorials)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_TUTORIAL_EXIST_ERROR);
        }
        tutorials = tutorialMapstruct.tutorialAddDtoToTutorial(tutorialAddDto);
        tutorials.setCreator(String.valueOf(staffId));
        tutorials.setUpdater(String.valueOf(staffId));
        tutorials.setClickNum(0);
        tutorialsMapper.insert(tutorials);
        //刷新缓存
        refreshWikiTutorialsCache(tutorialAddDto.getChannel());
    }

    /**
     * 详情教程
     * @param id
     * @return
     */
    @Override
    public BackTutorialVo detailTutorial(Long id) {
        FalconWikiTutorials tutorials = tutorialsMapper.selectById(id);
        if (Objects.isNull(tutorials)) {
            throw new ApiException(ResultEnum.BACKEND_CONTENT_TUTORIAL_NOT_EXIST_ERROR);
        }
        return tutorialMapstruct.tutorialToTutorialVo(tutorials);
    }

    /**
     * 修改教程
     * @param tutorialModifyDto
     * @return
     */
    @Override
    public void updateTutorial(BackTutorialModifyDto tutorialModifyDto) {
        String staffId = tokenManage.getUserId();
        FalconWikiTutorials tutorials = tutorialsMapper.selectById(tutorialModifyDto.getId());
        if(Objects.isNull(tutorials)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_TUTORIAL_NOT_EXIST_ERROR);
        }
        tutorials = tutorialMapstruct.tutorialModifyDtoToTutorial(tutorialModifyDto);
        tutorials.setUpdater(String.valueOf(staffId));
        tutorialsMapper.updateById(tutorials);
        //刷新缓存
        refreshWikiTutorialsCache(tutorialModifyDto.getChannel());
    }

    /**
     * 删除教程
     * @param id
     */
    @Override
    public void delTutorial(Long id) {
        FalconWikiTutorials tutorials = tutorialsMapper.selectById(id);
        if(Objects.isNull(tutorials)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_TUTORIAL_NOT_EXIST_ERROR);
        }
        String channel = tutorials.getChannel();
        //释放资源
        String currentEnv = environment.getProperty("spring.profiles.active");
        List<String> deleteUrls = Collections.singletonList(tutorials.getTutorialCover());
        if("test".equals(currentEnv)){
            ossUtils.deleteObjectsByUrls(deleteUrls,
                    environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }else{
            s3Utils.deleteObjectByUrls(deleteUrls,
                    environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }
        //删除记录
        tutorialsMapper.deleteById(id);
        //刷新缓存
        refreshWikiTutorialsCache(channel);
    }

    /**
     * 刷新教程缓存
     * @param channel
     */
    private void refreshWikiTutorialsCache(String channel){
        List<AppTutorialVo> tutorialVoList = wikiTutorialService.getTutorialsByChannel(channel);
        String tutorialsCache = JsonUtils.objectToJson(tutorialVoList);
        redisTemplate.opsForValue().set(RedisKey.getTutorials(channel),tutorialsCache);
    }
}
