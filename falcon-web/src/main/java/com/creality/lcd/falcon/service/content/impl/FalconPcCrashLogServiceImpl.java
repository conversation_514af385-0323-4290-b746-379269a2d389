package com.creality.lcd.falcon.service.content.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.FalconPcCrashLogMapper;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.CrashLogSearchDto;
import com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog;
import com.creality.lcd.falcon.pojo.vo.web.BackPcCrashLogPageVo;
import com.creality.lcd.falcon.service.content.FalconPcCrashLogService;
import com.creality.lcd.falcon.util.EnvironmentUtils;
import com.creality.lcd.falcon.util.OssUtils;
import com.creality.lcd.falcon.util.S3Utils;
import com.creality.lcd.falcon.utils.TimeUtils;

/**
 * PC崩溃日志管理实现类
 * @Author: zhangshaoming
 * @Date: 2025/04/23 10:02
 * @Description:
 */
@Service
public class FalconPcCrashLogServiceImpl implements FalconPcCrashLogService {

    @Autowired
    private FalconPcCrashLogMapper pcCrashLogMapper;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private EnvironmentUtils environmentUtils;

    /**
     * PC崩溃日志管理-分页查询
     * @param pageNum 页码
     * @param pageSize 每页查询数据条数
     * @param fileName 文件名称
     * @param uploadDateStart 上传开始时间
     * @param uploadDateEnd 上传结束时间
     * @return
     */
    @Override
    public PageResult<BackPcCrashLogPageVo> pageCrashLog(int pageNum, int pageSize, CrashLogSearchDto searchDto) {
        // 设置分页参数
        Page<FalconPcCrashLog> page = new Page<>(pageNum, pageSize);

        /* 弃用之前的查询方式
        
        // 创建查询条件
        QueryWrapper<FalconPcCrashLog> pcCrashLogQuery = new QueryWrapper<>();
        // 如果 channel 不为 null 或空，则添加条件
        if (!StringUtils.isBlank(fileName)) {
            pcCrashLogQuery.and(wrapper ->
                    wrapper.like("file_name", fileName));
        }

        Optional.ofNullable(uploadDateStart).ifPresent(us -> pcCrashLogQuery.ge("upload_time", us));
        Optional.ofNullable(uploadDateEnd).ifPresent(ue -> pcCrashLogQuery.le("upload_time", ue));

        // 按创建时间降序排序
        pcCrashLogQuery.orderByDesc("upload_time");

        // 查询数据
        Page<FalconPcCrashLog> resultPage = pcCrashLogMapper.selectPage(page, pcCrashLogQuery);
        
        */
        Page<FalconPcCrashLog> resultPage = pcCrashLogMapper.selFalconPcCrashLogList(page, searchDto);
        
        // 如果有结果，构建数据
        List<BackPcCrashLogPageVo> pcCrashLogPageVoList = Optional.ofNullable(resultPage.getRecords())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToBackSoftwarePageVo)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(resultPage.getTotal(), pcCrashLogPageVoList);
    }

    /**
     * 转换数据
     * @param pcCrashLog PC崩溃日志实体
     * @return 前端渲染VO
     */
    private BackPcCrashLogPageVo convertToBackSoftwarePageVo(FalconPcCrashLog pcCrashLog) {
        // 构建 VO 对象
        BackPcCrashLogPageVo pcCrashLogPageVo = new BackPcCrashLogPageVo();
        pcCrashLogPageVo.setId(pcCrashLog.getId());
        pcCrashLogPageVo.setFileName(pcCrashLog.getFileName());
        pcCrashLogPageVo.setSoftwareName(pcCrashLog.getSoftwareName());
        pcCrashLogPageVo.setSoftwareVersion(pcCrashLog.getSoftwareVersion());
        pcCrashLogPageVo.setOsVersion(pcCrashLog.getOsVersion());
        pcCrashLogPageVo.setCpu(pcCrashLog.getCpu());
        pcCrashLogPageVo.setMem(pcCrashLog.getMem());
        pcCrashLogPageVo.setLang(pcCrashLog.getLang());
        pcCrashLogPageVo.setScreen(pcCrashLog.getScreen());
        pcCrashLogPageVo.setHostName(pcCrashLog.getHostName());
        pcCrashLogPageVo.setMacAddress(pcCrashLog.getMacAddress());
        pcCrashLogPageVo.setDumpPath(pcCrashLog.getDumpPath());
        pcCrashLogPageVo.setLogPath(pcCrashLog.getLogPath());
        pcCrashLogPageVo.setLogZip(pcCrashLog.getLogZip());
        pcCrashLogPageVo.setLogZipSize(StringUtils.isNotBlank(pcCrashLog.getLogZipSize())
                ? Integer.parseInt(pcCrashLog.getLogZipSize()) : 0);
        pcCrashLogPageVo.setUploadTime(TimeUtils.transferLocalDateTimeToStr(pcCrashLog.getUploadTime()));
        return pcCrashLogPageVo;
    }

    /**
     * PC崩溃日志管理-删除
     * @param id 日志主键id
     * @return
     */
    @Override
    public void delCrashLog(Long id) {
        QueryWrapper<FalconPcCrashLog> pcCrashLogQuery = new QueryWrapper<>();
        pcCrashLogQuery.eq("id", id);
        FalconPcCrashLog pcCrashLog = pcCrashLogMapper.selectOne(pcCrashLogQuery);
        if(Objects.isNull(pcCrashLog)){
            throw new ApiException(ResultEnum.BACKEND_PC_CRASH_LOG_NOT_EXIST_ERROR);
        }
        List<String> deleteUrls = Arrays.asList(pcCrashLog.getDumpPath(),pcCrashLog.getLogPath(),pcCrashLog.getLogZip());
        //释放OSS或S3的资源
        if("test".equals(environmentUtils.getCurrentEnv())){
            ossUtils.deleteObjectsByUrls(deleteUrls,environmentUtils.getBucketNameByEnv("common"),
                    environmentUtils.getCustomDomainByEnv("common"));
        }else{
            s3Utils.deleteObjectByUrls(deleteUrls,environmentUtils.getBucketNameByEnv("common"),
                    environmentUtils.getCustomDomainByEnv("common"));
        }
        //删除该日志记录
        pcCrashLogMapper.deleteById(id);
    }

    @Override
	public List<String> searchOsVersionList() {
		return pcCrashLogMapper.searchOsVersionList();
	}

	@Override
	public List<String> searchIpAreaList() {
		return pcCrashLogMapper.searchIpAreaList();
	}

	@Override
	public List<String> searchDeviceNameList() {
		return pcCrashLogMapper.searchDeviceNameList();
	}
}
