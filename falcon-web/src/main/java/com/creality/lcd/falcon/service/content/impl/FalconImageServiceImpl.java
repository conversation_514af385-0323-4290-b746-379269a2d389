package com.creality.lcd.falcon.service.content.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.constant.redis.RedisKey;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.*;
import com.creality.lcd.falcon.mapstruct.ImageMapstruct;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.BackImageModifyDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageCollect;
import com.creality.lcd.falcon.pojo.entity.web.*;
import com.creality.lcd.falcon.pojo.vo.app.AppImageGalleryVo;
import com.creality.lcd.falcon.pojo.vo.web.BackImagePageVo;
import com.creality.lcd.falcon.pojo.vo.web.BackImageVo;
import com.creality.lcd.falcon.service.ImageService;
import com.creality.lcd.falcon.service.content.FalconImageService;
import com.creality.lcd.falcon.service.impl.FalconImageCollectService;
import com.creality.lcd.falcon.service.system.FalconStaffService;
import com.creality.lcd.falcon.util.EnvironmentUtils;
import com.creality.lcd.falcon.util.JsonUtils;
import com.creality.lcd.falcon.util.OssUtils;
import com.creality.lcd.falcon.util.S3Utils;
import com.creality.lcd.falcon.utils.FileUtils;
import com.creality.lcd.falcon.utils.TimeUtils;
import com.creality.lcd.falcon.utils.TokenManage;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 15:03
 * @Description: 图像管理接口实现
 */
@Service
public class FalconImageServiceImpl implements FalconImageService {

    private static final Logger log = LogManager.getLogger(FalconImageServiceImpl.class);

    @Autowired
    private FalconImageGalleryMapper imageGalleryMapper;

    @Autowired
    private TokenManage tokenManage;

    @Autowired
    private ImageMapstruct imageMapstruct;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private FalconStaffService staffService;

    @Autowired
    private ImageService imageService;

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private Environment environment;

    @Autowired
    private EnvironmentUtils environmentUtils;
    
    @Autowired
    private FalconImageCollectService falconImageCollectService;

    /**
     * 图像管理-分页查询
     * @param pageNum
     * @param pageSize
     * @param category
     * @param keyword
     * @return
     */
    @Override
    public PageResult<BackImagePageVo> pageImages(int pageNum, int pageSize, String category, String keyword) {
        // 设置分页参数
        Page<FalconImageGallery> page = new Page<>(pageNum, pageSize);

        // 创建查询条件
        QueryWrapper<FalconImageGallery> imageQuery = new QueryWrapper<>();
        if(StringUtils.isNotBlank(category)){
            imageQuery.eq("category", category);
        }
        if(StringUtils.isNotBlank(keyword)){
            imageQuery.and(wrapper ->
                    wrapper.like("label", keyword)
                            .or().like("image_name", keyword));
        }

        // 按创建时间降序排序
        imageQuery.orderByAsc("create_time");

        // 查询数据
        Page<FalconImageGallery> resultPage = imageGalleryMapper.selectPage(page, imageQuery);

        // 如果有结果，构建数据
        List<BackImagePageVo> imageVoList = Optional.ofNullable(resultPage.getRecords())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToBackImagePageVo)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(resultPage.getTotal(), imageVoList);
    }

    /**
     * 转换数据
     * @param imageGallery
     * @return
     */
    private BackImagePageVo convertToBackImagePageVo(FalconImageGallery imageGallery) {
        // 通过创建者和更新者的 ID 获取名字
        Map<String, FalconStaffs> staffsMap = staffService.getStaffsByStaffIds(
                Arrays.asList(imageGallery.getCreator(),imageGallery.getUpdater()));

        // 构建 VO 对象
        BackImagePageVo imagePageVo = new BackImagePageVo();
        imagePageVo.setId(imageGallery.getId());
        imagePageVo.setCategory(imageGallery.getCategory());
        imagePageVo.setLabel(imageGallery.getLabel());
        imagePageVo.setImageName(imageGallery.getImageName());
        imagePageVo.setImageUrl(imageGallery.getImageUrl());
        imagePageVo.setCreator(Optional.ofNullable(staffsMap.get(imageGallery.getCreator()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        imagePageVo.setCreateTime(TimeUtils.transferLocalDateTimeToStr(imageGallery.getCreateTime()));
        imagePageVo.setUpdater(Optional.ofNullable(staffsMap.get(imageGallery.getUpdater()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        imagePageVo.setUpdateTime(TimeUtils.transferLocalDateTimeToStr(imageGallery.getUpdateTime()));

        return imagePageVo;
    }

    /**
     * 图像管理-新增
     * @param categroy
     * @param label
     * @param files
     * @return
     */
    @Override
    public void addImages(String categroy, String label, List<MultipartFile> files) {
        //校验上传的文件是否都是svg文件,如果不是,则弹错误提示
        if(!FileUtils.areFilesAllSVG(files)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_IMAGE_UPLOAD_FILE_FORMAT_ERROR);
        }
        List<FalconImageGallery> imageGalleryList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(files)){
            files.forEach(ima -> {
                FalconImageGallery imageGallery = new FalconImageGallery();
                String imageName = ima.getOriginalFilename();
                imageGallery.setImageName(imageName);
                imageGallery.setCategory(categroy);
                imageGallery.setLabel(label);
                imageGallery.setCollectNum(0);
                String objectName = "images/" + imageName;
                String currentEnv = environment.getProperty("spring.profiles.active");
                log.info("图像管理-新增-当前环境为:{}",currentEnv);
                String imageUrl = "";
                if("test".equals(currentEnv)){
                    imageUrl = ossUtils.putObject(objectName,ima,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
                }else{
                    imageUrl = s3Utils.putObject(objectName,ima,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
                }
                imageGallery.setImageUrl(imageUrl);
                imageGalleryList.add(imageGallery);
            });
        }
        if(!CollectionUtils.isEmpty(imageGalleryList)){
            imageGalleryMapper.batchInsertImages(imageGalleryList);
        }
        //刷新缓存
        refreshImageGalleryCache();
    }

    /**
     * 图像管理-数据回显
     * @param id
     * @return
     */
    @Override
    public BackImageVo detailImage(Long id) {
        FalconImageGallery image = imageGalleryMapper.selectById(id);
        if(Objects.isNull(image)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_IMAGE_NOT_EXIST_ERROR);
        }
        return imageMapstruct.imageToImageVo(image);
    }

    /**
     * 图像管理-编辑
     * @param imageModifyDto
     * @return
     */
    @Override
    public void updateImage(BackImageModifyDto imageModifyDto) {
        String staffId = tokenManage.getUserId();
        FalconImageGallery image = imageGalleryMapper.selectById(imageModifyDto.getId());
        if(Objects.isNull(image)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_IMAGE_NOT_EXIST_ERROR);
        }
        image = imageMapstruct.imageModifyDtoToImage(imageModifyDto);
        image.setUpdater(String.valueOf(staffId));
        imageGalleryMapper.updateById(image);
        //刷新缓存
        refreshImageGalleryCache();
    }

    /**
     * 图像管理-删除
     * @param id
     */
    @Override
    public void delImage(Long id) {
        FalconImageGallery imageGallery = imageGalleryMapper.selectById(id);
        if(Objects.isNull(imageGallery)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_IMAGE_NOT_EXIST_ERROR);
        }
        List<String> deleteUrls = Collections.singletonList(imageGallery.getImageUrl());
        //释放图像资源
        String currentEnv = environment.getProperty("spring.profiles.active");
        log.info("图像管理-删除-当前环境为:{}",currentEnv);
        if("test".equals(currentEnv)){
            ossUtils.deleteObjectsByUrls(deleteUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }else{
            s3Utils.deleteObjectByUrls(deleteUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }
        //删除源数据
        imageGalleryMapper.deleteById(id);
        //删除收藏关系
        QueryWrapper<FalconImageCollect> query=new QueryWrapper<FalconImageCollect>();
        query.eq("img_id", id);
        falconImageCollectService.remove(query);
        //刷新缓存
        refreshImageGalleryCache();
    }

    /**
     * 图像管理-刷新缓存
     */
    private void refreshImageGalleryCache() {
        List<AppImageGalleryVo> imageGalleryVos = imageService.getAllImages();
        String imageGalleryCache = JsonUtils.objectToJson(imageGalleryVos);
        redisTemplate.opsForValue().set(RedisKey.CANVAS_IMAGE_GALLERY_ALL,imageGalleryCache);
    }
}
