package com.creality.lcd.falcon.service.content;

import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.BackPcVersionAddDto;
import com.creality.lcd.falcon.pojo.dto.web.BackPcVersionUpdateStatusDto;
import com.creality.lcd.falcon.pojo.dto.web.BackVerisonFileLogUpdateDto;
import com.creality.lcd.falcon.pojo.dto.web.BackVersionFileUploadDto;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionDetailVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionPageVo;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

/**
 * PC-软件包管理(WIN/MAC)
 * @Author: zhangshaoming
 * @Date: 2025/01/14 16:27
 * @Description:
 */
public interface FalconPcVersionService {

    PageResult<BackPcVersionPageVo> pagePcVersions(int pageNum, int pageSize, String keyword);

    void addPcVersion(BackPcVersionAddDto softwareAddDto);

    void delPcVersion(Long id);

    void updateStatus(BackPcVersionUpdateStatusDto updateStatusDto);

    BackPcVersionDetailVo detailPcVersion(Long id);

    void uploadPack(Long versionId, String os, MultipartFile packFile);

    void updateLogs(BackVerisonFileLogUpdateDto fileLogUpdateDto);

	List<String> searchPcVersionList();
}
