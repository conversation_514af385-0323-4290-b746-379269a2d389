package com.creality.lcd.falcon.service.content;

import java.util.List;

import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.CrashLogSearchDto;
import com.creality.lcd.falcon.pojo.vo.web.BackPcCrashLogPageVo;

/**
 * @Author: zhangshaoming
 * @Date: 2025/04/23 10:02
 * @Description:
 */
public interface FalconPcCrashLogService {

    PageResult<BackPcCrashLogPageVo> pageCrashLog(int pageNum, int pageSize, CrashLogSearchDto searchDto);

    void delCrashLog(Long id);

	List<String> searchOsVersionList();

	List<String> searchIpAreaList();

	List<String> searchDeviceNameList();
}
