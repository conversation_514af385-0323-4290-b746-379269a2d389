package com.creality.lcd.falcon.service.content.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.constant.redis.RedisKey;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.FalconMaterialMapper;
import com.creality.lcd.falcon.mappers.web.FalconMaterialParamMapper;
import com.creality.lcd.falcon.mappers.web.FalconMaterialTypeMapper;
import com.creality.lcd.falcon.mapstruct.MaterialMapstruct;
import com.creality.lcd.falcon.mapstruct.MaterialParamMapstruct;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.export.MaterialExcelImportDto;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialAddDto;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialModifyDto;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialUpdateStatusDto;
import com.creality.lcd.falcon.pojo.entity.web.*;
import com.creality.lcd.falcon.pojo.vo.app.AppMaterialTypeVo;
import com.creality.lcd.falcon.pojo.vo.web.*;
import com.creality.lcd.falcon.service.MaterialParamService;
import com.creality.lcd.falcon.service.MaterialService;
import com.creality.lcd.falcon.service.content.FalconMaterialService;
import com.creality.lcd.falcon.service.system.FalconStaffService;
import com.creality.lcd.falcon.util.EnvironmentUtils;
import com.creality.lcd.falcon.util.JsonUtils;
import com.creality.lcd.falcon.util.OssUtils;
import com.creality.lcd.falcon.util.S3Utils;
import com.creality.lcd.falcon.utils.IdGenerator;
import com.creality.lcd.falcon.utils.TimeUtils;
import com.creality.lcd.falcon.utils.TokenManage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 15:04
 * @Description: 材料管理接口实现
 */
@Service
public class FalconMaterialServiceImpl implements FalconMaterialService {

    private static final Logger log = LogManager.getLogger(FalconMaterialServiceImpl.class);

    @Autowired
    private FalconMaterialMapper materialMapper;

    @Autowired
    private FalconMaterialTypeMapper materialTypeMapper;

    @Autowired
    private FalconMaterialParamMapper materialParamMapper;

    @Autowired
    private TokenManage tokenManage;

    @Autowired
    private MaterialMapstruct materialMapstuct;

    @Autowired
    private MaterialParamMapstruct materialParamMapstruct;

    @Autowired
    private FalconStaffService staffService;

    @Autowired
    private Environment environment;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private MaterialParamService paramService;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private EnvironmentUtils environmentUtils;


    /**
     * 耗材管理-分页查询
     * @param pageNum
     * @param pageSize
     * @param materialType
     * @param keyword
     * @return
     */
    @Override
    public PageResult<BackMaterialPageVo> pageMaterials(int pageNum, int pageSize, String keyword,
                                                        String materialType, Byte status,
                                                        Date startDate, Date endState) {
        // 设置分页参数
        Page<FalconMaterial> page = new Page<>(pageNum, pageSize);

        // 创建查询条件
        QueryWrapper<FalconMaterial> materialQuery = new QueryWrapper<>();
        Optional.ofNullable(keyword).ifPresent(kw -> materialQuery.and(wrapper ->
                wrapper.like("name_cn", kw)
                        .or().like("name_en", kw)
                        .or().like("material_code", kw)
                        .or().like("material_code_id", kw)
        ));
        Optional.ofNullable(materialType).ifPresent(mt -> materialQuery.eq("material_type", mt));
        Optional.ofNullable(status).ifPresent(s -> materialQuery.eq("status", s));
        Optional.ofNullable(startDate).ifPresent(rds -> materialQuery.ge("create_time", rds));
        Optional.ofNullable(endState).ifPresent(rde -> materialQuery.le("create_time", rde));

        // 按创建时间降序排序
        materialQuery.orderByDesc("create_time");

        // 查询数据
        Page<FalconMaterial> resultPage = materialMapper.selectPage(page, materialQuery);

        // 如果有结果，构建数据
        List<BackMaterialPageVo> deviceVoList = Optional.ofNullable(resultPage.getRecords())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToBackMaterialPageVo)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(resultPage.getTotal(), deviceVoList);
    }

    /**
     * 转换数据
     * @param material
     * @return
     */
    private BackMaterialPageVo convertToBackMaterialPageVo(FalconMaterial material) {
        // 通过创建者和更新者的 ID 获取名字
        Map<String, FalconStaffs> staffsMap = staffService.getStaffsByStaffIds(
                Arrays.asList(material.getCreator(),material.getUpdater()));

        // 构建 VO 对象
        BackMaterialPageVo materialPageVo = new BackMaterialPageVo();
        materialPageVo.setId(material.getId());
        materialPageVo.setNameEn(material.getNameEn());
        materialPageVo.setNameCn(material.getNameCn());
        materialPageVo.setMaterialType(material.getMaterialType());
        materialPageVo.setThickness(material.getThickness());
        materialPageVo.setMaterialCode(material.getMaterialCode());
        materialPageVo.setMaterialCodeId(material.getMaterialCodeId());
        materialPageVo.setThumbnail(material.getThumbnail());
        materialPageVo.setStatus(material.getStatus());
        materialPageVo.setCreator(Optional.ofNullable(staffsMap.get(material.getCreator()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        materialPageVo.setCreateTime(TimeUtils.transferLocalDateTimeToStr(material.getCreateTime()));
        materialPageVo.setUpdater(Optional.ofNullable(staffsMap.get(material.getUpdater()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        materialPageVo.setUpdateTime(TimeUtils.transferLocalDateTimeToStr(material.getUpdateTime()));

        return materialPageVo;
    }

    /**
     * 耗材管理-新增
     * @param materialAddDto
     * @return
     */
    @Override
    public void addMaterial(BackMaterialAddDto materialAddDto) {

        LocalDateTime now = LocalDateTime.now();
        //获取用户信息
        String staffId = tokenManage.getUserId();
        //判断耗材编号是否已存在
        if(checkMaterialCodeIfRepeat(materialAddDto.getMaterialCode(),"add",null)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_NUMBER_EXISTED_ERROR);
        }
        //判断耗材编号对应的厚度是否已存在
        if(checkMaterialCodeThicknessIfRepeat(materialAddDto.getMaterialCode(),materialAddDto.getThickness(),
                "add",null)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_THICKNESS_REPEATED_ERROR);
        }
        //插入耗材基本信息表
        FalconMaterial material = materialMapstuct.materialAddDtoToMaterial(materialAddDto);
        Long materialId = IdGenerator.generateMaterialId();
        material.setMaterialId(materialId);
        material.setStatus((byte)0);
        material.setCreator(String.valueOf(staffId));
        material.setCreateTime(now);
        material.setUpdater(String.valueOf(staffId));
        material.setUpdateTime(now);
        materialMapper.insert(material);

        //插入耗材参数表
        List<FalconMaterialParam> materialParamList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(materialAddDto.getMaterialParams())){
            materialAddDto.getMaterialParams().forEach(item->{
                FalconMaterialParam materialParam = new FalconMaterialParam();
                materialParam.setMaterialId(material.getId());
                materialParam.setPower(item.getPower());
                materialParam.setCut(item.getCut());
                materialParam.setEngrave(item.getEngrave());
                materialParam.setCreator(String.valueOf(staffId));
                materialParam.setCreateTime(now);
                materialParam.setUpdater(String.valueOf(staffId));
                materialParam.setUpdateTime(now);
                materialParamList.add(materialParam);
            });
        }

        if(!CollectionUtils.isEmpty(materialParamList)){
            materialParamMapper.batchInsertMaterialParams(materialParamList);
        }
        //刷新缓存
        refreshMaterialCache();
    }

    /**
     * 判断材料编号是否重复
     * @param materialCode
     * @return
     */
    private boolean checkMaterialCodeIfRepeat(String materialCode,String operation,Long id) {
        QueryWrapper<FalconMaterial> materialCodeQuery = new QueryWrapper<>();
        materialCodeQuery.eq("material_code",materialCode);
        if("update".equals(operation)){
            materialCodeQuery.ne("id", id);
        }
        Long materialCodeRepeatCount = materialMapper.selectCount(materialCodeQuery);
        return materialCodeRepeatCount > 0;
    }

    /**
     * 判断材料编号对应的厚度是否重复
     * @param materialCode
     * @param thickness
     * @param operation
     * @param id
     * @return
     */
    private boolean checkMaterialCodeThicknessIfRepeat(String materialCode,String thickness,String operation,Long id) {
        QueryWrapper<FalconMaterial> materialCodeQuery = new QueryWrapper<>();
        materialCodeQuery.eq("material_code",materialCode);
        materialCodeQuery.eq("thickness",thickness);
        if("update".equals(operation)){
            materialCodeQuery.ne("id", id);
        }
        Long materialCodeRepeatCount = materialMapper.selectCount(materialCodeQuery);
        return materialCodeRepeatCount > 0;
    }

    /**
     * 耗材管理-数据回显
     * @param id
     * @return
     */
    @Override
    public BackMaterialVo detailMaterial(Long id) {
        log.info("获取耗材详情 detailMaterial id = {}", id);
        List<String> fixedPowers = Arrays.asList("1.6", "5", "7.5", "10", "12",
                "20", "22", "40", "60", "2-IR", "20-Fiber", "20-Diode","40-Diode");

        FalconMaterial material = materialMapper.selectById(id);
        if(Objects.isNull(material)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_NOT_EXIST_ERROR);
        }
        log.info("获取耗材详情 detailMaterial id = {}, material = {}", id, JSONObject.toJSONString(material));
        BackMaterialVo materialVo = materialMapstuct.materialToMaterialVo(material);
        List<FalconMaterialParam> materialParamList =
                materialParamMapper.selectList(new QueryWrapper<FalconMaterialParam>().eq("material_id",id));
        List<BackMaterialParamVo> paramVos = materialParamMapstruct.materialListToVoList(materialParamList);
        if(!CollectionUtils.isEmpty(paramVos)){
            paramVos.forEach(item -> {
                String powerValue = paramService.convertMaterialParamPower(item.getPower());
                item.setPowerShow(powerValue);
                item.setPower(StringUtils.isEmpty(item.getPower()) ? "/" : item.getPower());
                item.setCut(StringUtils.isEmpty(item.getCut()) ? "/" : item.getCut());
                // 如果 cut 或 engrave 为空，则不设置默认值，保留原值
            });

            // 创建一个 Map 来存储 paramVos，以便后续查找
            Map<String, BackMaterialParamVo> materialParamMap = new HashMap<>();
            for (BackMaterialParamVo paramVo : paramVos) {
                materialParamMap.put(paramVo.getPower(), paramVo);
            }
            // 补全数据
            List<BackMaterialParamVo> completeMaterialParams = new ArrayList<>();
            for (String power : fixedPowers) {
                BackMaterialParamVo materialParam = materialParamMap.get(power);
                if (materialParam != null) {
                    completeMaterialParams.add(materialParam);
                } else {
                    BackMaterialParamVo defaultParam = new BackMaterialParamVo();
                    String powerValue = paramService.convertMaterialParamPower(power);
                    defaultParam.setPowerShow(powerValue);
                    defaultParam.setPower(power);
                    defaultParam.setCut("/");
                    defaultParam.setEngrave("/");
                    completeMaterialParams.add(defaultParam);
                }
            }
            materialVo.setMaterialParams(completeMaterialParams);
        }
        log.info("获取耗材详情 detailMaterial id = {}, materialVo = {}", id, JSONObject.toJSONString(materialVo));
        return materialVo;
    }

    /**
     * 耗材管理-更新
     * @param materialModifyDto
     * @return
     */
    @Override
    public void updateMaterial(BackMaterialModifyDto materialModifyDto) {
        LocalDateTime now = LocalDateTime.now();
        //获取用户信息
        String staffId = tokenManage.getUserId();
        //判断耗材编号是否已存在
        if(checkMaterialCodeIfRepeat(materialModifyDto.getMaterialCode(),"update",materialModifyDto.getId())){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_NUMBER_EXISTED_ERROR);
        }

        //判断耗材编号对应的厚度是否已存在
        if(checkMaterialCodeThicknessIfRepeat(materialModifyDto.getMaterialCode(),materialModifyDto.getThickness(),
                "update",materialModifyDto.getId())){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_THICKNESS_REPEATED_ERROR);
        }

        FalconMaterial material = materialMapstuct.materialModifyDtoToMaterial(materialModifyDto);
        material.setUpdater(String.valueOf(staffId));
        materialMapper.updateById(material);

        //删除耗材参数表
        materialParamMapper.delete(new QueryWrapper<FalconMaterialParam>().eq("material_id",material.getId()));
        //插入耗材参数表
        List<FalconMaterialParam> materialParamList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(materialModifyDto.getMaterialParams())){
            materialModifyDto.getMaterialParams().forEach(item->{
                FalconMaterialParam materialParam = new FalconMaterialParam();
                materialParam.setMaterialId(material.getId());
                materialParam.setPower(item.getPower());
                materialParam.setCut(item.getCut());
                materialParam.setEngrave(item.getEngrave());
                materialParam.setCreator(String.valueOf(staffId));
                materialParam.setCreateTime(now);
                materialParam.setUpdater(String.valueOf(staffId));
                materialParam.setUpdateTime(now);
                materialParamList.add(materialParam);
            });
        }

        if(!CollectionUtils.isEmpty(materialParamList)){
            materialParamMapper.batchInsertMaterialParams(materialParamList);
        }
        //刷新缓存
        refreshMaterialCache();
    }

    /**
     * 耗材管理-删除
     * @param id
     */
    @Override
    public void delMaterial(Long id) {
        FalconMaterial material = materialMapper.selectById(id);
        if(Objects.isNull(material)){
            throw new ApiException("耗材不存在!");
        }
        //删除耗材缩略图图片
        String currentEnv = environment.getProperty("spring.profiles.active");
        List<String> deleteFileUrls = Collections.singletonList(material.getThumbnail());
        if("test".equals(currentEnv)){
            ossUtils.deleteObjectsByUrls(deleteFileUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }else{
            s3Utils.deleteObjectByUrls(deleteFileUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
        }
        //删除耗材参数表
        materialParamMapper.delete(new QueryWrapper<FalconMaterialParam>().eq("material_id",id));
        //删除耗材管理表
        materialMapper.deleteById(id);
        //刷新缓存
        refreshMaterialCache();
    }

    /**
     * 耗材管理-导入
     * @param file
     */
    @Override
    public void importMaterials(MultipartFile file) {
        LocalDateTime now = LocalDateTime.now();
        //获取用户ID
        String userId = tokenManage.getUserId();
        List<MaterialExcelImportDto> materialExcelImportDtos = new ArrayList<>();
        try {
            // 使用 EasyExcel 解析 Excel
            EasyExcel.read(file.getInputStream(), MaterialExcelImportDto.class,
                    new AnalysisEventListener<MaterialExcelImportDto>() {
                        @Override
                        public void invoke(MaterialExcelImportDto data, AnalysisContext context) {
                            materialExcelImportDtos.add(data);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            // 文件解析完成
                        }
                    }).sheet().doRead();

            List<FalconMaterialParam> materialParamList = new ArrayList<>();
            // 数据校验及转换
            materialExcelImportDtos.forEach(dto->{

                //耗材基本信息
                FalconMaterial material = new FalconMaterial();
                Long materialId = IdGenerator.generateMaterialId();
                material.setMaterialId(materialId);
                material.setNameCn(dto.getNameCn());
                material.setNameEn(dto.getNameEn());
                material.setMaterialType(dto.getMaterialType());
                material.setThickness(dto.getThickness());
                material.setMaterialCode(dto.getMaterialCode());
                material.setMaterialCodeId(dto.getMaterialCodeId());
                material.setThumbnail("");
                material.setStatus((byte)1);
                material.setCreator(String.valueOf(userId));
                material.setCreateTime(now);
                material.setUpdater(String.valueOf(userId));
                material.setUpdateTime(now);
                materialMapper.insert(material);

                //耗材参数
                FalconMaterialParam materialParam16 = new FalconMaterialParam();
                materialParam16.setPower("1.6");
                FalconMaterialParam materialParam50 = new FalconMaterialParam();
                materialParam50.setPower("5");
                FalconMaterialParam materialParam75 = new FalconMaterialParam();
                materialParam75.setPower("7.5");
                FalconMaterialParam materialParam100 = new FalconMaterialParam();
                materialParam100.setPower("10");
                FalconMaterialParam materialParam120 = new FalconMaterialParam();
                materialParam120.setPower("12");
                FalconMaterialParam materialParam200 = new FalconMaterialParam();
                materialParam200.setPower("20");
                FalconMaterialParam materialParam220 = new FalconMaterialParam();
                materialParam220.setPower("22");
                FalconMaterialParam materialParam400 = new FalconMaterialParam();
                materialParam400.setPower("40");
                FalconMaterialParam materialParam600 = new FalconMaterialParam();
                materialParam600.setPower("60");
                FalconMaterialParam materialParam2IR = new FalconMaterialParam();
                materialParam2IR.setPower("2-IR");
                FalconMaterialParam materialParam20Fiber = new FalconMaterialParam();
                materialParam20Fiber.setPower("20-Fiber");
                FalconMaterialParam materialParam20Diode = new FalconMaterialParam();
                materialParam20Diode.setPower("20-Diode");
                FalconMaterialParam materialParam40Diode = new FalconMaterialParam();
                materialParam40Diode.setPower("40-Diode");
                if(!StringUtils.isEmpty(dto.getEngraving16()) && !"/".equals(dto.getEngraving16())){
                    materialParam16.setEngrave(dto.getEngraving16());
                }
                if(!StringUtils.isEmpty(dto.getCutting16()) && !"/".equals(dto.getCutting16())){
                    materialParam16.setCut(dto.getCutting16());
                }
                if(!StringUtils.isEmpty(dto.getEngraving50()) && !"/".equals(dto.getEngraving50())){
                    materialParam50.setEngrave(dto.getEngraving50());
                }
                if(!StringUtils.isEmpty(dto.getCutting50()) && !"/".equals(dto.getCutting50())){
                    materialParam50.setCut(dto.getCutting50());
                }
                if(!StringUtils.isEmpty(dto.getEngraving75()) && !"/".equals(dto.getEngraving75())){
                    materialParam75.setEngrave(dto.getEngraving75());
                }
                if(!StringUtils.isEmpty(dto.getCutting75()) && !"/".equals(dto.getCutting75())){
                    materialParam75.setCut(dto.getCutting75());
                }
                if(!StringUtils.isEmpty(dto.getEngraving100()) && !"/".equals(dto.getEngraving100())){
                    materialParam100.setEngrave(dto.getEngraving100());
                }
                if(!StringUtils.isEmpty(dto.getCutting100()) && !"/".equals(dto.getCutting100())){
                    materialParam100.setCut(dto.getCutting100());
                }
                if(!StringUtils.isEmpty(dto.getEngraving120()) && !"/".equals(dto.getEngraving120())){
                    materialParam120.setEngrave(dto.getEngraving120());
                }
                if(!StringUtils.isEmpty(dto.getCutting120()) && !"/".equals(dto.getCutting120())){
                    materialParam120.setCut(dto.getCutting120());
                }
                if(!StringUtils.isEmpty(dto.getEngraving200()) && !"/".equals(dto.getEngraving200())){
                    materialParam200.setEngrave(dto.getEngraving200());
                }
                if(!StringUtils.isEmpty(dto.getCutting200()) && !"/".equals(dto.getCutting200())){
                    materialParam200.setCut(dto.getCutting200());
                }
                if(!StringUtils.isEmpty(dto.getEngraving220()) && !"/".equals(dto.getEngraving220())){
                    materialParam220.setEngrave(dto.getEngraving220());
                }
                if(!StringUtils.isEmpty(dto.getCutting220()) && !"/".equals(dto.getCutting220())){
                    materialParam220.setCut(dto.getCutting220());
                }
                if(!StringUtils.isEmpty(dto.getEngraving400()) && !"/".equals(dto.getEngraving400())){
                    materialParam400.setEngrave(dto.getEngraving400());
                }
                if(!StringUtils.isEmpty(dto.getCutting400()) && !"/".equals(dto.getCutting400())){
                    materialParam400.setCut(dto.getCutting400());
                }
                if(!StringUtils.isEmpty(dto.getEngraving600()) && !"/".equals(dto.getEngraving600())){
                    materialParam600.setEngrave(dto.getEngraving600());
                }
                if(!StringUtils.isEmpty(dto.getCutting600()) && !"/".equals(dto.getCutting600())){
                    materialParam600.setCut(dto.getCutting600());
                }
                if(!StringUtils.isEmpty(dto.getEngraving2WIR()) && !"/".equals(dto.getEngraving2WIR())){
                    materialParam2IR.setEngrave(dto.getEngraving2WIR());
                }
                if(!StringUtils.isEmpty(dto.getCutting2WIR()) && !"/".equals(dto.getCutting2WIR())){
                    materialParam2IR.setCut(dto.getCutting2WIR());
                }
                if(!StringUtils.isEmpty(dto.getEngraving20WFiber()) && !"/".equals(dto.getEngraving20WFiber())){
                    materialParam20Fiber.setEngrave(dto.getEngraving20WFiber());
                }
                if(!StringUtils.isEmpty(dto.getCutting20WFiber()) && !"/".equals(dto.getCutting20WFiber())){
                    materialParam20Fiber.setCut(dto.getCutting20WFiber());
                }
                if(!StringUtils.isEmpty(dto.getEngraving20WDiode()) && !"/".equals(dto.getEngraving20WDiode())){
                    materialParam20Diode.setEngrave(dto.getEngraving20WDiode());
                }
                if(!StringUtils.isEmpty(dto.getCutting20WDiode()) && !"/".equals(dto.getCutting20WDiode())){
                    materialParam20Diode.setCut(dto.getCutting20WDiode());
                }
                if(!StringUtils.isEmpty(dto.getEngraving40WDiode()) && !"/".equals(dto.getEngraving40WDiode())){
                    materialParam40Diode.setEngrave(dto.getEngraving40WDiode());
                }
                if(!StringUtils.isEmpty(dto.getCutting40WDiode()) && !"/".equals(dto.getCutting40WDiode())){
                    materialParam40Diode.setCut(dto.getCutting40WDiode());
                }
                materialParamList.add(materialParam16);
                materialParamList.add(materialParam50);
                materialParamList.add(materialParam75);
                materialParamList.add(materialParam100);
                materialParamList.add(materialParam120);
                materialParamList.add(materialParam200);
                materialParamList.add(materialParam220);
                materialParamList.add(materialParam400);
                materialParamList.add(materialParam600);
                materialParamList.add(materialParam2IR);
                materialParamList.add(materialParam20Fiber);
                materialParamList.add(materialParam20Diode);
                materialParamList.add(materialParam40Diode);

                materialParamList.forEach(item -> {
                    item.setMaterialId(material.getId());
                    item.setCreator(String.valueOf(userId));
                    item.setCreateTime(now);
                    item.setUpdater(String.valueOf(userId));
                    item.setUpdateTime(now);
                });
                materialParamMapper.batchInsertMaterialParams(materialParamList);
            });
        } catch (IOException e) {
            log.error("耗材列表-文件解析失败", e);
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_PARSE_FILE_FAIL);
        }
        //刷新缓存
        refreshMaterialCache();
    }

    /**
     * 耗材管理-下拉框查询(用于项目管理-新增、编辑、数据回显等)
     * @return
     */
    @Override
    public List<BackMaterialSelectVo> materialSelects(String keyword) {
        List<BackMaterialSelectVo> materialSelectVoList = new ArrayList<>();
        QueryWrapper<FalconMaterial> materialQuery = new QueryWrapper<>();
        Optional.ofNullable(keyword).ifPresent(kw -> materialQuery.and(wrapper ->
                wrapper.like("name_cn", kw)
                        .or().like("thickness", kw)
        ));
        List<FalconMaterial> materialList = materialMapper.selectList(materialQuery);
        if(!CollectionUtils.isEmpty(materialList)){
            materialSelectVoList = materialList.stream()
                    .map(material -> {
                        BackMaterialSelectVo vo = new BackMaterialSelectVo();
                        vo.setValue(material.getId());
                        vo.setName(material.getNameCn() + "-" + material.getThickness() + "mm");
                        return vo;
                    })
                    .toList();
        }
        return materialSelectVoList;
    }

    @Override
    public List<BackMaterialTypeSelectVo> materialTypeSelects(String keyword) {
        List<BackMaterialTypeSelectVo> materialTypeSelectVoList = new ArrayList<>();
        QueryWrapper<FalconMaterialType> materialTypeQuery = new QueryWrapper<>();
        Optional.ofNullable(keyword).ifPresent(kw -> materialTypeQuery.and(wrapper ->
                wrapper.like("material_type_cn", kw)
        ));
        List<FalconMaterialType> materialTypeList = materialTypeMapper.selectList(materialTypeQuery);
        if(!CollectionUtils.isEmpty(materialTypeList)){
            materialTypeSelectVoList = materialTypeList.stream()
                   .map(materialType -> {
                        BackMaterialTypeSelectVo vo = new BackMaterialTypeSelectVo();
                        vo.setValue(materialType.getMaterialTypeEn());
                        vo.setName(materialType.getMaterialType());
                        return vo;
                    })
                   .toList();
        }
        return materialTypeSelectVoList;
    }

    @Override
    public String downloadTemplate() {
        return String.format("%s%s", environmentUtils.getCustomDomainByEnv("common"),
                "material/template/耗材导入模版.xlsx");
    }

    /**
     * 耗材管理-更新状态
     * @return
     */
    @Override
    public void updateStatus(BackMaterialUpdateStatusDto updateStatusDto) {
        LocalDateTime now = LocalDateTime.now();
        //获取用户ID
        String userId = tokenManage.getUserId();
        QueryWrapper<FalconMaterial> materialQuery = new QueryWrapper<>();
        materialQuery.eq("id", updateStatusDto.getId());
        FalconMaterial material = materialMapper.selectOne(materialQuery);
        if(Objects.isNull(material)){
            throw new ApiException(ResultEnum.BACKEND_CONTENT_MATERIAL_NOT_EXIST_ERROR);
        }
        FalconMaterial updateMaterial = new FalconMaterial();
        updateMaterial.setId(updateStatusDto.getId());
        updateMaterial.setStatus(updateStatusDto.getStatus());
        updateMaterial.setUpdater(userId);
        updateMaterial.setUpdateTime(now);
        materialMapper.updateById(updateMaterial);
    }

    /**
     * 刷新画布材料缓存
     */
    private void refreshMaterialCache(){
        List<AppMaterialTypeVo> materialTypeVos = materialService.getMaterialList();
        if(!CollectionUtils.isEmpty(materialTypeVos)){
            String materialsCache = JsonUtils.objectToJson(materialTypeVos);
            redisTemplate.opsForValue().set(RedisKey.CANVAS_MATERIAL_INFO,materialsCache);
        }
    }
}
