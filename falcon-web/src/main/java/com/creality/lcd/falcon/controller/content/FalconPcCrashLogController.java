package com.creality.lcd.falcon.controller.content;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.CrashLogSearchDto;
import com.creality.lcd.falcon.pojo.vo.pc.FalconCreashLogSearchParamVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcCrashLogPageVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionPageVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.content.FalconPcCrashLogService;
import com.creality.lcd.falcon.service.content.FalconPcVersionService;

import cn.hutool.core.util.StrUtil;

/**
 * PC崩溃日志管理
 * @Author: zhangshaoming
 * @Date: 2025/04/23 09:55
 * @Description:
 */
@RestController
@RequestMapping("/pc-crash-log")
public class FalconPcCrashLogController {

    @Autowired
    private FalconPcCrashLogService pcCrashLogService;
    
    @Autowired
    private FalconPcVersionService falconPcVersionService;

    /**
     * 崩溃日志-查询条件-下拉框数据接口
     */
    @PostMapping("/searchParams")
    public BaseResponse<FalconCreashLogSearchParamVo> searchParams(){
    	FalconCreashLogSearchParamVo vo=new FalconCreashLogSearchParamVo();
    	//pc版本
    	List<String> softVersionList=falconPcVersionService.searchPcVersionList();
    	vo.setSoftVersionList(cleanNullItem(softVersionList));
    	//系统版本
    	List<String> osVersionList=pcCrashLogService.searchOsVersionList();
    	vo.setOsVersionList(cleanNullItem(osVersionList));
    	//系统归属地
    	List<String> ipAreaList=pcCrashLogService.searchIpAreaList();
    	vo.setIpAreaList(cleanNullItem(ipAreaList));
    	//设备名称
    	List<String> deviceNameList=pcCrashLogService.searchDeviceNameList();
    	vo.setDeviceNameList(cleanNullItem(deviceNameList));
    	
    	return BaseResponse.success(vo);
    }
    
    private List<String> cleanNullItem(List<String> list){
    	return Optional.ofNullable(list).orElse(new ArrayList<String>()).stream().filter(name->{
    		return StrUtil.isNotEmpty(name);
    	}).collect(Collectors.toList());
    }
    
    
    /**
     * PC崩溃日志管理-分页查询
     * @param pageNum 页码
     * @param pageSize 每页查询数据条数
     * @param fileName 文件名称
     * @param uploadDateStart 上传开始时间
     * @param uploadDateEnd 上传结束时间
     * @return
     */
    @GetMapping(value="/page")
    public BaseResponse<PageResult<BackPcCrashLogPageVo>> pageCrashLog(
            @RequestParam(value = "pageNum",defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
            
            @RequestParam(value = "deviceName",required = false) String deviceName,
            @RequestParam(value = "softwareVersion",required = false) String softwareVersion,
            @RequestParam(value = "osVersion",required = false) String osVersion,
            @RequestParam(value = "ipArea",required = false) String ipArea,
            @RequestParam(value = "fileName",required = false) String fileName,
            
            @RequestParam(value = "uploadDateStart",required = false) Date uploadDateStart,
            @RequestParam(value = "uploadDateEnd",required = false) Date uploadDateEnd)
    {
    	CrashLogSearchDto searchDto=new CrashLogSearchDto();
    	searchDto.setDeviceName(deviceName);
    	searchDto.setSoftwareVersion(softwareVersion);
    	searchDto.setOsVersion(osVersion);
    	searchDto.setIpArea(ipArea);
    	searchDto.setFileName(fileName);
    	searchDto.setUploadDateStart(uploadDateStart);
    	searchDto.setUploadDateEnd(uploadDateEnd);
    	
        PageResult<BackPcCrashLogPageVo> pageResult = pcCrashLogService.pageCrashLog(
                pageNum, pageSize, searchDto);
        return BaseResponse.success(pageResult);
    }

    /**
     * PC崩溃日志管理-删除
     * @param id 日志主键id
     * @return
     */
    @DeleteMapping(value="/del")
    public BaseResponse<PageResult<BackPcVersionPageVo>> delCrashLog(
            @RequestParam(value = "id") Long id)
    {
        pcCrashLogService.delCrashLog(id);
        return BaseResponse.success();
    }
}
