package com.creality.lcd.falcon.service.statistic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.creality.lcd.falcon.enums.EnumRegisterType;
import com.creality.lcd.falcon.mappers.account.FalconAccountMapper;
import com.creality.lcd.falcon.mappers.web.FalconHomeDataScreenMapper;
import com.creality.lcd.falcon.pojo.entity.web.FalconHomeDataScreen;
import com.creality.lcd.falcon.pojo.vo.web.BackHomeDataScreenVo;
import com.creality.lcd.falcon.pojo.vo.web.RegisterItem;
import com.creality.lcd.falcon.pojo.vo.web.RegisterUserNumGroup;
import com.creality.lcd.falcon.service.statistic.FalconDataScreenService;

/**
 * @Author: zhangshaoming
 * @Date: 2025/02/28 15:03
 * @Description:
 */
@Service
public class FalconDataScreenServiceImpl implements FalconDataScreenService {

    @Autowired
    private FalconHomeDataScreenMapper dataScreenMapper;

    @Autowired
    private FalconAccountMapper accountMapper;

    /**
     * 数据总览
     * @return
     */
    @Override
    public BackHomeDataScreenVo dataScreen(Date startDate, Date endDate) {
        BackHomeDataScreenVo result = new BackHomeDataScreenVo();
        // 1. 查询账号注册数,有日期则查区间的，没日期则查所有
        result.setRegisteredNum(0L);
        /*
         * 统计各个渠道注册数量
         */
        setRegisterData(result,startDate,endDate);
        
        // 2. 构建数据总览查询条件
        QueryWrapper<FalconHomeDataScreen> dataScreenQuery = new QueryWrapper<>();
        Optional.ofNullable(startDate).ifPresent(sd -> dataScreenQuery.ge("stat_date", sd));
        Optional.ofNullable(endDate).ifPresent(ed -> dataScreenQuery.le("stat_date", ed));
        // 3. 查询数据总览表数据
        List<FalconHomeDataScreen> homeDataScreenList = dataScreenMapper.selectList(dataScreenQuery);
        // 4. 构建返回数据
        if(CollectionUtils.isEmpty(homeDataScreenList)){
            return result;
        }
        homeDataScreenList.forEach(dataScreen -> {
            result.setWebVisitNum(result.getWebVisitNum() + dataScreen.getWebVisitNum());
            result.setWebActiveUserNum(result.getWebActiveUserNum() + dataScreen.getWebActiveUserNum());
            result.setPcDownloadNum(result.getPcDownloadNum() + dataScreen.getPcDownloadNum());
            result.setAppVisitNum(result.getAppVisitNum() + dataScreen.getAppVisitNum());
            result.setAppActiveUserNum(result.getAppActiveUserNum() + dataScreen.getAppActiveUserNum());
        });
        return result;
    }
    /**
     * 注册数量按各个渠道分组进行统计
     * @param result
     * @param startDate
     * @param endDate
     */
	private void setRegisterData(BackHomeDataScreenVo result, Date startDate, Date endDate) {
		List<RegisterItem> itemList=new ArrayList<RegisterItem>();
        result.setRegisterItems(itemList);
        
        List<RegisterUserNumGroup> dataList=accountMapper.countByOrigin(startDate,endDate);
        if(!CollectionUtils.isEmpty(dataList)) {
        	Map<String,Long> numMap=dataList.stream().collect(Collectors.toMap(RegisterUserNumGroup::getOrigin, RegisterUserNumGroup::getNum));
        	Long numAll=0L;
        	for(EnumRegisterType type:EnumRegisterType.values()) {
        		RegisterItem item=new RegisterItem();
        		item.setOrigin(type.getType());
        		Long num=numMap.get(type.getType());
        		item.setNum(num!=null?num:0L);
        		itemList.add(item);
        		
        		numAll+=num!=null?num:0L;
        	}
        	result.setRegisteredNum(numAll);
        }else {
        	for(EnumRegisterType type:EnumRegisterType.values()) {
        		RegisterItem item=new RegisterItem();
        		item.setOrigin(type.getType());
        		item.setNum(0L);
        		itemList.add(item);
        	}
        }
		
	}
}
