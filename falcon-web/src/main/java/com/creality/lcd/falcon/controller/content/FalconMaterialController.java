package com.creality.lcd.falcon.controller.content;

import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialAddDto;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialModifyDto;
import com.creality.lcd.falcon.pojo.dto.web.BackMaterialUpdateStatusDto;
import com.creality.lcd.falcon.pojo.vo.web.*;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.content.FalconMaterialService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 耗材管理
 * @Author: zhangshaoming
 * @Date: 2024/12/9 14:05
 * @Description: 耗材管理
 */
@RestController
@RequestMapping("/material")
public class FalconMaterialController {

    private static final Logger log = LogManager.getLogger(FalconMaterialController.class);

    @Autowired
    private FalconMaterialService materialService;

    /**
     * 耗材管理-分页查询
     * @return
     */
    @GetMapping(value="/page")
    public BaseResponse<PageResult<BackMaterialPageVo>> pageMaterials(
            @RequestParam(value = "pageNum",defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
            @RequestParam(value = "keyword",required = false) String keyword,
            @RequestParam(value = "materialType",required = false) String materialType,
            @RequestParam(value = "status",required = false) Byte status,
            @RequestParam(value = "startDate",required = false) Date startDate,
            @RequestParam(value = "endDate",required = false) Date endDate)


    {
        PageResult<BackMaterialPageVo> pageResult = materialService.pageMaterials(
                pageNum, pageSize,keyword,materialType,status,startDate,endDate);
        return BaseResponse.success(pageResult);
    }

    /**
     * 耗材管理-新增
     * @return
     */
    @PostMapping(value="/add")
    public BaseResponse<BackMaterialVo> addMaterial(@RequestBody @Valid BackMaterialAddDto materialAddDto)
    {
        materialService.addMaterial(materialAddDto);
        return BaseResponse.success();
    }

    /**
     * 耗材管理-数据回显
     * @return
     */
    @GetMapping(value="/detail")
    public BaseResponse<BackMaterialVo> detailMaterial(@RequestParam(value = "id") Long id)
    {
        BackMaterialVo materialVo = materialService.detailMaterial(id);
        return BaseResponse.success(materialVo);
    }

    /**
     * 耗材管理-更新
     * @return
     */
    @PostMapping(value="/update")
    public BaseResponse<BackMaterialVo> updateMaterial(
            @RequestBody @Valid BackMaterialModifyDto materialModifyDto)
    {
        materialModifyDto.setMaterialId(null);
        materialService.updateMaterial(materialModifyDto);
        return BaseResponse.success();
    }

    /**
     * 耗材管理-删除
     * @return
     */
    @DeleteMapping(value="/del")
    public BaseResponse<BackMaterialVo> delMaterial(@RequestParam(value = "id") Long id)
    {
        materialService.delMaterial(id);
        return BaseResponse.success();
    }

    /**
     * 耗材管理-Excel导入
     * @return
     */
    @PostMapping(value="/import")
    public BaseResponse<BackMaterialVo> importMaterials(@RequestParam("file") MultipartFile file)
    {
        materialService.importMaterials(file);
        return BaseResponse.success();
    }

    /**
     * 耗材管理-材料下拉框查询(用于项目管理-新增、编辑、数据回显等)
     * @return
     */
    @GetMapping(value="/select")
    public BaseResponse<List<BackMaterialSelectVo>> materialSelects(
            @RequestParam(value = "keyword",required = false) String keyword)
    {
        List<BackMaterialSelectVo> materialSelectVos = materialService.materialSelects(keyword);
        return BaseResponse.success(materialSelectVos);
    }

    /**
     * 耗材管理-材料类型下拉
     * @return
     */
    @GetMapping(value="/type-select")
    public BaseResponse<List<BackMaterialTypeSelectVo>> materialTypeSelects(
            @RequestParam(value = "keyword",required = false) String keyword)
    {
        List<BackMaterialTypeSelectVo> materialSelectVos = materialService.materialTypeSelects(keyword);
        return BaseResponse.success(materialSelectVos);
    }

    /**
     * 耗材管理-导入模版下载
     * @return
     */
    @GetMapping(value="/template-download")
    public BaseResponse<String> downloadTemplate() {
        String templateUrl = materialService.downloadTemplate();
        return BaseResponse.success(ResultEnum.SUCCESS.getMsg(),templateUrl);
    }

    /**
     * 耗材管理-更新状态
     * @return
     */
    @PostMapping(value="/updateStatus")
    public BaseResponse<String> updateStatus(@RequestBody @Valid BackMaterialUpdateStatusDto updateStatusDto) {
        materialService.updateStatus(updateStatusDto);
        return BaseResponse.success();
    }
}
