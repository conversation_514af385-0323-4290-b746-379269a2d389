package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.interceptor.TokenInterceptor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger log = LogManager.getLogger(WebConfig.class);

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义要排除的路径
        List<String> excludePaths = Arrays.asList(
                "/web-api/auth/login",
                "/web-api/test"
        );
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(excludePaths);// 排除放行接口
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        //解决18位Id过长，导致javascript无法解析为integer的问题
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);
        List<MediaType> mediaTypeList = jackson2HttpMessageConverter.getSupportedMediaTypes();
        mediaTypeList.forEach(mediaType -> log.info("before supportedMediaTypes:{}", mediaType.toString()));
        List<MediaType> newMediaTypeList = new ArrayList<>(mediaTypeList);
        newMediaTypeList.add(MediaType.TEXT_EVENT_STREAM);
        jackson2HttpMessageConverter.setSupportedMediaTypes(newMediaTypeList);
        mediaTypeList = jackson2HttpMessageConverter.getSupportedMediaTypes();
        mediaTypeList.forEach(mediaType -> log.info("after supportedMediaTypes:{}", mediaType.toString()));
        converters.add(0, jackson2HttpMessageConverter);
    }
}
