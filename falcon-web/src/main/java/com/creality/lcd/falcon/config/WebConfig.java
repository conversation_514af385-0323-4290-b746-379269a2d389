package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.interceptor.TokenInterceptor;
import com.creality.lcd.falcon.util.JacksonUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger log = LogManager.getLogger(WebConfig.class);

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义要排除的路径
        List<String> excludePaths = Arrays.asList(
                "/web-api/auth/login",
                "/web-api/test"
        );
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(excludePaths);// 排除放行接口
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = JacksonUtil.getObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        //解决18位Id过长，导致javascript无法解析为integer的问题
        simpleModule.addSerializer(Long.class, new ThresholdLongSerializer());
        simpleModule.addSerializer(Long.TYPE, new ThresholdLongSerializer());
        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);
        List<MediaType> mediaTypeList = jackson2HttpMessageConverter.getSupportedMediaTypes();
        mediaTypeList.forEach(mediaType -> log.info("before supportedMediaTypes:{}", mediaType.toString()));
        List<MediaType> newMediaTypeList = new ArrayList<>(mediaTypeList);
        newMediaTypeList.add(MediaType.TEXT_EVENT_STREAM);
        jackson2HttpMessageConverter.setSupportedMediaTypes(newMediaTypeList);
        mediaTypeList = jackson2HttpMessageConverter.getSupportedMediaTypes();
        mediaTypeList.forEach(mediaType -> log.info("after supportedMediaTypes:{}", mediaType.toString()));
        converters.add(0, jackson2HttpMessageConverter);
    }

    // 自定义Long序列化器
    private static class ThresholdLongSerializer extends JsonSerializer<Long> {
        // javascript精度丢失阈值
        private static final long FIFTEEN_DIGIT_THRESHOLD = 9007199254740991L;

        @Override
        public void serialize(Long value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            // 处理null值（Long类型可能为null）
            if (value == null) {
                gen.writeNull();
                return;
            }
            // 当|value| > javascript精度丢失阈值 时序列化为字符串
            if (Math.abs(value) > FIFTEEN_DIGIT_THRESHOLD) {
                gen.writeString(value.toString());
            }
            // 否则按原样输出数字
            else {
                gen.writeNumber(value);
            }
        }
    }
}
