package com.creality.lcd.falcon.service.content.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.mappers.web.FalconPcVersionMapper;
import com.creality.lcd.falcon.mappers.web.FalconVersionFileLogMapper;
import com.creality.lcd.falcon.mappers.web.FalconVersionFileMapper;
import com.creality.lcd.falcon.page.PageResult;
import com.creality.lcd.falcon.pojo.dto.web.BackPcVersionAddDto;
import com.creality.lcd.falcon.pojo.dto.web.BackPcVersionUpdateStatusDto;
import com.creality.lcd.falcon.pojo.dto.web.BackVerisonFileLogUpdateDto;
import com.creality.lcd.falcon.pojo.entity.web.FalconPcVersion;
import com.creality.lcd.falcon.pojo.entity.web.FalconStaffs;
import com.creality.lcd.falcon.pojo.entity.web.FalconVersionFile;
import com.creality.lcd.falcon.pojo.entity.web.FalconVersionFileLog;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionDetailVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionNoteVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionPackVo;
import com.creality.lcd.falcon.pojo.vo.web.BackPcVersionPageVo;
import com.creality.lcd.falcon.service.content.FalconPcVersionService;
import com.creality.lcd.falcon.service.system.FalconStaffService;
import com.creality.lcd.falcon.util.EnvironmentUtils;
import com.creality.lcd.falcon.util.OssUtils;
import com.creality.lcd.falcon.util.S3Utils;
import com.creality.lcd.falcon.utils.FileUtils;
import com.creality.lcd.falcon.utils.TimeUtils;
import com.creality.lcd.falcon.utils.TokenManage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.utils.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PC-软件包管理(WIN/MAC)
 * @Author: zhangshaoming
 * @Date: 2025/01/14 16:28
 * @Description:
 */
@Service
public class FalconPcVersionServiceImpl implements FalconPcVersionService {

    private static final Logger log = LogManager.getLogger(FalconPcVersionServiceImpl.class);

    @Autowired
    private FalconPcVersionMapper pcVersionMapper;

    @Autowired
    private FalconVersionFileMapper versionFileMapper;

    @Autowired
    private FalconVersionFileLogMapper versionFileLogMapper;

    @Autowired
    private FalconStaffService staffService;

    @Autowired
    private TokenManage tokenManage;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private Environment environment;

    @Autowired
    private EnvironmentUtils environmentUtils;

    /**
     * PC版本管理-分页查询
     * @param pageNum 页码
     * @param pageSize 页记录数
     * @param keyword 关键字
     * @return 分页结果
     */
    @Override
    public PageResult<BackPcVersionPageVo> pagePcVersions(int pageNum, int pageSize, String keyword) {
        // 设置分页参数
        Page<FalconPcVersion> page = new Page<>(pageNum, pageSize);

        // 创建查询条件
        QueryWrapper<FalconPcVersion> pcVersionQuery = new QueryWrapper<>();
        // 如果 channel 不为 null 或空，则添加条件
        if (!StringUtils.isBlank(keyword)) {
            pcVersionQuery.and(wrapper ->
                    wrapper.like("version", keyword)
                            .or().like("name", keyword));
        }

        // 按创建时间降序排序
        pcVersionQuery.orderByDesc("version");

        // 查询数据
        Page<FalconPcVersion> resultPage = pcVersionMapper.selectPage(page, pcVersionQuery);

        // 如果有结果，构建数据
        List<BackPcVersionPageVo> softwarePageVoList = Optional.ofNullable(resultPage.getRecords())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToBackSoftwarePageVo)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(resultPage.getTotal(), softwarePageVoList);
    }

    /**
     * 转换数据
     * @param pcVersion PC版本实体
     * @return 前端渲染VO
     */
    private BackPcVersionPageVo convertToBackSoftwarePageVo(FalconPcVersion pcVersion) {
        // 通过创建者和更新者的 ID 获取名字
        Map<String, FalconStaffs> staffsMap = staffService.getStaffsByStaffIds(
                Arrays.asList(pcVersion.getCreator(),pcVersion.getUpdater()));

        // 构建 VO 对象
        BackPcVersionPageVo softwarePageVo = new BackPcVersionPageVo();
        softwarePageVo.setId(pcVersion.getId());
        softwarePageVo.setName(pcVersion.getName());
        softwarePageVo.setVersion(pcVersion.getVersion());
        softwarePageVo.setStatus(pcVersion.getStatus());
        softwarePageVo.setCreator(Optional.ofNullable(staffsMap.get(pcVersion.getCreator()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        softwarePageVo.setCreateTime(TimeUtils.transferLocalDateTimeToStr(pcVersion.getCreateTime()));
        softwarePageVo.setUpdater(Optional.ofNullable(staffsMap.get(pcVersion.getUpdater()))
                .map(FalconStaffs::getUserName)
                .orElse(""));
        softwarePageVo.setUpdateTime(TimeUtils.transferLocalDateTimeToStr(pcVersion.getUpdateTime()));

        return softwarePageVo;
    }

    /**
     * PC版本管理-新建版本
     */
    @Override
    public void addPcVersion(BackPcVersionAddDto softwareAddDto) {
        LocalDateTime now = LocalDateTime.now();
        String userId = tokenManage.getUserId();
        QueryWrapper<FalconPcVersion> pcVersionQuery = new QueryWrapper<>();
        pcVersionQuery.eq("name", softwareAddDto.getName());
        pcVersionQuery.eq("version", softwareAddDto.getVersion());
        FalconPcVersion pcVersion = pcVersionMapper.selectOne(pcVersionQuery);
        if(Objects.nonNull(pcVersion)) {
            throw new ApiException(ResultEnum.BACKEND_CONTENT_PC_VERSION_EXIST_ERROR);
        }
        pcVersion = new FalconPcVersion();
        pcVersion.setName(softwareAddDto.getName());
        pcVersion.setVersion(softwareAddDto.getVersion());
        pcVersion.setStatus((byte)0);
        pcVersion.setCreator(userId);
        pcVersion.setCreateTime(now);
        pcVersion.setUpdater(userId);
        pcVersion.setUpdateTime(now);
        pcVersionMapper.insert(pcVersion);
    }

    /**
     * PC版本管理-删除
     */
    @Override
    public void delPcVersion(Long id) {
        FalconPcVersion pcVersion = pcVersionMapper.selectById(id);
        if(Objects.isNull(pcVersion)) {
            throw new ApiException(ResultEnum.BACKEND_CONTENT_PC_VERSION_NOT_EXIST_ERROR);
        }
        //删除关联表-更新日志表
        versionFileLogMapper.delByVersionId(id);
        QueryWrapper<FalconVersionFile> versionFileQuery = new QueryWrapper<>();
        versionFileQuery.eq("version_id", id);
        List<FalconVersionFile> versionFiles = versionFileMapper.selectList(versionFileQuery);
        if(!CollectionUtils.isEmpty(versionFiles)){
            List<String> fileUrls = versionFiles.stream().map(FalconVersionFile::getUrl).toList();
            String currentEnv = environment.getProperty("spring.profiles.active");
            log.info("PC版本管理-删除-当前环境为:{}",currentEnv);
            //释放文件服务资源
            if("test".equals(currentEnv)){
                ossUtils.deleteObjectsByUrls(fileUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            }else{
                s3Utils.deleteObjectByUrls(fileUrls,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            }
            //删除关联表-安装包表
            versionFileMapper.delByVersionId(id);
        }
        //删除PC版本表
        pcVersionMapper.deleteById(id);
    }

    /**
     * PC版本管理-修改状态(上架、下架)
     */
    @Override
    public void updateStatus(BackPcVersionUpdateStatusDto updateStatusDto) {
        LocalDateTime now = LocalDateTime.now();
        String userId = tokenManage.getUserId();
        pcVersionMapper.updateStatus(updateStatusDto.getId(), updateStatusDto.getStatus(), userId, now);
    }

    /**
     * PC版本管理-数据回显
     * @return PC版本详情
     */
    @Override
    public BackPcVersionDetailVo detailPcVersion(Long id) {
        BackPcVersionDetailVo detailVo = new BackPcVersionDetailVo();
        //查询安装包表
        QueryWrapper<FalconVersionFile> versionFileQuery = new QueryWrapper<>();
        versionFileQuery.eq("version_id", id);
        List<FalconVersionFile> versionFiles = versionFileMapper.selectList(versionFileQuery);
        detailVo.setPackList(CollectionUtils.isEmpty(versionFiles)
                ? Collections.emptyList() : convertToPackList(versionFiles));
        //查询更新日志表
        QueryWrapper<FalconVersionFileLog> versionFileLogQuery = new QueryWrapper<>();
        versionFileLogQuery.eq("version_id", id);
        List<FalconVersionFileLog> versionFileLogs = versionFileLogMapper.selectList(versionFileLogQuery);
        List<BackPcVersionNoteVo> noteList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(versionFileLogs)){
            for (FalconVersionFileLog versionFileLog : versionFileLogs) {
                BackPcVersionNoteVo noteVo = new BackPcVersionNoteVo();
                noteVo.setLanguage(versionFileLog.getLanguage());
                noteVo.setNote(versionFileLog.getNote());
                noteList.add(noteVo);
            }
        }
        detailVo.setNoteList(noteList);
        return detailVo;
    }

    /**
     * 转换数据
     * @param versionFiles PC版本文件列表
     * @return PC版本文件列表(前端渲染VO)
     */
    public List<BackPcVersionPackVo> convertToPackList(List<FalconVersionFile> versionFiles) {
        Map<String,FalconStaffs> allStaffsMap = staffService.getAllStaffs();

        // 使用流转换
        return versionFiles.stream()
                .filter(Objects::nonNull) // 过滤掉空值
                .map(file -> {
                    BackPcVersionPackVo packVo = new BackPcVersionPackVo();
                    packVo.setOs(file.getOs());
                    packVo.setFileName(file.getFileName());
                    packVo.setMd5(file.getMd5());
                    packVo.setSize(file.getSize());
                    packVo.setUrl(file.getUrl());
                    String uploader = Optional.ofNullable(allStaffsMap.get(file.getUploader()))
                            .map(FalconStaffs::getUserName)
                            .orElse("");
                    packVo.setUploader(uploader);
                    // 格式化 uploadTime，处理空值
                    packVo.setUploadTime(TimeUtils.transferLocalDateTimeToStr(file.getUploadTime()));
                    return packVo;
                })
                .collect(Collectors.toList());
    }

    /**
     * PC版本管理-上传软件包
     */
    @Override
    public void uploadPack(Long versionId, String os, MultipartFile packFile) {
        final LocalDateTime now = LocalDateTime.now();
        final String userId = tokenManage.getUserId();

        // 验证版本是否存在
        FalconPcVersion pcVersion = pcVersionMapper.selectById(versionId);
        if (Objects.isNull(pcVersion)) throw new ApiException(ResultEnum.BACKEND_CONTENT_PC_VERSION_NOT_EXIST_ERROR);

        // 查询或创建文件记录
        QueryWrapper<FalconVersionFile> query = new QueryWrapper<FalconVersionFile>()
                .eq("version_id", versionId)
                .eq("os", os);
        FalconVersionFile file = versionFileMapper.selectOne(query);
        boolean isUpdate = Objects.nonNull(file);

        // 统一存储服务选择
        String currentEnv = environment.getProperty("spring.profiles.active");
        log.info("PC版本管理-上传软件包-当前环境：{}", currentEnv);
        boolean isTestEnv = "test".equals(currentEnv);

        // 文件上传处理
        String fileName = packFile.getOriginalFilename();
        String objectName = String.format("software/%s/%s/%s", pcVersion.getVersion(), os, fileName);
        // 执行文件上传并获取元数据
        Map<String, String> fileData;
        String fileUrl;
        if (isTestEnv) {
            fileUrl = ossUtils.putObject(objectName, packFile,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            fileData = ossUtils.getObjectMeta(environmentUtils.getBucketNameByEnv("common"), objectName);
        } else {
            fileUrl = s3Utils.putObject(objectName, packFile,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            fileData = s3Utils.getObjectDetails(environmentUtils.getBucketNameByEnv("common"), objectName);
        }

        // 统一设置文件记录属性
        if (!isUpdate) file = new FalconVersionFile();
        file.setVersionId(versionId);
        file.setOs(os);
        file.setFileName(fileName);
        file.setMd5(fileData.get("fileMd5"));
        file.setSize(FileUtils.convertSize(fileData.get("fileSize")));
        file.setUrl(fileUrl);
        file.setUploader(userId);
        file.setUploadTime(now);;

        // 保存记录
        if (isUpdate) {
            versionFileMapper.update(file, query);
        } else {
            versionFileMapper.insert(file);
        }
    }

    /**
     * PC版本管理-插入更新日志
     */
    @Override
    public void updateLogs(BackVerisonFileLogUpdateDto fileLogUpdateDto){
        LocalDateTime now = LocalDateTime.now();
        String userId = tokenManage.getUserId();
        Long versionId = fileLogUpdateDto.getVersionId();
        List<BackPcVersionNoteVo> notes = fileLogUpdateDto.getNotes();
        List<FalconVersionFileLog> updateFileLog = new ArrayList<>();
        List<FalconVersionFileLog> logsToInsert = new ArrayList<>();

        // 获取当前版本的所有语言记录
        QueryWrapper<FalconVersionFileLog> fileLogQuery = new QueryWrapper<>();
        fileLogQuery.eq("version_id", versionId);
        List<FalconVersionFileLog> existingLogs = versionFileLogMapper.selectList(fileLogQuery);
        Map<String, FalconVersionFileLog> existingLogMap = new HashMap<>();

        // 将现有记录存入 Map
        for (FalconVersionFileLog log : existingLogs) {
            existingLogMap.put(log.getLanguage(), log);
        }

        // 准备批量更新和插入的集合

        for (BackPcVersionNoteVo note : notes) {
            if (existingLogMap.containsKey(note.getLanguage())) {
                // 更新已存在的记录
                FalconVersionFileLog existingLog = existingLogMap.get(note.getLanguage());
                existingLog.setNote(note.getNote());
                existingLog.setUpdater(userId);
                existingLog.setUpdateTime(now);
                // 可以在这里添加到更新列表
                updateFileLog.add(existingLog);
            } else {
                // 准备插入的新记录
                FalconVersionFileLog newLog = new FalconVersionFileLog();
                newLog.setVersionId(versionId);
                newLog.setLanguage(note.getLanguage());
                newLog.setNote(note.getNote());
                newLog.setUpdater(userId);
                newLog.setUpdateTime(now);
                logsToInsert.add(newLog);
            }
        }
        // 执行批量插入
        if(!CollectionUtils.isEmpty(logsToInsert)){
            versionFileLogMapper.insertBatch(logsToInsert);
        }
        // 执行批量更新
        if(!CollectionUtils.isEmpty(existingLogs)){
            versionFileLogMapper.updateBatch(updateFileLog);
        }
    }

    /**
     * 查询pc版本
     */
	@Override
	public List<String> searchPcVersionList() {
		QueryWrapper<FalconPcVersion> query=new QueryWrapper<FalconPcVersion>();
		query.orderByDesc("id");
		List<FalconPcVersion> versionList=pcVersionMapper.selectList(query);
		return Optional.ofNullable(versionList).orElse(new ArrayList<FalconPcVersion>()).stream().map(FalconPcVersion::getVersion).collect(Collectors.toList());
		
	}
}
