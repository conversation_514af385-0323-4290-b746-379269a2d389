version: '3.8'

services:
  your-java-app:
    restart: always
    build:
      context: .
      dockerfile: Dockerfile
    image: falcon-web
    container_name: falcon-web
    volumes:
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    ports:
      - "6437:6437"
      - "64370:64370"
    environment:
      - JAVA_OPTS=${JAVA_PROFILE:-Dspring.profiles.active=stage}
      - TZ=Asia/Shanghai
