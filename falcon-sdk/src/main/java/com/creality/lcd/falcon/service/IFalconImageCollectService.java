package com.creality.lcd.falcon.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageCollect;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectCountVo;

public interface IFalconImageCollectService  extends IService<FalconImageCollect> {

	public int addFalconImageCollect(FalconImageCollect falconImageCollect);
	
	public int delFalconImageCollectById(String id);
	
	public List<FalconImageCollect> selFalconImageCollectList(FalconImageCollect falconImageCollect);

	public List<Long> selCollectedIds(String userId, List<Long> imgIds);

	public Integer delFalconImageCollect(String userId, Long imageId);

	public List<ImageCollectCountVo> selCountByImgIds(List<Long> imgIds);
	

}

