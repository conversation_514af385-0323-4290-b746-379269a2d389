package com.creality.lcd.falcon.enums;

public enum EnumRegisterType {
	type_web("WEB","WEB端"),
	type_pc("PC","PC端"),
	type_app("APP","APP端");
	
	private String type;
	
	private String typeName;
	
	EnumRegisterType(String type,String typeName){
		this.type=type;
		this.typeName=typeName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	
}
