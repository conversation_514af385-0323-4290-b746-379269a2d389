package com.creality.lcd.falcon.mappers.pc;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageCollect;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectCountVo;

@Mapper
public interface FalconImageCollectMapper extends BaseMapper<FalconImageCollect> {

	int addFalconImageCollect(FalconImageCollect falconImageCollect);

	int delFalconImageCollectById(String id);

	List<FalconImageCollect> selFalconImageCollectList(FalconImageCollect falconImageCollect);

	List<Long> selCollectedIds(@Param("userId") String userId,@Param("imgIds") List<Long> imgIds);

	Integer delFalconImageCollect(@Param("userId")String userId,@Param("imageId") Long imageId);

	List<ImageCollectCountVo> selCountByImgIds(@Param("imgIds")List<Long> imgIds);


}
