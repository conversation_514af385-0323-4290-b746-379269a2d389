package com.creality.lcd.falcon.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.lcd.falcon.mappers.pc.FalconMaterialUsersMapper;
import com.creality.lcd.falcon.pojo.dto.pc.MaterialUsersSaveDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;
import com.creality.lcd.falcon.service.IFalconMaterialUsersParamService;
import com.creality.lcd.falcon.service.IFalconMaterialUsersService;

import cn.hutool.core.collection.CollectionUtil;

@Service
public class FalconMaterialUsersService extends ServiceImpl<FalconMaterialUsersMapper, FalconMaterialUsers> implements IFalconMaterialUsersService{

	
	@Autowired
	private FalconMaterialUsersMapper falconMaterialUsersMapper;
	@Autowired
	private IFalconMaterialUsersParamService falconMaterialUsersParamService;

	@Override
	public int addFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers) {
		return falconMaterialUsersMapper.addFalconMaterialUsers(falconMaterialUsers);
	}

	@Override
	public int delFalconMaterialUsersById(Long id) {
		return falconMaterialUsersMapper.delFalconMaterialUsersById(id);
	}

	@Override
	public FalconMaterialUsers selFalconMaterialUsersById(String id) {
		return falconMaterialUsersMapper.selectById(id);
	}

	@Override
	public int upFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers) {
		return falconMaterialUsersMapper.upFalconMaterialUsers(falconMaterialUsers);
	}

	@Override
	public List<FalconMaterialUsers> selFalconMaterialUsersList(FalconMaterialUsers falconMaterialUsers) {
		return falconMaterialUsersMapper.selFalconMaterialUsersList(falconMaterialUsers);
	}

	@Override
	public IPage<FalconMaterialUsers> selFalconMaterialUsersPage(FalconMaterialUsers falconMaterialUsers, Page<FalconMaterialUsers> page) {
		return falconMaterialUsersMapper.selFalconMaterialUsersPage(page, falconMaterialUsers);
	}

	@Override
	@Transactional
	public boolean updateMateriaById(MaterialUsersSaveDto dto) {
		FalconMaterialUsers material=new FalconMaterialUsers();
		BeanUtils.copyProperties(dto, material);
		//删除旧的参数
		QueryWrapper<FalconMaterialUsersParam> queryWrapper=new QueryWrapper<FalconMaterialUsersParam>();
		queryWrapper.eq("material_id", dto.getId());
		falconMaterialUsersParamService.remove(queryWrapper);
		List<FalconMaterialUsersParam> paramList=dto.getParamList();
		if(CollectionUtil.isNotEmpty(paramList)) {
			for(FalconMaterialUsersParam p:paramList) {
				p.setId(null);
				p.setMaterialId(material.getId());
			}
			//保存参数
			falconMaterialUsersParamService.saveBatch(paramList);
		}
		return this.updateById(material);
	}

	@Override
	@Transactional
	public void saveMaterial(FalconMaterialUsers meterial, List<FalconMaterialUsersParam> paramList) {
		this.addFalconMaterialUsers(meterial);
		if(CollectionUtil.isNotEmpty(paramList)) {
			Date now=new Date();
			for(FalconMaterialUsersParam p:paramList) {
				p.setMaterialId(meterial.getId());
				p.setCreateTime(now);
			}
			falconMaterialUsersParamService.saveBatchParam(paramList);
		}
	}
}
