package com.creality.lcd.falcon.pojo.entity.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 15:49
 * @Description: 图像管理实体
 */
@Data
@TableName("falcon_image_gallery")
public class FalconImageGallery {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "category")
    private String category;

    @TableField(value = "label")
    private String label;

    @TableField(value = "image_name")
    private String imageName;

    @TableField(value = "image_url")
    private String imageUrl;
    
    @TableField(value = "collect_num")
    private Integer collectNum;

    @TableField(value = "creator")
    private String creator;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "updater")
    private String updater;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
