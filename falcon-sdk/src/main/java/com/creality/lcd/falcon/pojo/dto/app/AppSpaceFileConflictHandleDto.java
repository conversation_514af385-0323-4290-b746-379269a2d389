package com.creality.lcd.falcon.pojo.dto.app;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Author: tiantao
 * @Date: 2025/1/10 22:00
 * @Description: 处理文件重名冲突DTO
 */
@Data
public class AppSpaceFileConflictHandleDto {

    @NotBlank(message = "场景标识不能为空")
    private String scene;

    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @NotBlank(message = "操作类型不能为空")
    private String action; // replace: 替换, add: 新增, cancel: 取消

    private Integer fdsSize = 0;

    @NotBlank(message = "fds工程文件不能为空")
    private String fdsPath;

    @NotBlank(message = "fds缩略图不能为空")
    private String fdsImage;

    private String gcodePath;
}
