package com.creality.lcd.falcon.pojo.entity.pc;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 系统菜单
 * <AUTHOR>
 */
@Data
public class FalconMaterialUsers implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键id*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**用户id*/
    private String userId;
    /**材料名称*/
    @NotNull(message = "materialName can not be null")
    @Length(max = 100,message = "materialName max length is 100")
    private String materialName;
    /**厚度*/
    private BigDecimal thickness;
    /**1非标准0标准*/
    @NotNull(message = "thicknessState can not be null")
    private Integer thicknessState;
    /**长度*/
    private BigDecimal materialLength;
    /**宽度*/
    private BigDecimal materialWidth;
    /**1非标准0标准*/
    @NotNull(message = "sizeState can not be null")
    private Integer sizeState;
    /**材料图片*/
    @NotNull(message = "materialImg can not be null")
    private String materialImg;
    /**删除状态0未删除1删除*/
    private Integer delState;
    /**创建时间*/
    private Date createTime;
}
