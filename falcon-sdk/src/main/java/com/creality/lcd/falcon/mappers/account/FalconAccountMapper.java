package com.creality.lcd.falcon.mappers.account;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.vo.web.RegisterUserNumGroup;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: tiantao
 * @Date: 2024/12/17 11:50
 * @Description: 评论 mapper
 */
@Mapper
public interface FalconAccountMapper extends BaseMapper<FalconAccount> {

	List<RegisterUserNumGroup> countByOrigin(@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
