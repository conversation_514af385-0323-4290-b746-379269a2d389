package com.creality.lcd.falcon.util;

import com.creality.lcd.falcon.config.MqttLifecycleEvents;
import com.creality.lcd.falcon.config.MqttPublishEvents;
import com.creality.lcd.falcon.properties.MqttSdkProperties;
import com.creality.lcd.falcon.service.MqttResponseService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awssdk.crt.mqtt5.Mqtt5Client;
import software.amazon.awssdk.crt.mqtt5.QOS;
import software.amazon.awssdk.crt.mqtt5.packets.ConnectPacket;
import software.amazon.awssdk.crt.mqtt5.packets.PublishPacket;
import software.amazon.awssdk.crt.mqtt5.packets.SubAckPacket;
import software.amazon.awssdk.crt.mqtt5.packets.SubscribePacket;
import software.amazon.awssdk.iot.AwsIotMqtt5ClientBuilder;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @Author: tiantao
 * @Date: 2025/2/12 17:26
 * @Description: MQTT 客户端包装类，对外提供操作方法
 */
public class MqttSdkClient {
    private static final Logger log = LogManager.getLogger(MqttSdkClient.class);
    private final Mqtt5Client client;
    private final MqttResponseService mqttResponseService;
    private final MqttLifecycleEvents lifecycleEvents;
    private final MqttPublishEvents publishEvents;

    public MqttSdkClient(MqttSdkProperties props,
                         MqttResponseService mqttResponseService,
                         MqttLifecycleEvents lifecycleEvents,
                         MqttPublishEvents publishEvents) {

        this.mqttResponseService = mqttResponseService;
        this.lifecycleEvents = lifecycleEvents;
        this.publishEvents = publishEvents;

        // 1) 构建 Builder
        AwsIotMqtt5ClientBuilder builder = AwsIotMqtt5ClientBuilder.newDirectMqttBuilderWithMtlsFromPath(
                props.getEndpoint(),
                props.getCertPath(),
                props.getPrivateKeyPath()
        );
        // 如果需要 root CA，则可以：
        // builder.withCertificateAuthorityFromPath(null, props.getCaPath());

        // 2) 设置连接包 (clientId)
        ConnectPacket.ConnectPacketBuilder cpb = new ConnectPacket.ConnectPacketBuilder();
        cpb.withClientId("iot-" + UUID.randomUUID());

        // 3) 绑定事件
        builder.withConnectProperties(cpb);
        builder.withLifeCycleEvents(lifecycleEvents);
        builder.withPublishEvents(publishEvents);

        // 4) 构建 Mqtt5Client
        this.client = builder.build();
        // builder 用完记得 close
        builder.close();

        // 5) 启动连接
        this.client.start();

        // 6) 等待连接完成
        try {
            lifecycleEvents.getConnectedFuture().get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException("MQTT 连接异常: " + e.getMessage(), e);
        }
    }

    /**
     * 请求-响应 发布
     */
    public Map<String, Object> sendMessageWithResponseTopic(String transNo, String sendTopic, String respTopic, String msg,
                                                            Long timeout, boolean isNeedSubscribe) {
        // 1. 重置 responseFuture
        CompletableFuture<Map<String, Object>> future = mqttResponseService.registerRequest(transNo);
        long defaultTimeout = 60000L;
        // 3. 订阅公共响应主题
        try {
            if (isNeedSubscribe) {
                SubscribePacket.SubscribePacketBuilder subBuilder = new SubscribePacket.SubscribePacketBuilder()
                        .withSubscription(respTopic, QOS.AT_LEAST_ONCE, false, false,
                                SubscribePacket.RetainHandlingType.DONT_SEND);
                client.subscribe(subBuilder.build()).get(3000, TimeUnit.MILLISECONDS);
            }
            // 4. 构建发布包，并将 correlationId 作为 correlation data 附加
            PublishPacket.PublishPacketBuilder pubBuilder = new PublishPacket.PublishPacketBuilder()
                    .withTopic(sendTopic)
                    .withQOS(QOS.AT_LEAST_ONCE)
                    .withPayload(msg.getBytes())
                    .withResponseTopic(respTopic)
                    .withCorrelationData(transNo.getBytes());

            log.info("[MQTT] 消息发送来自: transNo:{}，sendTopic:{}, respTopic:{},payload={}", transNo, sendTopic,
                    respTopic,msg);
            client.publish(pubBuilder.build());

            // 5. 等待响应（响应消息中应包含相同的 correlation data）
            if (timeout != null) {
                defaultTimeout = timeout;
            }
            return future.get(defaultTimeout, TimeUnit.MILLISECONDS);
//            return future.get();
        } catch (TimeoutException e) {
            log.warn("设备端响应超时, transNo: {}, sendTopic: {}, respTopic: {}", transNo,sendTopic,respTopic);
            mqttResponseService.removeRequest(transNo);
            return null;
        } catch (Exception e) {
            log.warn("MQTT 发布异常, transNo: {}, sendTopic: {}, respTopic: {}", transNo,sendTopic,respTopic);
            mqttResponseService.removeRequest(transNo);
            return null;
        }
    }


    /**
     * 纯订阅
     */
    public void subscribeTopic(String topicFilter) throws Exception {
        try {
            if (!client.getIsConnected()) {
                throw new IllegalStateException("MQTT客户端未连接");
            }

            SubscribePacket packet = new SubscribePacket.SubscribePacketBuilder()
                    .withSubscription(topicFilter, QOS.AT_LEAST_ONCE)
                    .build();

            client.subscribe(packet).whenComplete((response, ex) -> {
                if (ex != null) {
                    log.error("[MQTT] 订阅失败 {}: {}", topicFilter, ex.getMessage());
                } else {
                    // 使用 SubAckReasonCode 枚举类型
                    for (SubAckPacket.SubAckReasonCode code : response.getReasonCodes()) {
                        if (code != SubAckPacket.SubAckReasonCode.GRANTED_QOS_0 && code != SubAckPacket.SubAckReasonCode.GRANTED_QOS_1) {
                            log.warn("订阅返回错误状态码: {} -> {} ({})",
                                    topicFilter, code.getValue(), code.name());
                        }
                    }
                    log.info("[MQTT] 订阅成功: {}", topicFilter);
                }
            });
        } catch (Exception e) {
            log.error("订阅发生异常: {}", topicFilter, e);
        }
    }

    /**
     * 纯发布
     */
    public void publishNoResponse(String topic, String payload) throws Exception {
        PublishPacket.PublishPacketBuilder builder = new PublishPacket.PublishPacketBuilder()
                .withTopic(topic)
                .withQOS(QOS.AT_LEAST_ONCE)
                .withPayload(payload.getBytes());
        log.info("[MQTT] 异步消息发送: sendTopic:{},payload={}", topic, payload);
        client.publish(builder.build())
                .whenComplete((publishResult, throwable) -> {
                    if (throwable != null) {
                        log.warn("异步消息发布失败, 异常信息: {}", throwable.getMessage(), throwable);
                    } else {
                        log.info("异步消息发布成功，结果：{}", publishResult);
                    }
                });
    }

    /**
     * 停止客户端
     */
    public void stop() {
        if (client != null) {
            log.info("[MQTT]断开成功");
            client.stop(null);
        }
    }
}