package com.creality.lcd.falcon.pojo.entity.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 16:06
 * @Description: wiki教程实体
 */
@Data
@TableName("falcon_wiki_tutorials")
public class FalconWikiTutorials {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "channel")
    private String channel;

    @TableField(value = "tutorial_name")
    private String tutorialName;

    @TableField(value = "tutorial_name_en")
    private String tutorialNameEn;

    @TableField(value = "tutorial_desc")
    private String tutorialDesc;

    @TableField(value = "tutorial_desc_en")
    private String tutorialDescEn;

    @TableField(value = "tutorial_link")
    private String tutorialLink;

    @TableField(value = "tutorial_link_cn")
    private String tutorialLinkCn;

    @TableField(value = "tutorial_cover")
    private String tutorialCover;
    
    @TableField(value = "click_num")
    private Integer clickNum;

    @TableField(value = "creator")
    private String creator;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "updater")
    private String updater;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
