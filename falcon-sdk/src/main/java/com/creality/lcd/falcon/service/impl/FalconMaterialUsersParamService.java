package com.creality.lcd.falcon.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.lcd.falcon.mappers.pc.FalconMaterialUsersParamMapper;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;
import com.creality.lcd.falcon.service.IFalconMaterialUsersParamService;

@Service
public class FalconMaterialUsersParamService extends ServiceImpl<FalconMaterialUsersParamMapper, FalconMaterialUsersParam> implements IFalconMaterialUsersParamService{

	
	@Autowired
	private FalconMaterialUsersParamMapper falconMaterialUsersParamMapper;

	@Override
	public int addFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam) {
		return falconMaterialUsersParamMapper.addFalconMaterialUsersParam(falconMaterialUsersParam);
	}

	@Override
	public int delFalconMaterialUsersParamById(String id) {
		return falconMaterialUsersParamMapper.delFalconMaterialUsersParamById(id);
	}

	@Override
	public FalconMaterialUsersParam selFalconMaterialUsersParamById(String id) {
		return falconMaterialUsersParamMapper.selectById(id);
	}

	@Override
	public int upFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam) {
		return falconMaterialUsersParamMapper.upFalconMaterialUsersParam(falconMaterialUsersParam);
	}

	@Override
	public List<FalconMaterialUsersParam> selFalconMaterialUsersParamList(FalconMaterialUsersParam falconMaterialUsersParam) {
		return falconMaterialUsersParamMapper.selFalconMaterialUsersParamList(falconMaterialUsersParam);
	}

	@Override
	public IPage<FalconMaterialUsersParam> selFalconMaterialUsersParamPage(FalconMaterialUsersParam falconMaterialUsersParam, Page<FalconMaterialUsersParam> page) {
		return falconMaterialUsersParamMapper.selFalconMaterialUsersParamPage(page, falconMaterialUsersParam);
	}

	@Override
	public void saveBatchParam(List<FalconMaterialUsersParam> paramList) {
		falconMaterialUsersParamMapper.saveBatchParam(paramList);
	}

}
