package com.creality.lcd.falcon.mappers.pc;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;

@Mapper
public interface FalconMaterialUsersParamMapper extends BaseMapper<FalconMaterialUsersParam> {

	int addFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam);

	int delFalconMaterialUsersParamById(String id);

	FalconMaterialUsersParam findFalconMaterialUsersParamById(String id);

	int upFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam);

	List<FalconMaterialUsersParam> selFalconMaterialUsersParamList(FalconMaterialUsersParam falconMaterialUsersParam);

	Page<FalconMaterialUsersParam> selFalconMaterialUsersParamPage(Page<FalconMaterialUsersParam> pg,@Param("falconMaterialUsersParam")FalconMaterialUsersParam falconMaterialUsersParam);

	void saveBatchParam(@Param("paramList")List<FalconMaterialUsersParam> paramList);

}
