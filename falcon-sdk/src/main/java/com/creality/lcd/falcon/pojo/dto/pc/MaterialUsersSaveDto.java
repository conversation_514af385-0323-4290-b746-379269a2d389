package com.creality.lcd.falcon.pojo.dto.pc;

import java.util.List;

import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

public class MaterialUsersSaveDto extends FalconMaterialUsers{

	private static final long serialVersionUID = 1L;
	
	/**耗材参数列表*/
	@NotNull(message = "paramList can not be null")
	@Valid
	List<FalconMaterialUsersParam> paramList;

	public List<FalconMaterialUsersParam> getParamList() {
		return paramList;
	}

	public void setParamList(List<FalconMaterialUsersParam> paramList) {
		this.paramList = paramList;
	}

}
