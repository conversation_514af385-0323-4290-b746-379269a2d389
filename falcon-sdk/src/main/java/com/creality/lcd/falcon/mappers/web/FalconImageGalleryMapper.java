package com.creality.lcd.falcon.mappers.web;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.pojo.dto.pc.ImageSearchDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.pojo.entity.web.FalconImageGallery;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo;

/**
 * @Author: zhangshaoming
 * @Date: 2024/12/9 15:50
 * @Description: 图像管理DAO
 */
@Mapper
public interface FalconImageGalleryMapper extends BaseMapper<FalconImageGallery> {

    int batchInsertImages(@Param("imageGalleryList") List<FalconImageGallery> imageGalleryList);

    boolean isCategoryExistInImage(String category);

	IPage<ImageCollectVo> selFalconImagePage(@Param("searchDto")ImageSearchDto searchDto, Page<FalconImageUsers> searchPage);

	List<ImageCollectVo> selFalconImageList(@Param("searchDto")ImageSearchDto searchDto);

	void upCollectNum(@Param("imageId")Long imageId, @Param("collectNum")Integer collectNum);

}
