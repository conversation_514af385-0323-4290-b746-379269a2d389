package com.creality.lcd.falcon.session;

import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;

import lombok.Data;

@Data
public class SessionModel {
	
	/**账号信息*/
	private FalconAccount account;
	
	/**拉取的时间*/
	private Long saveTime;
	/**session剩余时间*/
	private Long sessionTime;
	
	/**
	 * 临时暂存的时间在有效期内-60秒
	 * @return
	 */
	public boolean saveTimeIsOk() {
		return (System.currentTimeMillis()-saveTime)<60*1000;
	}
	
}
