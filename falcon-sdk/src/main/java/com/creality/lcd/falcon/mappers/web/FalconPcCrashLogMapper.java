package com.creality.lcd.falcon.mappers.web;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.pojo.dto.web.CrashLogSearchDto;
import com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog;

import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

/**
 * @Author: zhangshaoming
 * @Date: 2025/04/22 11:59
 * @Description:
 */
@Mapper
public interface FalconPcCrashLogMapper extends BaseMapper<FalconPcCrashLog> {

	List<String> searchOsVersionList();

	List<String> searchIpAreaList();

	Page<FalconPcCrashLog> selFalconPcCrashLogList(Page<FalconPcCrashLog> page,@Param("searchDto") CrashLogSearchDto searchDto);

	List<String> searchDeviceNameList();
}
