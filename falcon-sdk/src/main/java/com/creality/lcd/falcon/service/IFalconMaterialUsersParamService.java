package com.creality.lcd.falcon.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;

public interface IFalconMaterialUsersParamService  extends IService<FalconMaterialUsersParam> {

	public int addFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam);
	
	public int delFalconMaterialUsersParamById(String id);
	
	public FalconMaterialUsersParam selFalconMaterialUsersParamById(String id);
	
	public int upFalconMaterialUsersParam(FalconMaterialUsersParam falconMaterialUsersParam);
	
	public List<FalconMaterialUsersParam> selFalconMaterialUsersParamList(FalconMaterialUsersParam falconMaterialUsersParam);
	
	public IPage<FalconMaterialUsersParam> selFalconMaterialUsersParamPage(FalconMaterialUsersParam falconMaterialUsersParam, Page<FalconMaterialUsersParam> page);

	public void saveBatchParam(List<FalconMaterialUsersParam> paramList);

}

