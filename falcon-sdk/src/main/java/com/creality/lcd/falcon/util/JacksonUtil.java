package com.creality.lcd.falcon.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.text.SimpleDateFormat;

/**
 * JacksonUtil
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public class JacksonUtil {

    public static ObjectMapper getObjectMapper() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        TimeZone timeZone = TimeZone.getTimeZone(SYSTEM_DEFAULT_CONFIG_TIMEZONE);
//        format.setTimeZone(timeZone);
        // modulesToInstall(JavaTimeModule.class) 支持 Java 8 date/time classes(LocalDateTime, LocalDate, etc.)
        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder()
                .modulesToInstall(JavaTimeModule.class)
                .dateFormat(format);
        return builder.build();
    }

}
