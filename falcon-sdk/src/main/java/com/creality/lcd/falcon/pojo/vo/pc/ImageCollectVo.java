package com.creality.lcd.falcon.pojo.vo.pc;

import java.sql.Date;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class ImageCollectVo {
	private Long id;
	/**分类*/
	private String category;
	/**标签*/
    private String lable;
    private String imageName;
    private String imageUrl;
    /**是否已经收藏*/
    private boolean collected=false;
    /**被收藏次数*/
    private Integer collectCount=0;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
