package com.creality.lcd.falcon.pojo.entity.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zhangshaoming
 * @Date: 2025/04/22 11:53
 * @Description:
 */
@Data
@TableName("falcon_pc_crash_log")
public class FalconPcCrashLog {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "file_name")
    private String fileName;

    @TableField(value = "software_name")
    private String softwareName;

    @TableField(value = "software_version")
    private String softwareVersion;

    @TableField(value = "os_version")
    private String osVersion;

    @TableField(value = "cpu")
    private String cpu;

    @TableField(value = "mem")
    private String mem;

    @TableField(value = "lang")
    private String lang;

    @TableField(value = "screen")
    private String screen;

    @TableField(value = "host_name")
    private String hostName;

    @TableField(value = "mac_address")
    private String macAddress;
    
    @TableField(value = "ip_address")
    private String ipAddress;
    
    @TableField(value = "ip_area")
    private String ipArea;
    
    @TableField(value = "device_name")
    private String deviceName;

    @TableField(value = "dump_path")
    private String dumpPath;

    @TableField(value = "log_path")
    private String logPath;

    @TableField(value = "log_zip")
    private String logZip;

    @TableField(value = "log_zip_size")
    private String logZipSize;

    @TableField(value = "upload_time")
    private LocalDateTime uploadTime;
}
