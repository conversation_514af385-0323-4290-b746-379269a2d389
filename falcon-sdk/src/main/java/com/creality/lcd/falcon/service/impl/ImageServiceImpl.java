package com.creality.lcd.falcon.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.mappers.web.FalconImageCategoryMapper;
import com.creality.lcd.falcon.mappers.web.FalconImageGalleryMapper;
import com.creality.lcd.falcon.pojo.dto.pc.ImageSearchDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.pojo.entity.web.FalconImageCategory;
import com.creality.lcd.falcon.pojo.entity.web.FalconImageGallery;
import com.creality.lcd.falcon.pojo.vo.app.AppImageGalleryVo;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo;
import com.creality.lcd.falcon.service.ImageService;

/**
 * @Author: zhangshaoming
 * @Date: 2025/03/07 18:11
 * @Description:
 */
@Service
public class ImageServiceImpl implements ImageService {

    @Autowired
    private FalconImageGalleryMapper imageGalleryMapper;
    @Autowired
    private FalconImageCategoryMapper falconImageCategoryMapper;

    @Override
    public List<AppImageGalleryVo> getAllImages() {
        //查出所有图像信息
        List<FalconImageGallery> imageGalleryList = imageGalleryMapper.selectList(null);
        return Optional.ofNullable(imageGalleryList)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(imageGallery -> {
                    AppImageGalleryVo vo = new AppImageGalleryVo();
                    vo.setId(imageGallery.getId());
                    vo.setLable(imageGallery.getLabel());
                    vo.setImageName(imageGallery.getImageName());
                    vo.setImageUrl(imageGallery.getImageUrl());
                    return vo;
                })
                .collect(Collectors.toList());
    }

	@Override
	public IPage<ImageCollectVo> selFalconImagePage(ImageSearchDto searchDto, Page<FalconImageUsers> searchPage) {
		return imageGalleryMapper.selFalconImagePage(searchDto,searchPage);
	}

	@Override
	public List<FalconImageCategory> selImageCategoryList() {
		QueryWrapper<FalconImageCategory> query=new QueryWrapper<FalconImageCategory>();
		return falconImageCategoryMapper.selectList(query);
	}

	@Override
	public List<ImageCollectVo> selFalconImageList(ImageSearchDto searchDto) {
		return imageGalleryMapper.selFalconImageList(searchDto);
	}

	@Override
	public void upCollectNum(Long imageId, Integer collectNum) {
		if(imageId!=null&&collectNum!=null) {
			imageGalleryMapper.upCollectNum(imageId,collectNum);
		}
	}
}
