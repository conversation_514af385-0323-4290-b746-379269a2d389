package com.creality.lcd.falcon.session;

import java.util.Map.Entry;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.creality.lcd.falcon.constant.redis.RedisKey;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.util.JsonUtils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * session管理
 */
@Slf4j
@Component
public class SessionManagerAll {
	
	private final Long lostTimeSecond=3*24*60*60L;
	
	@Autowired
    private RedisTemplate<String,Object> redisTemplate;
	
	private ConcurrentHashMap<String, SessionModel> sessionMap=new ConcurrentHashMap<String, SessionModel>();

	
	private String createSessionKey(String sessionId) {
		return RedisKey.getUserSession(sessionId);
	}
	
	/**
	 * 检查sessiId是否有效
	 * @param sessionId
	 * @return
	 */
	public boolean checkSessionExpired(String sessionId) {
		if(StrUtil.isEmpty(sessionId)) {
			return false;
		}
		if(StrUtil.isNotEmpty(sessionId)) {
			SessionModel sessionModel=sessionMap.get(sessionId);
			if(sessionModel!=null&&sessionModel.saveTimeIsOk()) {
				return true;
			}
			return loadDataFromCache(sessionId);
		}
		return false;
	}
	/**
	 * 从redis读取缓存判断session是否过期
	 * @param sessionId
	 * @return
	 */
	private synchronized boolean loadDataFromCache(String sessionId) {
		SessionModel sessionModel=sessionMap.get(sessionId);
		if(sessionModel!=null&&sessionModel.saveTimeIsOk()) {
			return true;
		}
		cleanCache();
		
		String sessionKey=createSessionKey(sessionId);
		log.info("从redis读取session信息:{}",sessionId);
		Object account=redisTemplate.opsForValue().get(sessionKey);
		if(account!=null) {
			String userDetailRedis = String.valueOf(account);
			FalconAccount falconAccount = JsonUtils.jsonToObject(userDetailRedis,FalconAccount.class);
			SessionModel model=new SessionModel();
			
			model.setAccount(falconAccount);
			model.setSaveTime(System.currentTimeMillis());
			//失效时间
			Long lostTime=redisTemplate.getExpire(sessionKey);
			log.info("{}失效时间:{}",sessionId,lostTime);
			//小于三天的时候进行续期
			if(lostTime<lostTimeSecond) {
				log.info("自动续期:{}",sessionId);
				redisTemplate.expire(sessionKey, 7,TimeUnit.DAYS);
			}
			sessionMap.put(sessionId, model);
			return true;
		}
		
		return false;
	}
	
	private void cleanCache() {
		Random r=new Random();
		Integer index=r.nextInt(100);
		if(index.equals(1)) {
			log.info("清理当前用户信息缓存");
			new Thread(new Runnable() {
				@Override
				public void run() {
					for(Entry<String, SessionModel> kv:sessionMap.entrySet()) {
						String key=kv.getKey();
						SessionModel value=kv.getValue();
						if(!value.saveTimeIsOk()) {
							sessionMap.remove(key);
						}
					}
				}
			}).start();
		}
	}
	/**
	 * 根据sessionId获取当前用户信息
	 * @param sessionId
	 * @return
	 */
	public FalconAccount getUserInfo(String sessionId) {
		if(StrUtil.isEmpty(sessionId)) {
			return null;
		}
		SessionModel model=sessionMap.get(sessionId);
		if(model!=null&&model.saveTimeIsOk()) {
			return model.getAccount();
		}
		String sessionKey=createSessionKey(sessionId);
		String userDetailRedis = String.valueOf(redisTemplate.opsForValue().get(sessionKey));
		FalconAccount falconAccount = JsonUtils.jsonToObject(userDetailRedis,FalconAccount.class);
        return falconAccount;
	}
}

