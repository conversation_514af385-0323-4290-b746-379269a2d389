package com.creality.lcd.falcon.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.pojo.dto.pc.ImageSearchDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers;
import com.creality.lcd.falcon.pojo.entity.web.FalconImageCategory;
import com.creality.lcd.falcon.pojo.vo.app.AppImageGalleryVo;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo;

/**
 * @Author: zhangshaoming
 * @Date: 2025/03/07 18:11
 * @Description:
 */
public interface ImageService {

    List<AppImageGalleryVo> getAllImages();

    /**
     * 查询qt端用户图像库
     * @param searchDto
     * @param searchPage
     * @return
     */
	IPage<ImageCollectVo> selFalconImagePage(ImageSearchDto searchDto, Page<FalconImageUsers> searchPage);

	List<FalconImageCategory> selImageCategoryList();

	List<ImageCollectVo> selFalconImageList(ImageSearchDto searchDto);

	void upCollectNum(Long imageId, Integer collectNum);
}
