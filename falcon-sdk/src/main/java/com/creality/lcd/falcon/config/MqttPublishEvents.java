package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.service.MqttResponseService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awssdk.crt.mqtt5.Mqtt5Client;
import software.amazon.awssdk.crt.mqtt5.Mqtt5ClientOptions;
import software.amazon.awssdk.crt.mqtt5.PublishReturn;
import software.amazon.awssdk.crt.mqtt5.packets.PublishPacket;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: tiantao
 * @Date: 2025/2/12 17:19
 * @Description: 消息到达回调
 */

public class MqttPublishEvents implements Mqtt5ClientOptions.PublishEvents {
    private static final Logger log = LogManager.getLogger(MqttPublishEvents.class);

    // 用于请求-响应模式
    private final MqttResponseService mqttResponseService;

    // 如果需要允许添加多个监听器，可以这样管理
    private final List<MqttMessageListener> listeners = new CopyOnWriteArrayList<>();

    // IoT消息处理器管理器（可选注入，用于新的处理方式）
    private Object iotMessageHandlerManager;

    /**
     * 线程池：使用可控的 ThreadPoolExecutor，而不是 newCachedThreadPool()
     *
     * 这里给出示例：corePoolSize=10, maxPoolSize=50, 队列大小=500
     * 你可根据实际 CPU/内存/并发需求进行调整
     */
    private final ExecutorService eventExecutor;

    public MqttPublishEvents(MqttResponseService mqttResponseService) {
        this.mqttResponseService = mqttResponseService;

        // 具体参数可根据你们的服务器性能、并发压力做调优
        this.eventExecutor = new ThreadPoolExecutor(
                // corePoolSize
                10,
                // maximumPoolSize
                50,
                // keepAliveTime
                60L, TimeUnit.SECONDS,
                // 有界队列 500
                new LinkedBlockingQueue<>(500),
                new DefaultThreadFactory("mqtt-event-worker"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    public void addListener(MqttMessageListener listener) {
        listeners.add(listener);
    }

    @Override
    public void onMessageReceived(Mqtt5Client client, PublishReturn publishReturn) {
        if (publishReturn == null || publishReturn.getPublishPacket() == null) {
            log.warn("[MQTT] 收到空的 PublishReturn 或 PublishPacket，消息来源无法溯源");
            return;
        }

        PublishPacket packet = publishReturn.getPublishPacket();
        String topic = packet.getTopic();
        byte[] payload = packet.getPayload();

        if (payload == null || payload.length == 0) {
            log.warn("[MQTT] 收到空的 Payload，Topic={}，消息处理被跳过", topic);
            return;
        }

        String message = new String(payload);

        log.info("[MQTT] 消息来自: Topic={}, Payload={}", topic, message);

        // 如果设备将 correlationId 放在 payload 中，我们需要解析 JSON 并提取该字段
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将 payload 解析为 Map<String, Object>
            Map<String, Object> map  = objectMapper.readValue(message, Map.class);
            if (map.containsKey("transNo")) {
                String correlationId = (String) map.get("transNo");
                // 根据解析到的 correlationId 完成对应的 Future
                mqttResponseService.completeResponse(correlationId, map);
            }
            // 将通知监听器的操作放到线程池中，避免阻塞MQTT回调线程
            eventExecutor.submit(() -> {
                for (MqttMessageListener listener : listeners) {
                    try {
                        listener.onMessage(topic, map);
                    } catch (Exception e) {
                        log.error("[MQTT] Listener处理消息异常, topic={}, 异常信息={}", topic, e.getMessage(), e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("mqtt消息接收或发送异常，异常信息: {}，{}", e.getMessage(), e);
        }


    }
    /**
     * 自定义线程工厂，以便命名线程或设置其它属性
     */
    private static class DefaultThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicInteger threadIndex = new AtomicInteger(1);

        public DefaultThreadFactory(String prefix) {
            this.namePrefix = prefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            String threadName = namePrefix + "-" + threadIndex.getAndIncrement();
            Thread t = new Thread(r, threadName);
            // 根据需求可设 t.setDaemon(false) / t.setPriority(...)
            return t;
        }
    }
}