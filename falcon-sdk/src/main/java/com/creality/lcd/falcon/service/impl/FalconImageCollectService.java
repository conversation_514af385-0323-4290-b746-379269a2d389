package com.creality.lcd.falcon.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.creality.lcd.falcon.mappers.pc.FalconImageCollectMapper;
import com.creality.lcd.falcon.pojo.entity.pc.FalconImageCollect;
import com.creality.lcd.falcon.pojo.vo.pc.ImageCollectCountVo;
import com.creality.lcd.falcon.service.IFalconImageCollectService;

@Service
public class FalconImageCollectService extends ServiceImpl<FalconImageCollectMapper, FalconImageCollect> implements IFalconImageCollectService{
	
	@Autowired
	private FalconImageCollectMapper falconImageCollectMapper;

	@Override
	public int addFalconImageCollect(FalconImageCollect falconImageCollect) {
		return falconImageCollectMapper.addFalconImageCollect(falconImageCollect);
	}

	@Override
	public int delFalconImageCollectById(String id) {
		return falconImageCollectMapper.delFalconImageCollectById(id);
	}


	@Override
	public List<FalconImageCollect> selFalconImageCollectList(FalconImageCollect falconImageCollect) {
		return falconImageCollectMapper.selFalconImageCollectList(falconImageCollect);
	}

	@Override
	public List<Long> selCollectedIds(String userId, List<Long> imgIds) {
		return falconImageCollectMapper.selCollectedIds(userId,imgIds);
	}

	@Override
	public Integer delFalconImageCollect(String userId, Long imageId) {
		return falconImageCollectMapper.delFalconImageCollect(userId,imageId);
	}

	@Override
	public List<ImageCollectCountVo> selCountByImgIds(List<Long> imgIds) {
		return falconImageCollectMapper.selCountByImgIds(imgIds);
	}
}
