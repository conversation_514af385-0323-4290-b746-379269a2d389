package com.creality.lcd.falcon.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.creality.lcd.falcon.pojo.dto.pc.MaterialUsersSaveDto;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam;

public interface IFalconMaterialUsersService  extends IService<FalconMaterialUsers> {

	public int addFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers);
	
	public int delFalconMaterialUsersById(Long id);
	
	public FalconMaterialUsers selFalconMaterialUsersById(String id);
	
	public int upFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers);
	
	public List<FalconMaterialUsers> selFalconMaterialUsersList(FalconMaterialUsers falconMaterialUsers);
	
	public IPage<FalconMaterialUsers> selFalconMaterialUsersPage(FalconMaterialUsers falconMaterialUsers, Page<FalconMaterialUsers> page);

	public boolean updateMateriaById(MaterialUsersSaveDto dto);

	public void saveMaterial(FalconMaterialUsers meterial, List<FalconMaterialUsersParam> paramList);

}

