package com.creality.lcd.falcon.mappers.pc;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers;


@Mapper
public interface FalconMaterialUsersMapper extends BaseMapper<FalconMaterialUsers> {

	int addFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers);

	int delFalconMaterialUsersById(Long id);

	FalconMaterialUsers findFalconMaterialUsersById(String id);

	int upFalconMaterialUsers(FalconMaterialUsers falconMaterialUsers);

	List<FalconMaterialUsers> selFalconMaterialUsersList(FalconMaterialUsers falconMaterialUsers);

	Page<FalconMaterialUsers> selFalconMaterialUsersPage(Page<FalconMaterialUsers> pg,@Param("falconMaterialUsers")FalconMaterialUsers falconMaterialUsers);

}
