package com.creality.lcd.falcon.pojo.entity.pc;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户自定义耗材参数表
 * <AUTHOR>
 */
@Data
public class FalconMaterialUsersParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键id*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**耗材id*/
    private Long materialId;
    /**功率*/
    @NotNull(message = "power can not be null")
    private String power;
    /**切割参数*/
    @NotNull(message = "cut can not be null")
    private String cut;
    /**雕刻参数*/
    @NotNull(message = "engrave can not be null")
    private String engrave;
    /**创建时间*/
    private Date createTime;
}
