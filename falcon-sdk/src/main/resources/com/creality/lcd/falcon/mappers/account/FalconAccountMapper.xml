<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.account.FalconAccountMapper">
    <resultMap id="FalconAccountMap" type="com.creality.lcd.falcon.pojo.entity.account.FalconAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="origin" jdbcType="VARCHAR" property="origin"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="device_avatar" jdbcType="VARCHAR" property="deviceAvatar"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="resume" jdbcType="VARCHAR" property="resume"/>
        <result column="sex" jdbcType="TINYINT" property="sex"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="is_recommend" jdbcType="INTEGER" property="isRecommend"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="delete_status"     jdbcType="TINYINT"   property="deleteStatus"/>
        <result column="delete_apply_time" jdbcType="TIMESTAMP" property="deleteApplyTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    
    <select id="countByOrigin" resultType="com.creality.lcd.falcon.pojo.vo.web.RegisterUserNumGroup">
    	select upper(origin) origin,count(*) num
    	from falcon_account
    	where delete_status=0
    	<if test="startDate!=null">
    		and create_time&gt;=#{startDate}
    	</if>
    	<if test="endDate!=null">
    		and create_time&lt;#{endDate}
    	</if>
    	group by origin
    </select>
</mapper>