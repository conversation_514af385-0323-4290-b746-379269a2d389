<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.pc.FalconMaterialUsersMapper">

	<resultMap type="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers" id="FalconMaterialUsersResult">
		<result property="id"    column="id"/>
		<result property="userId"    column="user_id"/>
		<result property="materialName"    column="material_name"/>
		<result property="thickness"    column="thickness"/>
		<result property="thicknessState"    column="thickness_state"/>
		<result property="materialLength"    column="material_length"/>
		<result property="materialWidth"    column="material_width"/>
		<result property="sizeState"    column="size_state"/>
		<result property="materialImg"    column="material_img"/>
		<result property="delState"    column="del_state"/>
		<result property="createTime"    column="create_time"/>
	</resultMap>
	<sql id="selFalconMaterialUsers">
		select id,user_id,material_name,thickness,thickness_state,material_length,material_width,size_state,material_img,del_state,create_time from falcon_material_users
	</sql>
	<insert id="addFalconMaterialUsers" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers" useGeneratedKeys="true" keyProperty="id">
		insert into falcon_material_users 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="userId != null">user_id,</if>
			<if test="materialName != null">material_name,</if>
			<if test="thickness != null">thickness,</if>
			<if test="thicknessState != null">thickness_state,</if>
			<if test="materialLength != null">material_length,</if>
			<if test="materialWidth != null">material_width,</if>
			<if test="sizeState != null">size_state,</if>
			<if test="materialImg != null">material_img,</if>
			<if test="delState != null">del_state,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="userId != null">#{userId},</if>
			<if test="materialName != null">#{materialName},</if>
			<if test="thickness != null">#{thickness},</if>
			<if test="thicknessState != null">#{thicknessState},</if>
			<if test="materialLength != null">#{materialLength},</if>
			<if test="materialWidth != null">#{materialWidth},</if>
			<if test="sizeState != null">#{sizeState},</if>
			<if test="materialImg != null">#{materialImg},</if>
			<if test="delState != null">#{delState},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	<update id="upFalconMaterialUsers" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers">
		update falcon_material_users 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="userId != null">user_id=#{userId},</if>
			<if test="materialName != null">material_name=#{materialName},</if>
			<if test="thickness != null">thickness=#{thickness},</if>
			<if test="thicknessState != null">thickness_state=#{thicknessState},</if>
			<if test="materialLength != null">material_length=#{materialLength},</if>
			<if test="materialWidth != null">material_width=#{materialWidth},</if>
			<if test="sizeState != null">size_state=#{sizeState},</if>
			<if test="materialImg != null">material_img=#{materialImg},</if>
			<if test="delState != null">del_state=#{delState},</if>
			<if test="createTime != null">create_time=#{createTime},</if>
		</trim>
		where id = #{id}
	</update>
	<select id="selFalconMaterialUsersPage" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers" resultMap="FalconMaterialUsersResult">
		<include refid="selFalconMaterialUsers"/>
		<where>
			<if test="falconMaterialUsers.id != null  and falconMaterialUsers.id != ''"> and id=#{falconMaterialUsers.id}</if>
			<if test="falconMaterialUsers.userId != null  and falconMaterialUsers.userId != ''"> and user_id=#{falconMaterialUsers.userId}</if>
			<if test="falconMaterialUsers.materialName != null  and falconMaterialUsers.materialName != ''"> and material_name=#{falconMaterialUsers.materialName}</if>
			<if test="falconMaterialUsers.thickness != null  and falconMaterialUsers.thickness != ''"> and thickness=#{falconMaterialUsers.thickness}</if>
			<if test="falconMaterialUsers.thicknessState != null  and falconMaterialUsers.thicknessState != ''"> and thickness_state=#{falconMaterialUsers.thicknessState}</if>
			<if test="falconMaterialUsers.materialLength != null  and falconMaterialUsers.materialLength != ''"> and material_length=#{falconMaterialUsers.materialLength}</if>
			<if test="falconMaterialUsers.materialWidth != null  and falconMaterialUsers.materialWidth != ''"> and material_width=#{falconMaterialUsers.materialWidth}</if>
			<if test="falconMaterialUsers.sizeState != null  and falconMaterialUsers.sizeState != ''"> and size_state=#{falconMaterialUsers.sizeState}</if>
			<if test="falconMaterialUsers.materialImg != null  and falconMaterialUsers.materialImg != ''"> and material_img=#{falconMaterialUsers.materialImg}</if>
			<if test="falconMaterialUsers.delState != null  and falconMaterialUsers.delState != ''"> and del_state=#{falconMaterialUsers.delState}</if>
			<if test="falconMaterialUsers.createTime != null  and falconMaterialUsers.createTime != ''"> and create_time=#{falconMaterialUsers.createTime}</if>
		</where>
	</select>
	<select id="selFalconMaterialUsersList" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsers" resultMap="FalconMaterialUsersResult">
		<include refid="selFalconMaterialUsers"/>
		<where>
			<if test="id != null"> and id=#{id}</if>
			<if test="userId != null  and userId != ''"> and user_id=#{userId}</if>
			<if test="materialName != null  and materialName != ''"> and instr(material_name,#{materialName})&gt;0</if>
			<if test="thickness != null"> and thickness=#{thickness}</if>
			<if test="thicknessState != null"> and thickness_state=#{thicknessState}</if>
			<if test="materialLength != null"> and material_length=#{materialLength}</if>
			<if test="materialWidth != null"> and material_width=#{materialWidth}</if>
			<if test="sizeState != null"> and size_state=#{sizeState}</if>
			<if test="materialImg != null  and materialImg != ''"> and material_img=#{materialImg}</if>
			<if test="delState != null"> and del_state=#{delState}</if>
			<if test="createTime != null"> and create_time=#{createTime}</if>
		</where>
		order by id desc
	</select>
	
	<delete id="delFalconMaterialUsersById" parameterType="Long">
		update falcon_material_users set del_state=(del_state+1)%2 where id = #{id}
	</delete>
</mapper>
