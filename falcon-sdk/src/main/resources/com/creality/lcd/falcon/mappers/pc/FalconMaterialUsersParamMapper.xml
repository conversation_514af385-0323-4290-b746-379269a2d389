<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.pc.FalconMaterialUsersParamMapper">

	<resultMap type="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam" id="FalconMaterialUsersParamResult">
		<result property="id"    column="id"/>
		<result property="materialId"    column="material_id"/>
		<result property="power"    column="power"/>
		<result property="cut"    column="cut"/>
		<result property="engrave"    column="engrave"/>
		<result property="createTime"    column="create_time"/>
	</resultMap>
	<sql id="selFalconMaterialUsersParam">
		select id,material_id,power,cut,engrave,create_time from falcon_material_users_param
	</sql>
	<insert id="addFalconMaterialUsersParam" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam">
		insert into falcon_material_users_param 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="materialId != null">material_id,</if>
			<if test="power != null">power,</if>
			<if test="cut != null">cut,</if>
			<if test="engrave != null">engrave,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="materialId != null">#{materialId},</if>
			<if test="power != null">#{power},</if>
			<if test="cut != null">#{cut},</if>
			<if test="engrave != null">#{engrave},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	
	<insert id="saveBatchParam">
		insert into falcon_material_users_param (material_id,power,cut,engrave,create_time)
		values
		<foreach collection="paramList" item="item" separator=",">
        	(#{item.materialId}, #{item.power}, #{item.cut},#{item.engrave},#{item.createTime})
    	</foreach>
	</insert>
	
	<update id="upFalconMaterialUsersParam" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam">
		update falcon_material_users_param 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="materialId != null">material_id=#{materialId},</if>
			<if test="power != null">power=#{power},</if>
			<if test="cut != null">cut=#{cut},</if>
			<if test="engrave != null">engrave=#{engrave},</if>
			<if test="createTime != null">create_time=#{createTime},</if>
		</trim>
		where id = #{id}
	</update>
	<select id="selFalconMaterialUsersParamPage" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam" resultMap="FalconMaterialUsersParamResult">
		<include refid="selFalconMaterialUsersParam"/>
		<where>
			<if test="falconMaterialUsersParam.id != null  and falconMaterialUsersParam.id != ''"> and id=#{falconMaterialUsersParam.id}</if>
			<if test="falconMaterialUsersParam.materialId != null  and falconMaterialUsersParam.materialId != ''"> and material_id=#{falconMaterialUsersParam.materialId}</if>
			<if test="falconMaterialUsersParam.power != null  and falconMaterialUsersParam.power != ''"> and power=#{falconMaterialUsersParam.power}</if>
			<if test="falconMaterialUsersParam.cut != null  and falconMaterialUsersParam.cut != ''"> and cut=#{falconMaterialUsersParam.cut}</if>
			<if test="falconMaterialUsersParam.engrave != null  and falconMaterialUsersParam.engrave != ''"> and engrave=#{falconMaterialUsersParam.engrave}</if>
			<if test="falconMaterialUsersParam.createTime != null  and falconMaterialUsersParam.createTime != ''"> and create_time=#{falconMaterialUsersParam.createTime}</if>
		</where>
	</select>
	<select id="selFalconMaterialUsersParamList" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconMaterialUsersParam" resultMap="FalconMaterialUsersParamResult">
		<include refid="selFalconMaterialUsersParam"/>
		<where>
			<if test="id != null  and id != ''"> and id=#{id}</if>
			<if test="materialId != null  and materialId != ''"> and material_id=#{materialId}</if>
			<if test="power != null  and power != ''"> and power=#{power}</if>
			<if test="cut != null  and cut != ''"> and cut=#{cut}</if>
			<if test="engrave != null  and engrave != ''"> and engrave=#{engrave}</if>
			<if test="createTime != null  and createTime != ''"> and create_time=#{createTime}</if>
		</where>
	</select>
	<delete id="delFalconMaterialUsersParamById" parameterType="String">
		delete from falcon_material_users_param where id = #{id}
	</delete>
</mapper>
