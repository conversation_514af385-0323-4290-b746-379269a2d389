<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.web.FalconPcCrashLogMapper">

	<resultMap type="com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog" id="FalconPcCrashLogResult">
		<result property="id"    column="id"/>
		<result property="fileName"    column="file_name"/>
		<result property="softwareName"    column="software_name"/>
		<result property="softwareVersion"    column="software_version"/>
		<result property="osVersion"    column="os_version"/>
		<result property="cpu"    column="cpu"/>
		<result property="mem"    column="mem"/>
		<result property="lang"    column="lang"/>
		<result property="screen"    column="screen"/>
		<result property="hostName"    column="host_name"/>
		<result property="macAddress"    column="mac_address"/>
		<result property="ipAddress"    column="ip_address"/>
		<result property="ipArea"    column="ip_area"/>
		<result property="deviceName"    column="device_name"/>
		<result property="dumpPath"    column="dump_path"/>
		<result property="logPath"    column="log_path"/>
		<result property="logZip"    column="log_zip"/>
		<result property="logZipSize"    column="log_zip_size"/>
		<result property="uploadTime"    column="upload_time"/>
	</resultMap>
	<sql id="selFalconPcCrashLog">
		select id,file_name,software_name,software_version,os_version,cpu,mem,lang,screen,host_name,mac_address,ip_address,ip_area,device_name,dump_path,log_path,log_zip,log_zip_size,upload_time from falcon_pc_crash_log
	</sql>
	<insert id="addFalconPcCrashLog" parameterType="com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog">
		insert into falcon_pc_crash_log 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="fileName != null">file_name,</if>
			<if test="softwareName != null">software_name,</if>
			<if test="softwareVersion != null">software_version,</if>
			<if test="osVersion != null">os_version,</if>
			<if test="cpu != null">cpu,</if>
			<if test="mem != null">mem,</if>
			<if test="lang != null">lang,</if>
			<if test="screen != null">screen,</if>
			<if test="hostName != null">host_name,</if>
			<if test="macAddress != null">mac_address,</if>
			<if test="ipAddress != null">ip_address,</if>
			<if test="ipArea != null">ip_area,</if>
			<if test="deviceName != null">device_name,</if>
			<if test="dumpPath != null">dump_path,</if>
			<if test="logPath != null">log_path,</if>
			<if test="logZip != null">log_zip,</if>
			<if test="logZipSize != null">log_zip_size,</if>
			<if test="uploadTime != null">upload_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="fileName != null">#{fileName},</if>
			<if test="softwareName != null">#{softwareName},</if>
			<if test="softwareVersion != null">#{softwareVersion},</if>
			<if test="osVersion != null">#{osVersion},</if>
			<if test="cpu != null">#{cpu},</if>
			<if test="mem != null">#{mem},</if>
			<if test="lang != null">#{lang},</if>
			<if test="screen != null">#{screen},</if>
			<if test="hostName != null">#{hostName},</if>
			<if test="macAddress != null">#{macAddress},</if>
			<if test="ipAddress != null">#{ipAddress},</if>
			<if test="ipArea != null">#{ipArea},</if>
			<if test="deviceName != null">#{deviceName},</if>
			<if test="dumpPath != null">#{dumpPath},</if>
			<if test="logPath != null">#{logPath},</if>
			<if test="logZip != null">#{logZip},</if>
			<if test="logZipSize != null">#{logZipSize},</if>
			<if test="uploadTime != null">#{uploadTime},</if>
		</trim>
	</insert>
	<update id="upFalconPcCrashLog" parameterType="com.creality.lcd.falcon.pojo.entity.web.FalconPcCrashLog">
		update falcon_pc_crash_log 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="fileName != null">file_name=#{fileName},</if>
			<if test="softwareName != null">software_name=#{softwareName},</if>
			<if test="softwareVersion != null">software_version=#{softwareVersion},</if>
			<if test="osVersion != null">os_version=#{osVersion},</if>
			<if test="cpu != null">cpu=#{cpu},</if>
			<if test="mem != null">mem=#{mem},</if>
			<if test="lang != null">lang=#{lang},</if>
			<if test="screen != null">screen=#{screen},</if>
			<if test="hostName != null">host_name=#{hostName},</if>
			<if test="macAddress != null">mac_address=#{macAddress},</if>
			<if test="ipAddress != null">ip_address=#{ipAddress},</if>
			<if test="ipArea != null">ip_area=#{ipArea},</if>
			<if test="deviceName != null">device_name=#{deviceName},</if>
			<if test="dumpPath != null">dump_path=#{dumpPath},</if>
			<if test="logPath != null">log_path=#{logPath},</if>
			<if test="logZip != null">log_zip=#{logZip},</if>
			<if test="logZipSize != null">log_zip_size=#{logZipSize},</if>
			<if test="uploadTime != null">upload_time=#{uploadTime},</if>
		</trim>
		where id = #{id}
	</update>
	
	<select id="selFalconPcCrashLogList" parameterType="com.creality.lcd.falcon.pojo.dto.web.CrashLogSearchDto" resultMap="FalconPcCrashLogResult">
		<include refid="selFalconPcCrashLog"/>
		<where>
			<if test="searchDto.fileName != null  and searchDto.fileName != ''"> and file_name=#{searchDto.fileName}</if>
			<if test="searchDto.softwareVersion != null  and searchDto.softwareVersion != ''"> and software_version=#{searchDto.softwareVersion}</if>
			<if test="searchDto.osVersion != null  and searchDto.osVersion != ''"> and os_version=#{searchDto.osVersion}</if>
			<if test="searchDto.ipArea != null  and searchDto.ipArea != ''"> and ip_area=#{searchDto.ipArea}</if>
			<if test="searchDto.deviceName != null  and searchDto.deviceName != ''"> and instr(device_name,#{searchDto.deviceName})&gt;0</if>
			<if test="searchDto.uploadDateStart != null"> and upload_time&gt;=#{searchDto.uploadDateStart}</if>
			<if test="searchDto.uploadDateEnd != null"> and upload_time&lt;#{searchDto.uploadDateEnd}</if>
		</where>
	</select>
	<delete id="delFalconPcCrashLogById" parameterType="String">
		delete from falcon_pc_crash_log where id = #{id}
	</delete>
	
	<select id="searchOsVersionList" resultType="java.lang.String">
		select distinct os_version from falcon_pc_crash_log order by os_version desc
	</select>
	
	<select id="searchIpAreaList" resultType="java.lang.String">
		select distinct ip_area from falcon_pc_crash_log order by ip_area asc
	</select>
	
	<select id="searchDeviceNameList" resultType="java.lang.String">
		select distinct device_name from falcon_pc_crash_log
	</select>
	
	
</mapper>
