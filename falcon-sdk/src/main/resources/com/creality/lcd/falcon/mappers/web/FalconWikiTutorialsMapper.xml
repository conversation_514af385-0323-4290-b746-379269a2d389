<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.web.FalconWikiTutorialsMapper">
    <resultMap id="FalconAdConfigResultMap" type="com.creality.lcd.falcon.pojo.entity.web.FalconWikiTutorials">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="tutorial_name" jdbcType="VARCHAR" property="tutorialName"/>
        <result column="tutorial_name_en" jdbcType="VARCHAR" property="tutorialNameEn"/>
        <result column="tutorial_desc" jdbcType="VARCHAR" property="tutorialDesc"/>
        <result column="tutorial_desc_en" jdbcType="VARCHAR" property="tutorialDescEn"/>
        <result column="tutorial_link" jdbcType="VARCHAR" property="tutorialLink"/>
        <result column="tutorial_link_cn" jdbcType="VARCHAR" property="tutorialLinkCn"/>
        <result column="tutorial_cover" jdbcType="VARCHAR" property="tutorialCover"/>
        <result column="click_num" jdbcType="INTEGER" property="click_num"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    
    <update id="clickTutorials">
    	update falcon_wiki_tutorials set click_num=click_num+1 where id=#{id}
    </update>
</mapper>