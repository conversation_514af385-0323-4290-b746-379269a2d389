<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.web.FalconImageGalleryMapper">
    <resultMap id="FalconAdConfigResultMap" type="com.creality.lcd.falcon.pojo.entity.web.FalconImageGallery">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="image_name" jdbcType="VARCHAR" property="imageName"/>
        <result column="image_url" jdbcType="VARCHAR" property="imageUrl"/>
        <result property="collectNum"    column="collect_num"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <insert id="batchInsertImages" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO aurora_db.falcon_image_gallery (category, label, image_name,
                                          image_url, creator, create_time, updater, update_time)
        VALUES
        <foreach collection="imageGalleryList" item="item" separator=",">
            (#{item.category}, #{item.label}, #{item.imageName},
             #{item.imageUrl}, #{item.creator}, CURRENT_TIMESTAMP, #{item.updater}, CURRENT_TIMESTAMP)
        </foreach>
    </insert>
    <select id="isCategoryExistInImage" parameterType="String" resultType="boolean">
        select count(1) > 0 from aurora_db.falcon_image_gallery where category = #{category}
    </select>
    
    <select id="selFalconImageList" resultType="com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo">
    	select g.id,g.category,g.image_name imageName,g.label,g.image_url imageUrl,g.create_time createTime
    	from falcon_image_gallery g
    	<if test="searchDto.collectType != null  and searchDto.collectType == 1">
    		left join falcon_image_collect c on g.id=c.img_id
    	</if>
    	<where>
    		<if test="searchDto.collectType != null  and searchDto.collectType == 1">
    			and c.user_id=#{searchDto.userId}
    		</if>
    		<if test="searchDto.category != null  and searchDto.category != '' ">
    			and instr(g.category,#{searchDto.category})&gt;0
    		</if>
    		<if test="searchDto.imgName != null  and searchDto.imgName != ''">
    			and instr(g.image_name,#{searchDto.imgName})&gt;0
    		</if>
    	</where>
    	
    	<if test="searchDto.sortType != null  and searchDto.sortType == 0 ">
    	order by g.create_time desc
    	</if>
    	<if test="searchDto.sortType != null  and searchDto.sortType == 1 ">
    	order by g.collect_num asc
    	</if>
    </select>
    
    <select id="selFalconImagePage" resultType="com.creality.lcd.falcon.pojo.vo.pc.ImageCollectVo">
    	select g.id,g.image_name imageName,g.label,g.image_url imageUrl
    	from falcon_image_gallery g
    	
    	<if test="searchDto.collectType != null  and searchDto.collectType == 1">
    		left join falcon_image_collect c on g.id=c.img_id
    	</if>
    	<where>
    		<if test="searchDto.collectType != null  and searchDto.collectType == 1">
    			and c.user_id=#{searchDto.userId}
    		</if>
    		<if test="searchDto.category != null  and searchDto.category != '' ">
    			and instr(g.category,#{searchDto.category})&gt;0
    		</if>
    		<if test="searchDto.imgName != null  and searchDto.imgName != ''">
    			and instr(g.image_name,#{searchDto.imgName})&gt;0
    		</if>
    	</where>
    	
    	<if test="searchDto.sortType != null  and searchDto.sortType == 0 ">
    	order by g.create_time desc
    	</if>
    	<if test="searchDto.sortType != null  and searchDto.sortType == 1 ">
    	order by g.collect_num asc
    	</if>
    </select>
    
    <update id="upCollectNum">
    	update falcon_image_gallery
    	<if test="collectNum != null  and collectNum &gt; 0">
    		set collect_num=(collect_num+1)
    		where id=#{imageId}
    	</if>
    	<if test="collectNum != null  and collectNum &lt; 0">
    		set collect_num=(collect_num-1)
    		where id=#{imageId} and collect_num&gt;0
    	</if>
    </update>
    
</mapper>