<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creality.lcd.falcon.mappers.pc.FalconImageUsersMapper">

	<resultMap type="com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers" id="FalconImageUsersResult">
		<result property="id"    column="id"/>
		<result property="userId"    column="user_id"/>
		<result property="imageName"    column="image_name"/>
		<result property="imageUrl"    column="image_url"/>
		<result property="imageUrlResize"    column="image_url_resize"/>
		<result property="delState"    column="del_state"/>
		<result property="createTime"    column="create_time"/>
	</resultMap>
	<sql id="selFalconImageUsers">
		select id,user_id,image_name,image_url,image_url_resize,del_state,create_time from falcon_image_users
	</sql>
	<insert id="addFalconImageUsers" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers">
		insert into falcon_image_users 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="userId != null">user_id,</if>
			<if test="imageName != null">image_name,</if>
			<if test="imageUrl != null">image_url,</if>
			<if test="imageUrlResize != null">image_url_resize,</if>
			<if test="delState != null">del_state,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="userId != null">#{userId},</if>
			<if test="imageName != null">#{imageName},</if>
			<if test="imageUrl != null">#{imageUrl},</if>
			<if test="imageUrlResize != null">#{imageUrlResize},</if>
			<if test="delState != null">#{delState},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>
	<update id="upFalconImageUsers" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers">
		update falcon_image_users 
		<trim prefix="SET" suffixOverrides=",">
			<if test="id != null">id=#{id},</if>
			<if test="userId != null">user_id=#{userId},</if>
			<if test="imageName != null">image_name=#{imageName},</if>
			<if test="imageUrl != null">image_url=#{imageUrl},</if>
			<if test="imageUrlResize != null">image_url_resize=#{imageUrlResize},</if>
			<if test="delState != null">del_state=#{delState},</if>
			<if test="createTime != null">create_time=#{createTime},</if>
		</trim>
		where id = #{id}
	</update>
	<select id="selFalconImageUsersPage" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers" resultMap="FalconImageUsersResult">
		<include refid="selFalconImageUsers"/>
		<where>
			<if test="falconImageUsers.id != null  and falconImageUsers.id != ''"> and id=#{falconImageUsers.id}</if>
			<if test="falconImageUsers.userId != null  and falconImageUsers.userId != ''"> and user_id=#{falconImageUsers.userId}</if>
			<if test="falconImageUsers.imageName != null  and falconImageUsers.imageName != ''"> and instr(image_name,#{falconImageUsers.imageName})&gt;0</if>
			<if test="falconImageUsers.imageUrl != null  and falconImageUsers.imageUrl != ''"> and image_url=#{falconImageUsers.imageUrl}</if>
			<if test="falconImageUsers.imageUrlResize != null  and falconImageUsers.imageUrlResize != ''"> and image_url_resize=#{falconImageUsers.imageUrlResize}</if>
			<if test="falconImageUsers.delState != null  and falconImageUsers.delState != ''"> and del_state=#{falconImageUsers.delState}</if>
			<if test="falconImageUsers.createTime != null  and falconImageUsers.createTime != ''"> and create_time=#{falconImageUsers.createTime}</if>
		</where>
		order by create_time desc
	</select>
	<select id="selFalconImageUsersList" parameterType="com.creality.lcd.falcon.pojo.entity.pc.FalconImageUsers" resultMap="FalconImageUsersResult">
		<include refid="selFalconImageUsers"/>
		<where>
			<if test="id != null"> and id=#{id}</if>
			<if test="userId != null"> and user_id=#{userId}</if>
			<if test="imageName != null">and instr(image_name,#{imageName})&gt;0</if>
			<if test="imageUrl != null  and imageUrl != ''"> and image_url=#{imageUrl}</if>
			<if test="imageUrlResize != null  and imageUrlResize != ''"> and image_url_resize=#{imageUrlResize}</if>
			<if test="delState != null"> and del_state=#{delState}</if>
			<if test="createTime != null"> and create_time=#{createTime}</if>
		</where>
		order by id desc
	</select>
	
	<delete id="delFalconImageUsersById">
		update falcon_image_users set del_state=(del_state+1)%2 where id = #{id} and user_id=#{userId}
	</delete>
</mapper>
