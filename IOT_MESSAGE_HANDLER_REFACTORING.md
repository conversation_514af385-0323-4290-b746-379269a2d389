# IoT消息处理器重构方案

## 🎯 重构目标

### 原始问题
- `onMessage`方法中包含大量if-else判断，代码冗长难维护
- 不同设备型号的处理逻辑混杂在一起
- 新增设备型号需要修改核心配置类
- 缺乏扩展性和可维护性

### 重构方案
采用**策略模式 + 责任链模式**，将不同设备型号的处理逻辑分离到独立的处理器中。

## 🏗️ 架构设计

### 1. 核心接口设计
```java
public interface IotMessageHandler {
    boolean supports(String deviceModel);           // 是否支持该设备型号
    void handleMessage(String topic, Map<String, Object> messageMap); // 处理消息
    default int getPriority() { return 100; }       // 处理器优先级
}
```

### 2. 抽象基类
```java
public abstract class AbstractIotMessageHandler implements IotMessageHandler {
    // 通用的消息分发逻辑
    // 抽象方法：extractModel(), extractSn()
    // 可重写方法：handleOtaRequest(), handleDeviceStatusReport()等
}
```

### 3. 具体处理器
- **LaserEngraverMessageHandler**: 激光雕刻机处理器
- **AirPurifierMessageHandler**: 空气净化器处理器  
- **DefaultMessageHandler**: 默认处理器（兜底）

### 4. 管理器
```java
@Component
public class IotMessageHandlerManager {
    // 自动注入所有处理器
    // 根据设备型号选择合适的处理器
    // 按优先级排序
}
```

## 📁 文件结构

```
falcon-iot/src/main/java/com/creality/lcd/falcon/service/handler/
├── IotMessageHandler.java                    # 核心接口
├── AbstractIotMessageHandler.java            # 抽象基类
├── LaserEngraverMessageHandler.java          # 雕刻机处理器
├── AirPurifierMessageHandler.java            # 空气净化器处理器
├── DefaultMessageHandler.java                # 默认处理器
└── IotMessageHandlerManager.java             # 处理器管理器
```

## 🔧 核心实现

### 1. 消息分发流程
```java
// 原来的方式
@PostConstruct
public void init() {
    mqttPublishEvents.addListener(new MqttMessageListener() {
        @Override
        public void onMessage(String topic, Map<String, Object> messageMap) {
            // 100多行if-else判断...
        }
    });
}

// 重构后的方式
@PostConstruct
public void init() {
    mqttPublishEvents.addListener(new MqttMessageListener() {
        @Override
        public void onMessage(String topic, Map<String, Object> messageMap) {
            messageHandlerManager.handleMessage(topic, messageMap);
        }
    });
}
```

### 2. 设备型号识别
```java
private String extractDeviceModel(String topic) {
    String[] parts = topic.split("/");
    
    // 雕刻机格式: device/{model}/{sn}/...
    if (parts.length >= 2 && "device".equals(parts[0])) {
        return parts[1];
    }
    
    // 空气净化器格式: {version}/device/ap/{sn}/...
    if (parts.length >= 3 && "device".equals(parts[1]) && "ap".equals(parts[2])) {
        return "ap";
    }
    
    return null;
}
```

### 3. 处理器选择
```java
private Optional<IotMessageHandler> findHandler(String deviceModel) {
    return messageHandlers.stream()
            .filter(handler -> handler.supports(deviceModel))
            .findFirst(); // 按优先级排序，选择第一个匹配的
}
```

## 🎨 设计模式应用

### 1. **策略模式**
- **Context**: `IotMessageHandlerManager`
- **Strategy**: `IotMessageHandler`接口
- **ConcreteStrategy**: 各种具体的处理器

### 2. **责任链模式**
- 按优先级排序的处理器链
- 第一个支持的处理器处理消息
- 默认处理器作为兜底

### 3. **模板方法模式**
- `AbstractIotMessageHandler`定义处理流程
- 子类实现具体的处理逻辑

## 📊 重构效果对比

### 重构前
```java
// IotMessageListenerConfig.java - 146行
public void onMessage(String topic, Map<String, Object> messageMap) {
    //1、雕刻机 - 65行if-else
    if (topic.matches("^device/.+/.+/OTA/reqVersion/"+ version +"$")) {
        String model = topic.split("/")[1];
        iotService.getOtaVersion(model, messageMap);
    }
    // ... 12个雕刻机相关的if判断
    
    //2、空气净化器 - 25行if-else  
    if (topic.matches("^"+version+"/device/ap/.+/info/online"+"$")) {
        String apSn = topic.split("/")[3];
        iotApService.updateApDeviceOnline(apSn,messageMap);
    }
    // ... 5个空气净化器相关的if判断
}
```

### 重构后
```java
// IotMessageListenerConfig.java - 42行
public void onMessage(String topic, Map<String, Object> messageMap) {
    messageHandlerManager.handleMessage(topic, messageMap);
}

// LaserEngraverMessageHandler.java - 专门处理雕刻机
// AirPurifierMessageHandler.java - 专门处理空气净化器
// DefaultMessageHandler.java - 处理未知设备
```

## ✅ 重构优势

### 1. **可维护性提升**
- ✅ 单一职责：每个处理器只处理一种设备类型
- ✅ 代码分离：不同设备的逻辑完全分离
- ✅ 易于调试：问题定位更精确

### 2. **扩展性增强**
- ✅ 新增设备：只需添加新的处理器，无需修改现有代码
- ✅ 功能扩展：在具体处理器中添加新功能
- ✅ 配置灵活：通过优先级控制处理顺序

### 3. **代码质量改善**
- ✅ 消除重复：公共逻辑抽取到基类
- ✅ 类型安全：编译时检查处理器注册
- ✅ 测试友好：每个处理器可独立测试

## 🚀 使用示例

### 1. 添加新设备类型
```java
@Component
public class NewDeviceMessageHandler extends AbstractIotMessageHandler {
    
    @Override
    public boolean supports(String deviceModel) {
        return deviceModel.startsWith("NEW-");
    }
    
    @Override
    public int getPriority() {
        return 15; // 设置优先级
    }
    
    @Override
    protected String extractModel(String[] topicParts) {
        return topicParts[1]; // 根据topic格式提取
    }
    
    @Override
    protected String extractSn(String[] topicParts) {
        return topicParts[2]; // 根据topic格式提取
    }
    
    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        // 新设备的上线处理逻辑
        log.info("新设备上线: model={}, sn={}", model, sn);
        // 调用相应的service方法
    }
}
```

### 2. 自定义消息类型
```java
@Override
protected void handleCustomMessage(String topic, Map<String, Object> messageMap) {
    if (topic.contains("/custom/newFeature/")) {
        handleNewFeature(topic, messageMap);
    } else {
        super.handleCustomMessage(topic, messageMap);
    }
}

private void handleNewFeature(String topic, Map<String, Object> messageMap) {
    // 处理新功能消息
}
```

## 🔍 监控和调试

### 1. 处理器注册日志
```
初始化IoT消息处理器管理器，共注册4个处理器:
  - LaserEngraverMessageHandler: 优先级=10
  - AirPurifierMessageHandler: 优先级=20
  - DefaultMessageHandler: 优先级=2147483647
```

### 2. 消息处理日志
```
使用处理器 LaserEngraverMessageHandler 处理消息: topic=device/CV-30/SN123/info/online/v1, model=CV-30
雕刻机上线上报: model=CV-30, sn=SN123
```

## 📈 性能影响

### 1. **处理器查找**
- 时间复杂度：O(n)，n为处理器数量
- 实际影响：处理器数量少（<10个），性能影响可忽略
- 优化方案：可考虑使用Map缓存设备型号到处理器的映射

### 2. **内存占用**
- 增加：每个处理器实例的内存占用
- 减少：消除了大量重复的字符串匹配逻辑
- 总体：内存占用略有增加，但可接受

## 🎯 总结

这次重构成功地将原来146行的复杂`onMessage`方法，重构为：
- **1个简洁的入口方法**（10行）
- **4个专门的处理器类**（每个100-200行）
- **1个统一的管理器**（100行）

实现了：
- ✅ **代码可维护性**大幅提升
- ✅ **扩展性**显著增强  
- ✅ **职责分离**更加清晰
- ✅ **测试覆盖**更加容易

为IoT消息处理系统的长期发展奠定了坚实的架构基础！
