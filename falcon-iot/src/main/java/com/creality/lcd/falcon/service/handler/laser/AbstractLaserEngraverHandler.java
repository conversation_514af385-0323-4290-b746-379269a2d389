package com.creality.lcd.falcon.service.handler.laser;

import com.creality.lcd.falcon.service.IotService;
import com.creality.lcd.falcon.service.handler.AbstractIotMessageHandler;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 16:40
 * @Description: 激光雕刻机抽象处理器基类
 */
public abstract class AbstractLaserEngraverHandler extends AbstractIotMessageHandler {

    @Autowired
    protected IotService iotService;

    // 移除优先级设置，使用默认优先级

    /**
     * 检查是否为Falcon系列雕刻机（A1 Pro和T1）
     */
    protected boolean isFalconSeries(String deviceModel) {
        return "Creality Falcon A1 Pro".equals(deviceModel) ||
               "Creality Falcon T1".equals(deviceModel);
    }

    @Override
    protected String extractModel(String[] topicParts) {
        // 雕刻机topic格式: device/{model}/{sn}/...
        return topicParts.length > 1 ? topicParts[1] : "";
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 雕刻机topic格式: device/{model}/{sn}/...
        return topicParts.length > 2 ? topicParts[2] : "";
    }

    // ==================== 雕刻机通用处理方法 ====================

    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机OTA请求: model={}, sn={}", model, sn);
        iotService.getOtaVersion(model, messageMap);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机状态上报: model={}, sn={}", model, sn);
        iotService.updateDeviceWorkStatus(sn, messageMap);
    }

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机上线上报: model={}, sn={}", model, sn);
        iotService.updateDeviceOnline(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机离线上报: model={}, sn={}", model, sn);
        iotService.offLine(messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机IP上报: model={}, sn={}", model, sn);
        iotService.ipReport(model, sn, messageMap);
    }

    @Override
    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机OTA状态上报: model={}, sn={}", model, sn);
        iotService.updateOtaStatus(model, sn, messageMap);
    }

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机文件传输状态上报: model={}, sn={}", model, sn);
        iotService.updateFileTransferStatus(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机断开连接: model={}, sn={}", model, sn);
        iotService.deviceDisConnect(model, sn, messageMap);
    }

    @Override
    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机云文件列表请求: model={}, sn={}", model, sn);
        iotService.getUserCloudFileList(model, sn, messageMap);
    }

    @Override
    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机扫码上报: model={}, sn={}", model, sn);
        iotService.putNewScanCode(model, sn, messageMap);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机告警上报: model={}, sn={}", model, sn);
        iotService.putAlarm(model, sn, messageMap);
    }

    @Override
    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机埋点数据上报: model={}, sn={}", model, sn);
        iotService.putBuriedData(model, sn, messageMap);
    }

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机加工数据上报: model={}, sn={}", model, sn);
        iotService.putJobDescData(model, sn, messageMap);
    }

    // ==================== 雕刻机通用Topic判断方法 ====================

    @Override
    protected boolean isOtaRequest(String topic) {
        return topic.matches("^device/.+/.+/OTA/reqVersion/" + version + "$");
    }

    @Override
    protected boolean isDeviceStatusReport(String topic) {
        return topic.matches("^device/.+/.+/info/putDeviceStatus/" + version + "$");
    }

    @Override
    protected boolean isDeviceOnlineReport(String topic) {
        return topic.matches("^device/.+/.+/info/online/" + version + "$");
    }

    @Override
    protected boolean isDeviceOfflineReport(String topic) {
        return topic.matches("^device/.+/.+/info/offLine/" + version + "$");
    }

    @Override
    protected boolean isIpReport(String topic) {
        return topic.matches("^device/.+/.+/info/putNewIPConnect/" + version + "$");
    }

    @Override
    protected boolean isOtaStatusReport(String topic) {
        return topic.matches("^device/.+/.+/OTA/putOTAStatus/" + version + "$");
    }

    @Override
    protected boolean isFileTransferStatusReport(String topic) {
        return topic.matches("^device/.+/.+/info/putFileTransferStatus/" + version + "$");
    }

    @Override
    protected boolean isDeviceDisconnect(String topic) {
        return topic.matches("^device/.+/.+/info/disConnect/" + version + "$");
    }

    @Override
    protected boolean isCloudFileListRequest(String topic) {
        return topic.matches("^device/.+/.+/info/reqCloudFileList/" + version + "$");
    }

    @Override
    protected boolean isScanCodeReport(String topic) {
        return topic.matches("^device/.+/.+/info/putNewScanCode/" + version + "$");
    }

    @Override
    protected boolean isAlarmReport(String topic) {
        return topic.matches("^device/.+/.+/info/putAlarm/" + version + "$");
    }

    @Override
    protected boolean isBuriedDataReport(String topic) {
        return topic.matches("^device/.+/.+/info/putBuriedData/" + version + "$");
    }

    @Override
    protected boolean isJobDescDataReport(String topic) {
        return topic.matches("^device/.+/.+/info/putJobDescData/" + version + "$");
    }

    // ==================== 子类可重写的特殊处理方法 ====================

    /**
     * 处理型号特有的消息
     * 子类可以重写此方法来处理特定型号的特殊消息
     */
    protected void handleModelSpecificMessage(String topic, Map<String, Object> messageMap) {
        log.debug("型号 {} 没有特殊消息处理逻辑: topic={}", getSupportedModel(), topic);
    }

    /**
     * 获取支持的型号名称
     * 子类必须实现此方法
     */
    protected abstract String getSupportedModel();

    /**
     * 检查是否为型号特有的topic
     * 子类可以重写此方法来识别特殊的topic格式
     */
    protected boolean isModelSpecificTopic(String topic) {
        return false;
    }

    @Override
    protected void handleCustomMessage(String topic, Map<String, Object> messageMap) {
        if (isModelSpecificTopic(topic)) {
            handleModelSpecificMessage(topic, messageMap);
        } else {
            super.handleCustomMessage(topic, messageMap);
        }
    }
}
