package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotApService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: 空气净化器消息处理器
 */
@Component
public class AirPurifierMessageHandler extends AbstractIotMessageHandler {

    @Autowired
    private IotApService iotApService;

    // 支持的空气净化器型号
    private static final Set<String> SUPPORTED_MODELS = Set.of(
        "Creality Falcon AP"
    );

    @Override
    public boolean supports(String deviceModel) {
        // 精确匹配支持的型号
        if (SUPPORTED_MODELS.contains(deviceModel)) {
            return true;
        }

        // 兼容性匹配：支持包含AP的Falcon设备
        return deviceModel != null &&
               deviceModel.contains("Falcon") &&
               deviceModel.contains("AP");
    }

    // 移除优先级设置，使用默认优先级

    @Override
    protected String extractModel(String[] topicParts) {
        // 空气净化器topic格式: {version}/device/ap/{sn}/...
        return "ap"; // 空气净化器统一使用ap作为model
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 空气净化器topic格式: {version}/device/ap/{sn}/...
        return topicParts.length > 3 ? topicParts[3] : "";
    }

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器上线上报: model={}, sn={}", model, sn);
        iotApService.updateApDeviceOnline(sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器离线上报: model={}, sn={}", model, sn);
        iotApService.apOffLine(messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器IP上报: model={}, sn={}", model, sn);
        iotApService.apIpReport(sn, messageMap);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器状态上报: model={}, sn={}", model, sn);
        iotApService.updateDeviceWorkStatus(sn, messageMap);
    }

    /**
     * 处理滤芯寿命变化上报
     */
    protected void handleFilterLifeChange(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器滤芯寿命上报: model={}, sn={}", model, sn);
        iotApService.putFilterLifeChange(sn, messageMap);
    }

    @Override
    protected void handleFilterLifeChange(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器滤芯寿命上报: model={}, sn={}", model, sn);
        iotApService.putFilterLifeChange(sn, messageMap);
    }

    // ==================== 空气净化器特有的Topic判断方法 ====================

    @Override
    protected boolean isDeviceOnlineReport(String topic) {
        return topic.matches("^" + version + "/device/ap/.+/info/online$");
    }

    @Override
    protected boolean isDeviceOfflineReport(String topic) {
        return topic.matches("^" + version + "/device/ap/.+/info/offLine$");
    }

    @Override
    protected boolean isIpReport(String topic) {
        return topic.matches("^" + version + "/device/ap/.+/info/putIPChange$");
    }

    @Override
    protected boolean isDeviceStatusReport(String topic) {
        return topic.matches("^" + version + "/device/ap/.+/info/putApDeviceStatus$");
    }

    protected boolean isFilterLifeChangeReport(String topic) {
        return topic.matches("^" + version + "/device/ap/.+/info/putFilterLifeChange$");
    }
}
