package com.creality.lcd.falcon.service.handler;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 20:00
 * @Description: IoT消息Topic常量定义
 * 统一管理所有Topic路径，避免硬编码字符串
 */
public final class MessageTopicConstants {

    private MessageTopicConstants() {
        // 工具类，禁止实例化
    }

    // ==================== OTA相关 ====================
    public static final String OTA_REQ_VERSION = "/OTA/reqVersion/";
    public static final String OTA_PUT_STATUS = "/OTA/putOTAStatus/";

    // ==================== 设备状态相关 ====================
    public static final String INFO_PUT_DEVICE_STATUS = "/info/putDeviceStatus/";
    public static final String INFO_PUT_AP_DEVICE_STATUS = "/info/putApDeviceStatus";
    public static final String INFO_ONLINE = "/info/online";
    public static final String INFO_OFFLINE = "/info/offLine";

    // ==================== 网络相关 ====================
    public static final String INFO_PUT_NEW_IP_CONNECT = "/info/putNewIPConnect/";
    public static final String INFO_PUT_IP_CHANGE = "/info/putIPChange";

    // ==================== 文件传输相关 ====================
    public static final String INFO_PUT_FILE_TRANSFER_STATUS = "/info/putFileTransferStatus/";
    public static final String INFO_REQ_CLOUD_FILE_LIST = "/info/reqCloudFileList/";

    // ==================== 设备操作相关 ====================
    public static final String INFO_DISCONNECT = "/info/disConnect/";
    public static final String INFO_PUT_NEW_SCAN_CODE = "/info/putNewScanCode/";

    // ==================== 告警和数据上报 ====================
    public static final String INFO_PUT_ALARM = "/info/putAlarm/";
    public static final String INFO_PUT_BURIED_DATA = "/info/putBuriedData/";
    public static final String INFO_PUT_JOB_DESC_DATA = "/info/putJobDescData/";

    // ==================== 空气净化器特有 ====================
    public static final String INFO_PUT_FILTER_LIFE_CHANGE = "/info/putFilterLifeChange";
    public static final String INFO_PUT_AIR_QUALITY = "/info/putAirQuality";

    // ==================== 雕刻机特有 ====================
    public static final String INFO_PUT_SPECIAL_FEATURE = "/info/putSpecialFeature/";

    // ==================== 设备类型识别 ====================
    public static final String DEVICE_PREFIX = "device/";
    public static final String AP_DEVICE_PATH = "/device/ap/";
    public static final String FALCON_KEYWORD = "Falcon";
    public static final String AP_KEYWORD = "AP";

    // ==================== 设备型号常量 ====================
    public static final String MODEL_FALCON_A1_PRO = "Creality Falcon A1 Pro";
    public static final String MODEL_FALCON_T1 = "Creality Falcon T1";
    public static final String MODEL_FALCON_AP = "Creality Falcon AP";

    // ==================== 工具方法 ====================

    /**
     * 检查topic是否包含指定路径
     */
    public static boolean containsPath(String topic, String path) {
        return topic != null && topic.contains(path);
    }

    /**
     * 检查是否为雕刻机设备
     */
    public static boolean isLaserEngraver(String topic) {
        return topic != null && 
               topic.startsWith(DEVICE_PREFIX) && 
               topic.contains(FALCON_KEYWORD) && 
               !topic.contains(AP_DEVICE_PATH);
    }

    /**
     * 检查是否为空气净化器设备
     */
    public static boolean isAirPurifier(String topic) {
        return topic != null && topic.contains(AP_DEVICE_PATH);
    }

    /**
     * 检查是否为A1 Pro型号
     */
    public static boolean isA1Pro(String model) {
        return MODEL_FALCON_A1_PRO.equals(model);
    }

    /**
     * 检查是否为T1型号
     */
    public static boolean isT1(String model) {
        return MODEL_FALCON_T1.equals(model);
    }

    /**
     * 检查是否为空气净化器型号
     */
    public static boolean isAP(String model) {
        return MODEL_FALCON_AP.equals(model) ||
               (model != null && model.contains(FALCON_KEYWORD) && model.contains(AP_KEYWORD));
    }
}
