package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.util.MqttSdkClient;
import jakarta.annotation.PostConstruct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: tiantao
 * @Date: 2024/12/31 0:33
 * @Description:
 */
@Component
@DependsOn("mqttSdkClient")
public class IotSubscribeConfig {
    private static final Logger log = LogManager.getLogger(IotSubscribeConfig.class);

    // 改为实例字段，不再使用 static final
    private String[] subscriptionTopics;

    // 从配置文件中读取版本号，例如在 application.yml 中配置 iot.topic.version: test 或 v1
    @Value("${iot.topic.version}")
    private String version;

    @Autowired
    private MqttSdkClient mqttSdkClient;

    @PostConstruct
    public void init() {
        // 根据配置动态生成各个 topic 字符串
        subscriptionTopics = new String[] {
                "$share/group1/device/+/+/OTA/reqVersion/" + version,
                "$share/group1/device/+/+/info/putDeviceStatus/" + version,
                "$share/group1/device/+/+/info/online/" + version,
                "$share/group1/device/+/+/info/offLine/" + version,
                "$share/group1/device/+/+/info/putNewIPConnect/" + version,
                "$share/group1/device/+/+/OTA/putOTAStatus/" + version,
                "device/+/+/info/commonResp/" + version,
                "$share/group1/device/+/+/info/putFileTransferStatus/" + version,
                "$share/group1/device/+/+/info/disConnect/" + version,
                "$share/group1/device/+/+/info/reqCloudFileList/" + version,
                "$share/group1/device/+/+/info/putNewScanCode/" + version,
                "$share/group1/device/+/+/info/putAlarm/" + version,
                "device/+/+/info/putPosition/" + version,
                "device/+/+/control/respCapturePic/" + version,
                "device/+/+/control/respCalibrationValue/" + version,
                "$share/group1/device/+/+/info/putBuriedData/" + version,
                "$share/group1/device/+/+/info/putJobDescData/" + version,
                //空气净化器
                version+"/device/ap/+/info/commonRespToServer",
                "$share/group1/"+version+"/device/ap/+/info/online",
                "$share/group1/"+version+"/device/ap/+/info/offLine",
                "$share/group1/"+version+"/device/ap/+/info/putIPChange",
                "$share/group1/"+version+"/device/ap/+/info/putFilterLifeChange",
                "$share/group1/"+version+"/device/ap/+/info/putApDeviceStatus"
        };
        log.info("[MQTT] Subscription topics: {}", (Object) subscriptionTopics);
    }

    @EventListener(MqttConnectionSuccessEvent.class)
    public void handleMqttConnectionSuccess(MqttConnectionSuccessEvent event) {
        log.info("[MQTT] 收到连接事件，开始异步订阅...");

        CompletableFuture.runAsync(() -> {
            for (String topic : subscriptionTopics) {
                try {
                    // 添加订阅状态检查
                    if (!event.getClient().getIsConnected()) {
                        log.warn("客户端未连接，延迟重试订阅: {}", topic);
                        Thread.sleep(500);
                    }
                    // 最大重试3次
                    subscribeWithRetry(topic, 3);
                } catch (InterruptedException e) {
                    log.error("订阅线程中断", e);
                }
            }
        });
    }

    private void subscribeWithRetry(String topic, int maxRetries) {
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                mqttSdkClient.subscribeTopic(topic);
                // 订阅成功则退出
                return;
            } catch (Exception e) {
                retryCount++;
                log.error("[MQTT] 订阅失败 {} (重试{}/{}): {}", topic, retryCount, maxRetries, e.getMessage());
                try {
                    Thread.sleep(1000L * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
