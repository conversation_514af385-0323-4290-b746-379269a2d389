package com.creality.lcd.falcon.service.handler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: IoT消息处理器管理器
 */
@Component
public class IotMessageHandlerManager {

    private static final Logger log = LogManager.getLogger(IotMessageHandlerManager.class);

    @Autowired
    private List<IotMessageHandler> messageHandlers;

    @PostConstruct
    public void init() {
        log.info("初始化IoT消息处理器管理器，共注册{}个处理器:", messageHandlers.size());
        for (IotMessageHandler handler : messageHandlers) {
            log.info("  - {}", handler.getClass().getSimpleName());
        }
    }

    /**
     * 处理IoT消息
     * @param topic MQTT主题
     * @param messageMap 消息内容
     */
    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            // 从topic中提取设备型号
            String deviceModel = extractDeviceModel(topic);
            
            if (deviceModel == null || deviceModel.isEmpty()) {
                log.warn("无法从topic中提取设备型号: {}", topic);
                return;
            }

            // 查找合适的处理器
            Optional<IotMessageHandler> handler = findHandler(deviceModel);
            
            if (handler.isPresent()) {
                log.debug("使用处理器 {} 处理消息: topic={}, model={}", 
                         handler.get().getClass().getSimpleName(), topic, deviceModel);
                handler.get().handleMessage(topic, messageMap);
            } else {
                log.warn("未找到合适的处理器处理设备型号: {}, topic: {}", deviceModel, topic);
                handleUnknownMessage(topic, messageMap);
            }
            
        } catch (Exception e) {
            log.error("处理IoT消息异常: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    /**
     * 从topic中提取设备型号
     * @param topic MQTT主题
     * @return 设备型号
     */
    private String extractDeviceModel(String topic) {
        try {
            String[] parts = topic.split("/");

            // 雕刻机格式: device/{model}/{sn}/...
            // 例如: device/Creality Falcon A1 Pro/SN123/info/online/v1
            if (parts.length >= 2 && "device".equals(parts[0])) {
                String model = parts[1];

                // 处理可能的URL编码或特殊字符
                model = java.net.URLDecoder.decode(model, "UTF-8");

                log.debug("从topic提取到雕刻机型号: {}", model);
                return model;
            }

            // 空气净化器格式: {version}/device/ap/{sn}/...
            // 例如: v1/device/ap/SN456/info/online
            if (parts.length >= 3 && "device".equals(parts[1]) && "ap".equals(parts[2])) {
                log.debug("从topic识别到空气净化器");
                return "Creality Falcon AP"; // 统一返回标准型号名称
            }

            log.warn("无法识别的topic格式: {}", topic);
            return null;
        } catch (Exception e) {
            log.error("提取设备型号异常: topic={}, error={}", topic, e.getMessage());
            return null;
        }
    }

    /**
     * 查找合适的处理器
     * @param deviceModel 设备型号
     * @return 处理器
     */
    private Optional<IotMessageHandler> findHandler(String deviceModel) {
        return messageHandlers.stream()
                .filter(handler -> handler.supports(deviceModel))
                .findFirst();
    }

    /**
     * 处理未知消息
     * @param topic MQTT主题
     * @param messageMap 消息内容
     */
    private void handleUnknownMessage(String topic, Map<String, Object> messageMap) {
        log.warn("收到未知设备消息: topic={}, message={}", topic, messageMap);
        // 可以在这里添加默认处理逻辑或者发送到死信队列
    }

    /**
     * 获取所有注册的处理器
     * @return 处理器列表
     */
    public List<IotMessageHandler> getAllHandlers() {
        return messageHandlers;
    }

    /**
     * 根据设备型号获取处理器
     * @param deviceModel 设备型号
     * @return 处理器
     */
    public Optional<IotMessageHandler> getHandler(String deviceModel) {
        return findHandler(deviceModel);
    }
}
