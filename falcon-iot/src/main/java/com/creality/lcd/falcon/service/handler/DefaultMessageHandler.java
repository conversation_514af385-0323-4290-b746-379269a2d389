package com.creality.lcd.falcon.service.handler;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: 默认消息处理器，处理未知设备型号的消息
 */
@Component
public class DefaultMessageHandler extends AbstractIotMessageHandler {

    @Override
    public boolean supports(String deviceModel) {
        // 默认处理器支持所有设备型号，但优先级最低
        return true;
    }

    // 移除优先级设置，使用默认优先级

    @Override
    protected String extractModel(String[] topicParts) {
        // 尝试从不同格式的topic中提取model
        if (topicParts.length >= 2 && "device".equals(topicParts[0])) {
            return topicParts[1]; // device/{model}/{sn}/...
        }
        if (topicParts.length >= 3 && "device".equals(topicParts[1])) {
            return topicParts[2]; // {version}/device/{model}/{sn}/...
        }
        return "unknown";
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 尝试从不同格式的topic中提取sn
        if (topicParts.length >= 3 && "device".equals(topicParts[0])) {
            return topicParts[2]; // device/{model}/{sn}/...
        }
        if (topicParts.length >= 4 && "device".equals(topicParts[1])) {
            return topicParts[3]; // {version}/device/{model}/{sn}/...
        }
        return "unknown";
    }

    @Override
    protected void handleCustomMessage(String topic, Map<String, Object> messageMap) {
        log.warn("默认处理器收到未知消息: topic={}, message={}", topic, messageMap);
        // 可以在这里添加通用的处理逻辑，比如：
        // 1. 记录到数据库
        // 2. 发送到监控系统
        // 3. 转发到死信队列
    }

    // 默认处理器对所有消息类型都只记录日志，不做具体处理
    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - OTA请求: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 设备状态上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 设备上线上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 设备离线上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - IP上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - OTA状态上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 文件传输状态上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 设备断开连接: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 云文件列表请求: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 扫码上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 告警上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 埋点数据上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("默认处理器 - 加工数据上报: model={}, sn={}, topic包含未知设备型号", model, sn);
    }
}
