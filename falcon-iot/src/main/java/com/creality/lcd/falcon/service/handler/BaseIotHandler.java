package com.creality.lcd.falcon.service.handler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

import static com.creality.lcd.falcon.service.handler.MessageTopicConstants.*;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: IoT消息处理基类 - 极简版
 * 包含所有设备通用的处理方法，子类只需要重写不同的方法
 */
public abstract class BaseIotHandler {

    protected static final Logger log = LogManager.getLogger(BaseIotHandler.class);

    @Value("${iot.topic.version}")
    protected String version;

    // ==================== 抽象方法 - 子类必须实现 ====================

    /**
     * 判断是否支持该设备型号
     */
    public abstract boolean supports(String deviceModel);

    /**
     * 获取对应的Service实例
     */
    protected abstract Object getService();

    // ==================== 通用处理方法 - 子类可重写 ====================

    /**
     * 统一消息处理入口
     */
    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            String[] parts = topic.split("/");
            String model = extractModel(parts);
            String sn = extractSn(parts);

            log.info("处理{}消息: model={}, sn={}, topic={}", getDeviceType(), model, sn, topic);

            // 使用简单字符串匹配分发消息
            dispatchMessage(topic, model, sn, messageMap);

        } catch (Exception e) {
            log.error("处理{}消息异常: topic={}, error={}", getDeviceType(), topic, e.getMessage(), e);
        }
    }

    /**
     * 消息分发 - 子类可重写添加特有消息类型
     */
    protected void dispatchMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        // 通用消息类型处理 - 使用常量避免硬编码
        if (containsPath(topic, OTA_REQ_VERSION)) {
            handleOtaRequest(model, sn, messageMap);
        } else if (containsPath(topic, OTA_PUT_STATUS)) {
            handleOtaStatusReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_DEVICE_STATUS) || containsPath(topic, INFO_PUT_AP_DEVICE_STATUS)) {
            handleDeviceStatusReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_ONLINE)) {
            handleDeviceOnlineReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_OFFLINE)) {
            handleDeviceOfflineReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_NEW_IP_CONNECT) || containsPath(topic, INFO_PUT_IP_CHANGE)) {
            handleIpReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_FILE_TRANSFER_STATUS)) {
            handleFileTransferStatusReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_DISCONNECT)) {
            handleDeviceDisconnect(model, sn, messageMap);
        } else if (containsPath(topic, INFO_REQ_CLOUD_FILE_LIST)) {
            handleCloudFileListRequest(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_NEW_SCAN_CODE)) {
            handleScanCodeReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_ALARM)) {
            handleAlarmReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_BURIED_DATA)) {
            handleBuriedDataReport(model, sn, messageMap);
        } else if (containsPath(topic, INFO_PUT_JOB_DESC_DATA)) {
            handleJobDescDataReport(model, sn, messageMap);
        } else {
            // 子类特有消息处理
            handleCustomMessage(topic, model, sn, messageMap);
        }
    }

    // ==================== 通用处理方法 - 使用反射调用Service ====================

    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_GET_OTA_VERSION, model, messageMap);
    }

    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_UPDATE_OTA_STATUS, model, sn, messageMap);
    }

    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_UPDATE_DEVICE_WORK_STATUS, sn, messageMap);
    }

    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_UPDATE_DEVICE_ONLINE, model, sn, messageMap);
    }

    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_OFF_LINE, messageMap);
    }

    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_IP_REPORT, model, sn, messageMap);
    }

    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_UPDATE_FILE_TRANSFER_STATUS, model, sn, messageMap);
    }

    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_DEVICE_DISCONNECT, model, sn, messageMap);
    }

    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_GET_USER_CLOUD_FILE_LIST, model, sn, messageMap);
    }

    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_PUT_NEW_SCAN_CODE, model, sn, messageMap);
    }

    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_PUT_ALARM, model, sn, messageMap);
    }

    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_PUT_BURIED_DATA, model, sn, messageMap);
    }

    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        callServiceMethod(METHOD_PUT_JOB_DESC_DATA, model, sn, messageMap);
    }

    /**
     * 子类特有消息处理 - 子类重写
     */
    protected void handleCustomMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        log.warn("未识别的{}消息: topic={}", getDeviceType(), topic);
    }

    // ==================== 工具方法 ====================

    /**
     * 通用的Service方法调用
     */
    protected void callServiceMethod(String methodName, Object... args) {
        try {
            Object service = getService();
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i].getClass();
                // 处理基本类型
                if (paramTypes[i] == String.class) continue;
                if (paramTypes[i] == Map.class) continue;
            }
            
            service.getClass().getMethod(methodName, paramTypes).invoke(service, args);
            
        } catch (Exception e) {
            log.error("调用{}Service方法失败: method={}, error={}", getDeviceType(), methodName, e.getMessage());
        }
    }

    /**
     * 提取设备型号 - 子类可重写
     */
    protected abstract String extractModel(String[] topicParts);

    /**
     * 提取设备序列号 - 子类可重写
     */
    protected abstract String extractSn(String[] topicParts);

    /**
     * 获取设备类型名称 - 用于日志
     */
    protected abstract String getDeviceType();
}
