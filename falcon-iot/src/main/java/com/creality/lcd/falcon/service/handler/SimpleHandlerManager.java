package com.creality.lcd.falcon.service.handler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

import static com.creality.lcd.falcon.service.handler.MessageTopicConstants.*;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: 简单的处理器管理器
 * 自动发现所有Handler，根据设备型号分发消息
 */
@Component
public class SimpleHandlerManager {

    private static final Logger log = LogManager.getLogger(SimpleHandlerManager.class);

    @Autowired
    private List<BaseIotHandler> handlers;

    @PostConstruct
    public void init() {
        log.info("初始化简单处理器管理器，共注册{}个处理器:", handlers.size());
        for (BaseIotHandler handler : handlers) {
            log.info("  - {}", handler.getClass().getSimpleName());
        }
    }

    /**
     * 处理消息 - 自动选择合适的处理器
     */
    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            // 提取设备型号
            String deviceModel = extractDeviceModel(topic);
            
            // 查找支持该设备型号的处理器
            BaseIotHandler handler = findHandler(deviceModel);
            
            if (handler != null) {
                log.info("使用处理器 {} 处理消息", handler.getClass().getSimpleName());
                handler.handleMessage(topic, messageMap);
            } else {
                log.warn("未找到支持设备型号 {} 的处理器: topic={}", deviceModel, topic);
            }
            
        } catch (Exception e) {
            log.error("处理消息异常: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    /**
     * 查找支持指定设备型号的处理器
     */
    private BaseIotHandler findHandler(String deviceModel) {
        return handlers.stream()
            .filter(handler -> handler.supports(deviceModel))
            .findFirst()
            .orElse(null);
    }

    /**
     * 从topic中提取设备型号
     */
    private String extractDeviceModel(String topic) {
        if (topic == null || topic.isEmpty()) {
            return "";
        }

        String[] parts = topic.split("/");

        // 雕刻机格式: device/{model}/{sn}/...
        if (parts.length >= 2 && "device".equals(parts[0])) {
            return parts[1];
        }

        // 空气净化器格式: {version}/device/ap/{sn}/...
        if (parts.length >= 3 && "device".equals(parts[1]) && "ap".equals(parts[2])) {
            return "Creality Falcon AP";
        }

        return "";
    }
}
