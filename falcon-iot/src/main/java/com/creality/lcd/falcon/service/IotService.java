package com.creality.lcd.falcon.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.creality.lcd.falcon.config.IotDeviceTopicConfig;
import com.creality.lcd.falcon.enums.DeviceKindEnum;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.framework.redis.RedisLock;
import com.creality.lcd.falcon.mappers.account.FalconAccountMapper;
import com.creality.lcd.falcon.mappers.web.FalconFirmVersionMapper;
import com.creality.lcd.falcon.pojo.bo.IotBuriedDataBo;
import com.creality.lcd.falcon.pojo.bo.IotDeviceAlarmBo;
import com.creality.lcd.falcon.pojo.bo.IotDeviceProcessingDataBo;
import com.creality.lcd.falcon.pojo.dto.iot.*;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.app.FalconDeviceBind;
import com.creality.lcd.falcon.pojo.entity.app.FalconDeviceDetail;
import com.creality.lcd.falcon.pojo.entity.app.FalconDeviceDetailExtend;
import com.creality.lcd.falcon.pojo.entity.mongo.MongoIotDeviceAlarmRecord;
import com.creality.lcd.falcon.pojo.entity.web.FalconFirmVersion;
import com.creality.lcd.falcon.pojo.vo.app.AppCameraPhotographyVo;
import com.creality.lcd.falcon.pojo.vo.app.AppDeviceCalibrationVo;
import com.creality.lcd.falcon.pojo.vo.app.AppDevicePositionVo;
import com.creality.lcd.falcon.pojo.vo.app.AppSpaceFileVo;
import com.creality.lcd.falcon.pojo.vo.iot.*;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.util.BaseUtils;
import com.creality.lcd.falcon.util.MqttSdkClient;
import com.creality.lcd.falcon.util.OtaDownLoadUrlProcessUtil;
import com.creality.lcd.falcon.util.RotatingBloomFilter;
import com.creality.lcd.falcon.util.SessionManage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: tiantao
 * @Date: 2024/12/31 0:25
 * @Description: iot服务
 */
@Service
public class IotService {
    private static final Logger log = LogManager.getLogger(IotService.class);
    @Autowired
    private MqttSdkClient mqttSdkClient;

    @Autowired
    private FalconIotDeviceService falconIotDeviceService;

    @Autowired
    private SessionManage sessionManage;

    @Autowired
    private SpaceFilesService spaceFilesService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Value("${cache.expiration.deviceQrCode:10}")
    private Long deviceQrCodeVerification;

    @Autowired
    private FalconAccountMapper falconAccountMapper;

    @Autowired
    private IotDeviceTopicConfig topicConfig;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MongoIotDeviceAlarmRecordService mongoIotDeviceAlarmRecordService;

    @Value("${cache.expiration.deviceLastTsTTL:3600}")
    private Long deviceLastTsTTL;

    @Autowired
    private RotatingBloomFilter bloom;

    @Autowired
    private FalconFirmVersionMapper falconFirmVersionMapper;

    @Autowired
    private MongoIotDeviceOnlineLogService mongoIotDeviceOnlineLogService;
    
    @Autowired
    private  OtaDownLoadUrlProcessUtil otaDownLoadUrlProcessUtil;




    public void getOtaVersion(String model, Map<String, Object> messageMap) {
        // 获取固件版本信息
        List<FalconFirmVersion> firmVersionList = falconIotDeviceService.getFirmVersionList(model);
        IotFirmeareVersionVo vo = new IotFirmeareVersionVo();

        if (!CollectionUtils.isEmpty(firmVersionList)) {
        	//对下载连接进行加速处理
            FalconFirmVersion firmVersion = firmVersionList.get(0);
            //对固件下载连接进行进行url替换加速处理
            otaDownLoadUrlProcessUtil.processOtaDownloadUrl(firmVersion);
            BeanUtils.copyProperties(firmVersion, vo);
        }
        // 获取 JSON payload
        try {
            String sn = (String) messageMap.get("sn");
            String deviceTransNo = (String) messageMap.get("transNo");

            if (StringUtils.isEmpty(deviceTransNo)) {
                String transNo = BaseUtils.getUUID();
                vo.setTransNo(transNo);
            } else {
                vo.setTransNo(deviceTransNo);
            }
            String payload = convertObjectToJson(vo);

            String sendTopic = topicConfig.getOtaUpgradeResponseTopic(sn);
            mqttSdkClient.publishNoResponse(sendTopic, payload);
        } catch (Exception e) {
            log.warn("OTA升级发送异常，异常信息: {},{}", e, e.getStackTrace());
        }
    }

    private String convertObjectToJson(Object object) throws JsonProcessingException {
        try {
            return new ObjectMapper().writeValueAsString(object);
        } catch (Exception e) {
            log.warn("JSON转换失败，异常信息: {},{}", e, e.getStackTrace());
            throw e;
        }
    }

    /**
     * 设备上报工作状态
     */
    public void updateDeviceWorkStatus(String sn, Map<String, Object> messageMap) {
        if (CollectionUtils.isEmpty(messageMap)) {
            log.info("设备上报工作状态信息为空, model:{}", sn);
            return;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> payloadMap = (Map<String, Object>) messageMap.get("payload");
        if (CollectionUtils.isEmpty(payloadMap)) {
            log.info("设备上报工作状态payload为空, deviceSn: {}", sn);
            return;
        }
        String deviceTransNo = (String) messageMap.get("transNo");
        if (StringUtils.isBlank(deviceTransNo) || bloom.mightContain(deviceTransNo)) {
            log.info("设备上报工作状态信息不全或重入丢弃 transNo:{}, 上报信息:{}", deviceTransNo, messageMap);
            return;
        }
        String deviceSn = (String) payloadMap.get("sn");
        Integer workStatus = (Integer) payloadMap.get("status");
        if (StringUtils.isBlank(deviceSn) || workStatus == null) {
            log.info("设备上报工作状态参数为空，deviceSn: {},workStatus: {},,上报设备: {}", deviceSn,
                    workStatus, sn);
            return;
        }
        try (RedisLock redisLock = new RedisLock("device_lock:" + deviceSn, 0, 1000 * 10)) {
            if (redisLock.lock()) {
                bloom.add(deviceTransNo);
                Long ts = (Long) messageMap.get("timestamp");
                String redisKey = "device_last_ts:" + deviceSn;
                long lastTs = Optional.ofNullable(redisTemplate.opsForValue().get(redisKey))
                        .map(Long::valueOf).orElse(0L);
                if (ts <= lastTs) {
                    log.info("丢弃过期设备上报工作状态 deviceSn:{}, 时间戳ts:{}", deviceSn, ts);
                    return;
                }
                redisTemplate.opsForValue().set(redisKey, String.valueOf(ts), deviceLastTsTTL, TimeUnit.SECONDS);
                FalconDeviceDetail deviceDetailDto = new FalconDeviceDetail();
                deviceDetailDto.setDeviceSn(deviceSn);
                deviceDetailDto.setWorkStatus(workStatus);
                falconIotDeviceService.updateFalconDeviceDetail(deviceDetailDto);
            }
        } catch (Exception e) {
            log.warn("设备上报工作状态异常，异常信息: {},{}", e, e.getStackTrace());
        }
    }

    /**
     * 设备上报OTA升级状态
     */
    public void updateOtaStatus(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("设备上报OTA升级状态信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            String deviceTransNo = (String) messageMap.get("transNo");
            if (StringUtils.isBlank(deviceTransNo) || bloom.mightContain(deviceTransNo)) {
                log.info("设备上报OTA升级状态信息不全或重入丢弃 transNo:{}, 上报信息:{}", deviceTransNo, messageMap);
                return;
            }
            @SuppressWarnings("unchecked")
            Map<String, Object> payloadMap = (Map<String, Object>) messageMap.get("payload");
            if (CollectionUtils.isEmpty(payloadMap)) {
                log.info("设备上报OTA升级状态信息payload为空, deviceSn: {}", sn);
                return;
            }
            String deviceSn = (String) payloadMap.get("sn");
            Integer status = (Integer) payloadMap.get("status");
            String firmwareVersion = (String) payloadMap.get("firmwareVersion");
            if (StringUtils.isBlank(deviceSn) || status == null || StringUtils.isBlank(firmwareVersion)) {
                log.info("设备上报OTA升级状态信息参数为空，设备型号deviceSn:{},升级状态status:{},固件版本firmwareVersion：{}," +
                        "上报设备: {}", deviceSn, status, firmwareVersion, sn);
                return;
            }
            bloom.add(deviceTransNo);
            FalconDeviceDetail deviceDetailDto = new FalconDeviceDetail();
            deviceDetailDto.setDeviceSn(deviceSn);
            deviceDetailDto.setFirmwareVersion(firmwareVersion);
            //置为空闲中
            if (status == 600) {
                deviceDetailDto.setWorkStatus(600);
            } else {
                deviceDetailDto.setWorkStatus(2);
            }
            falconIotDeviceService.updateFalconDeviceDetail(deviceDetailDto);
        } catch (Exception e) {
            log.info("设备上报OTA升级状态处理异常, 上报信息:{},异常信息:{},{}",
                    messageMap, e, e.getStackTrace());
        }

    }

    /**
     * 设备上报数据
     */
    public void updateDeviceOnline(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备开机上报信息 model = {}, sn = {}, messageMap = {}", model, sn, messageMap);
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("设备开机上报信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            mongoIotDeviceOnlineLogService.saveDeviceOnlineLog(sn, model);
        } catch (Exception e) {
            log.error("mongoIotDeviceOnlineLogService.saveDeviceOnlineLog 异常 sn = {}", sn);
        }
        DeviceKindEnum deviceKindEnum = DeviceKindEnum.fromDeviceModel(model);
        if (deviceKindEnum == null) {
            log.warn("设备开机上报信息服务端未配置设备类型, deviceModel:{}", model);
            return;
        }
        ObjectMapper mapper = new ObjectMapper();
        IotDeviceDetailDto dto = mapper.convertValue(messageMap, IotDeviceDetailDto.class);
        if (dto == null) {
            log.info("设备开机上报信息为空, deviceModel:{} , 上报信息：{}", sn, messageMap);
            return;
        }
        String deviceSn = dto.getSn();
        String deviceTransNo = (String) messageMap.get("transNo");
        if (StringUtils.isBlank(deviceSn) || StringUtils.isBlank(deviceTransNo)) {
            log.info("设备开机上报信息缺失，设备deviceSn:{},上报设备: {},transNo: {}", deviceSn,
                    sn,deviceTransNo);
            return;
        }
        if (bloom.mightContain(deviceTransNo)) {
            log.info("设备开机上报重入丢弃 transNo:{}, 上报信息:{}", deviceTransNo, messageMap);
            return;
        }
        try (RedisLock redisLock = new RedisLock("device_lock:" + deviceSn, 0, 1000 * 10)) {
            if (redisLock.lock()) {
                bloom.add(deviceTransNo);
                Long ts = dto.getTimestamp();
                String redisKey = "device_last_ts:" + deviceSn;
                long lastTs = Optional.ofNullable(redisTemplate.opsForValue().get(redisKey))
                        .map(Long::valueOf).orElse(0L);
                if (ts <= lastTs) {
                    log.info("丢弃过期设备开机上报消息 deviceSn:{}, 时间戳ts:{}", deviceSn, ts);
                    return;
                }
                redisTemplate.opsForValue().set(redisKey, String.valueOf(ts), deviceLastTsTTL, TimeUnit.SECONDS);
                FalconDeviceDetail deviceDetailDto = new FalconDeviceDetail();
                Integer workStatus = (Integer) messageMap.get("workState");
                BeanUtils.copyProperties(dto, deviceDetailDto);
                deviceDetailDto.setDeviceSn(dto.getSn());
                deviceDetailDto.setDeviceModel(dto.getModel());
                deviceDetailDto.setWorkareaSize(dto.getWorkAreaSize());
                deviceDetailDto.setDeviceKind(deviceKindEnum.getDeviceKind());
                if (workStatus == null) {
                    workStatus = 2;
                }
                deviceDetailDto.setWorkStatus(workStatus);
                if (StringUtils.isBlank(deviceDetailDto.getDeviceModel()) || StringUtils.isBlank(deviceDetailDto.getDeviceSn())) {
                    log.info("设备开机上报信息缺失, deviceModel:{} , 上报deviceSn：{}", model, deviceDetailDto.getDeviceSn());
                    return;
                }

                FalconDeviceDetail deviceDetail = falconIotDeviceService.getDeviceDetail(deviceDetailDto.getDeviceSn());

                if (deviceDetail == null) {
                    deviceDetailDto.setActiveStatus(1);
                    deviceDetailDto.setActiveTime(LocalDateTime.now());
                    falconIotDeviceService.insertDeviceDetailInfo(deviceDetailDto);
                    return;
                } else {
                    if (dto.getApBindStatus() != null && (dto.getApBindStatus() == 0 || dto.getApBindStatus() == 1)) {
                        // 处理雕刻机的空气净化器绑定状态变更
                        handleLaserApBindStatusChange(deviceDetailDto.getDeviceSn(), dto.getApBindStatus(), dto.getApSn());
                    }
                    if (deviceDetail.getActiveStatus() == null || deviceDetail.getActiveStatus() != 1) {
                        deviceDetailDto.setActiveStatus(1);
                        deviceDetailDto.setActiveTime(LocalDateTime.now());
                    }
                    if (1 == deviceDetailDto.getStatus()) {
                        String userId = (String) messageMap.get("userId");
                        if (StringUtils.isBlank(userId)) {
                            log.info("设备开机上报信息, userId缺失, deviceModel:{} , 上报deviceSn：{}", model, deviceDetailDto.getDeviceSn());
                            return;
                        }
                        //查询当前用户是否与该设备绑定，因为设备离线时用户可能连接到了其他空闲的设备
                        FalconDeviceBind bindInfo = falconIotDeviceService.getBindInfo(userId, deviceDetailDto.getDeviceSn(), null);
                        if (bindInfo == null || 0 == bindInfo.getStatus()) {
                            //设备上线第一条消息status为1，且服务端当前用户有与该设备未连接，证明已与当前设备断开连接，需要通知设备端登出
                            String transNo = BaseUtils.getUUID();
                            IotDisConnectVo disConnectVo = new IotDisConnectVo();
                            disConnectVo.setUserId(userId);
                            disConnectVo.setTransNo(transNo);
                            String disConnectPayload = new ObjectMapper().writeValueAsString(disConnectVo);
                            String sendTopic = topicConfig.getDisconnectTopic(deviceDetailDto.getDeviceSn());
                            deviceDetailDto.setStatus((byte) 0);
                            falconIotDeviceService.updateFalconDeviceDetail(deviceDetailDto);
                            mqttSdkClient.publishNoResponse(sendTopic, disConnectPayload);
                        } else {
                            falconIotDeviceService.updateFalconDeviceDetail(deviceDetailDto);
                        }
                    } else {
                        falconIotDeviceService.updateFalconDeviceDetail(deviceDetailDto);
                    }
                }

            }
        } catch (Exception e) {
            log.error("上线处理异常 deviceSn:{},异常信息:{},{}", deviceSn, e, e.getStackTrace());
        }
    }

    /**
     * app扫码/蓝牙/设备切换连接
     */

    public BaseResponse<Object> bindUserAndDevice(IotDeviceConnectDto dto) {
        FalconAccount account = sessionManage.getUserInfo();
        if (Objects.isNull(account)) {
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        try {
            FalconDeviceDetail deviceDetail = falconIotDeviceService.getDeviceDetail(dto.getDeviceSn());
            if (deviceDetail == null) {
                //兼容现有app连接绑定、现app切换连接dto的DeviceModel没有传，后续版本加上会与下述判断合并
                if ((dto.getType() == 0 || dto.getType() == 2)) {
                    Byte deviceKind =DeviceKindEnum.getDeviceKindByDeviceModel(dto.getDeviceModel());
                    if (deviceKind == null) {
                        log.warn("app设备连接设备类型不存在, deviceModel:{}", dto.getDeviceModel());
                        return BaseResponse.error(ResultEnum.IOT_DEVICE_TYPE_NOT_EXIST);
                    } else if (deviceKind == 0) {
                        String transNo = BaseUtils.getUUID();
                        IotBaseVo vo = new IotBaseVo();
                        vo.setTransNo(transNo);
                        String payload = new ObjectMapper().writeValueAsString(vo);
                        String disConnectTopic = topicConfig.getReqDeviceIsExistTopic(dto.getDeviceSn());
                        mqttSdkClient.publishNoResponse(disConnectTopic, payload);
                    }
                }
                log.warn("app设备连接，设备信息不存在，deviceSN: {}", dto.getDeviceSn());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_INFO_NOT_EXIST);
            }
            DeviceKindEnum deviceKindEnum = DeviceKindEnum.fromDeviceModel(deviceDetail.getDeviceModel());
            if (deviceKindEnum == null) {
                log.warn("app设备连接设备类型不存在, deviceModel:{}", deviceDetail.getDeviceModel());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_TYPE_NOT_EXIST);
            }
            if (deviceDetail.getStatus() == 2) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_OFFLINE);
            }
            if (deviceKindEnum.getDeviceKind() == 0 && (deviceDetail.getStatus() == 1 || deviceDetail.getWorkStatus() != 2)) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_ALREADY_OCCUPIED);
            }
            if (dto.getType() == 0) {
                if (StringUtils.isEmpty(dto.getCheckCodeInfo())) {
                    return BaseResponse.error(ResultEnum.VALIDATE_ERROR);
                }
                String key = "device:qrCode:deviceSn:" + dto.getDeviceSn();
                String cacheValue = redisTemplate.opsForValue().get(key);
                if (StringUtils.isEmpty(cacheValue) || !cacheValue.equals(dto.getCheckCodeInfo())) {
                    return BaseResponse.error(ResultEnum.IOT_DEVICE_QR_EXPIRED);
                }
            }
            if (dto.getType() == 2) {
                if (deviceKindEnum.getDeviceKind() == 1) {
                    List<FalconDeviceBind> bindList = falconIotDeviceService.getBindList(deviceDetail.getDeviceSn(), (byte) 1);
                    if (CollectionUtils.isEmpty(bindList)) {
                        falconIotDeviceService.insertDeviceBindInfo(account.getUserId(), dto.getDeviceSn(), deviceDetail.getDeviceModel(),
                                deviceDetail.getDeviceKind(), (byte) 1);
                        deviceDetail.setStatus((byte) 1);
                        falconIotDeviceService.updateFalconDeviceDetail(deviceDetail);
                        return BaseResponse.success();
                    } else {
                        String userId = bindList.get(0).getUserId();
                        if (account.getUserId().equals(userId)) {
                            return BaseResponse.success();
                        } else {
                            return BaseResponse.error(ResultEnum.IOT_DEVICE_ALREADY_OCCUPIED);
                        }
                    }
                }
            }
            List<FalconDeviceBind> bindList = falconIotDeviceService.getDeviceByUserId(account.getUserId(), (byte) 0);
            Map<String, Byte> bindMap = new HashMap<>();
            bindMap = bindList.stream()
                    .collect(Collectors.toMap(
                            FalconDeviceBind::getDeviceSn,
                            FalconDeviceBind::getStatus
                    ));
            if (!bindMap.isEmpty() && bindMap.containsKey(dto.getDeviceSn())) {
                List<String> deviceSnList = bindMap.entrySet().stream()
                        .filter(entry -> entry.getValue() == 1)
                        .map(Map.Entry::getKey)
                        .toList();
                if (!CollectionUtils.isEmpty(deviceSnList)) {
                    //断开当前设备连接
                    String disConnectSn = deviceSnList.get(0);
                    FalconDeviceDetail detail = falconIotDeviceService.getDeviceDetail(null,disConnectSn);
                    if (detail != null) {
                        return BaseResponse.error(ResultEnum.IOT_DEVICE_SMOKE_PURIFIER_UNBIND);
                    }
                    FalconDeviceDetail disConnectDetail = falconIotDeviceService.getDeviceDetail(disConnectSn);
                    String transNo = BaseUtils.getUUID();
                    IotDisConnectVo disConnectVo = new IotDisConnectVo();
                    disConnectVo.setUserId(account.getUserId());
                    disConnectVo.setTransNo(transNo);
                    String payload = new ObjectMapper().writeValueAsString(disConnectVo);
                    String disConnectTopic = topicConfig.getDisconnectTopic(disConnectSn);
                    falconIotDeviceService.updateDeviceBindStatus(account.getUserId(), disConnectSn, (byte) 0);
                    disConnectDetail.setStatus((byte) 0);
                    falconIotDeviceService.updateFalconDeviceDetail(disConnectDetail);
                    mqttSdkClient.publishNoResponse(disConnectTopic, payload);
                }
            } else {
                falconIotDeviceService.insertDeviceBindInfo(account.getUserId(), dto.getDeviceSn(), deviceDetail.getDeviceModel(),
                        deviceDetail.getDeviceKind(), (byte) 0);
            }
            //建立连接
            IotDeviceConnectVo iotDeviceConnectVo = new IotDeviceConnectVo();
            BeanUtils.copyProperties(dto, iotDeviceConnectVo);
            BeanUtils.copyProperties(account, iotDeviceConnectVo);
            if (StringUtils.isEmpty(account.getDeviceAvatar())) {
                QueryWrapper<FalconAccount> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_id", account.getUserId());
                FalconAccount userInfo = falconAccountMapper.selectOne(queryWrapper);
                iotDeviceConnectVo.setDeviceAvatar(userInfo.getDeviceAvatar());
            }
            String connectTransNo = BaseUtils.getUUID();
            iotDeviceConnectVo.setTransNo(connectTransNo);
            String  connectPayload = new ObjectMapper().writeValueAsString(iotDeviceConnectVo);
            String connectSendTopic = topicConfig.getConnectTopic(deviceDetail.getDeviceSn());
            Map<String, Object> connectResult = mqttSdkClient.sendMessageWithResponseTopic(connectTransNo, connectSendTopic,
                    null, connectPayload, null, false);
            if (CollectionUtils.isEmpty(connectResult)) {
                log.info("app用户与设备建立连接返回数据为空, transNo: {}, userId: {}", connectTransNo, account.getUserId());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CONNECT_ERROR);
            }
            String code = (String) connectResult.get("code");
            if (StringUtils.isBlank(code) || "1".equals(code)) {
                log.info("app用户与设备建立连接失败, transNo: {}, userId :{}", connectTransNo, account.getUserId());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CONNECT_ERROR);
            }

            falconIotDeviceService.updateDeviceBindStatus(account.getUserId(), dto.getDeviceSn(), (byte) 1);
            deviceDetail.setStatus((byte) 1);
            falconIotDeviceService.updateFalconDeviceDetail(deviceDetail);
        } catch (Exception e) {
            log.error("Failed to disconnect user and device:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_DISCONNECT_ERROR);
        }

        return BaseResponse.success();
    }

    /**
     * app断开设备连接
     */
    public BaseResponse<Object> disConnect(IotDeviceDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException("The user is not logged in, please log in and try again");
            }
            FalconDeviceDetail detail = falconIotDeviceService.getDeviceDetail(null,dto.getDeviceSn());
            if (detail != null) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_SMOKE_PURIFIER_UNBIND);
            }
            String transNo = BaseUtils.getUUID();
            IotDisConnectVo disConnectVo = new IotDisConnectVo();
            disConnectVo.setUserId(account.getUserId());
            disConnectVo.setTransNo(transNo);
            String payload = "";
            try {
                payload = new ObjectMapper().writeValueAsString(disConnectVo);
            } catch (Exception e) {
                log.info("app断开连接操作转json失败, payload: {}, 异常信息：{},{}", disConnectVo, e, e.getStackTrace());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_JSON_CONVERSION_ERROR);
            }
            String sendTopic = topicConfig.getDisconnectTopic(dto.getDeviceSn());
            FalconDeviceDetail detailDto = new FalconDeviceDetail();
            detailDto.setDeviceSn(dto.getDeviceSn());
            detailDto.setStatus((byte) 0);
            falconIotDeviceService.updateDeviceBindStatus(account.getUserId(), dto.getDeviceSn(), (byte) 0);
            falconIotDeviceService.updateFalconDeviceDetail(detailDto);
            mqttSdkClient.publishNoResponse(sendTopic, payload);
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("app操作设备断开连接异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_DISCONNECT_ERROR);
        }
    }

    /**
     * 设备离线上报
     */
    public void offLine(Map<String, Object> messageMap) {
        if (CollectionUtils.isEmpty(messageMap)) {
            return;
        }
        String deviceSn = (String) messageMap.get("sn");
//        String deviceTransNo = (String) messageMap.get("transNo");
        if (StringUtils.isBlank(deviceSn)) {
            return;
        }
        String transNo = BaseUtils.getUUID();
        IotBaseVo vo = new IotBaseVo();
        vo.setTransNo(transNo);
        String payload = "";
        try {
            payload = new ObjectMapper().writeValueAsString(vo);
        } catch (Exception e) {
            log.info("设备离线上报转json失败, payload: {}, 异常信息：{},{}", vo, e, e.getStackTrace());
            return;
        }
        String sendTopic = topicConfig.getReqOnlineTopic(deviceSn);

        Map<String, Object> result = mqttSdkClient.sendMessageWithResponseTopic(transNo, sendTopic, null, payload,
                5000L, false);
        if (!CollectionUtils.isEmpty(result)) {
            return;
        }
        RLock lock = redissonClient.getLock("device_lock:" + deviceSn);
        try {
            if (!lock.tryLock(10, TimeUnit.SECONDS)) {
                return;
            }
            FalconDeviceDetail dto = new FalconDeviceDetail();
            dto.setDeviceSn(deviceSn);
            dto.setStatus((byte) 2);
            dto.setWorkStatus(2);
            falconIotDeviceService.updateFalconDeviceDetail(dto);
        } catch (Exception e) {
            log.error("离线处理异常 deviceSn:{},异常信息:{},{}", deviceSn, e, e.getStackTrace());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 设备上报IP信息变更
     */
    public void ipReport(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("设备上报IP信息，信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> payloadMap = (Map<String, Object>) messageMap.get("payload");
            if (CollectionUtils.isEmpty(payloadMap)) {
                log.info("设备上报IP信息变更payload为空, deviceSn: {}", sn);
                return;
            }
            String deviceSn = (String) payloadMap.get("sn");
            String wifiConfig = (String) payloadMap.get("wifiConfig");
            if (StringUtils.isBlank(deviceSn) || StringUtils.isBlank(wifiConfig)) {
                log.info("设备上报IP信息变更数据缺失，deviceSn: {}, wifiConfig: {},上报设备: {}", deviceSn, wifiConfig, sn);
                return;
            }
            FalconDeviceDetail dto = new FalconDeviceDetail();
            dto.setWifiConfig(wifiConfig);
            dto.setDeviceSn(deviceSn);
            falconIotDeviceService.updateFalconDeviceDetail(dto);
        } catch (Exception e) {
            log.info("设备上报IP信息变更异常, 异常信息:{},{}", e, e.getStackTrace());
        }
    }

    /**
     * 设备走边框/加工
     */
    public BaseResponse<Object> workType(IotDeviceWorkerDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            if (!("0".equals(dto.getWorkType()) || "1".equals(dto.getWorkType()))) {
                log.info("设备走边框或加工参数错误, 参数类型wrokType: {}", dto.getWorkType());
                return BaseResponse.error(ResultEnum.VALIDATE_ERROR);
            }
            String transNo = BaseUtils.getUUID();
            IotDeviceWorkerVo deviceWorkerVo = new IotDeviceWorkerVo();
            deviceWorkerVo.setUserId(account.getUserId());
            deviceWorkerVo.setTransNo(transNo);
            BeanUtils.copyProperties(dto, deviceWorkerVo);
            String payload = new ObjectMapper().writeValueAsString(deviceWorkerVo);
            String sendTopic = null;
            if ("0".equals(dto.getWorkType())) {
                sendTopic = topicConfig.getPreviewTopic(dto.getDeviceSn());
            } else {
                sendTopic = topicConfig.getStartWorkTopic(dto.getDeviceSn());
            }
            mqttSdkClient.publishNoResponse(sendTopic, payload);
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("走边框或加工异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_WORK_ERROR);
        }
    }

    /**
     * 设备控制（暂停、恢复、结束）
     */
    public BaseResponse<Object> deviceControl(IotDeviceControlDto dto) {
        try {
            if (dto == null || !("0".equals(dto.getOperateType()) || "1".equals(dto.getOperateType())
                    || "2".equals(dto.getOperateType()))) {
                log.info("设备控制暂停或开始参数错误, 参数信息: {}", dto);
                return BaseResponse.error(ResultEnum.VALIDATE_ERROR);
            }
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            String transNo = BaseUtils.getUUID();
            IotDeviceWorkerVo deviceWorkerVo = new IotDeviceWorkerVo();
            deviceWorkerVo.setUserId(account.getUserId());
            deviceWorkerVo.setTransNo(transNo);
            String payload = "";
            try {
                payload = new ObjectMapper().writeValueAsString(deviceWorkerVo);
            } catch (Exception e) {
                log.info("设备控制暂停或开始转json失败, payload: {}, 异常信息：{},{}", deviceWorkerVo, e, e.getStackTrace());
                return BaseResponse.error(ResultEnum.IOT_DEVICE_JSON_CONVERSION_ERROR);
            }
            String sendTopic = null;
            if ("0".equals(dto.getOperateType())) {
                sendTopic = topicConfig.getPauseWorkTopic(dto.getDeviceSn());
            } else if ("1".equals(dto.getOperateType())) {
                sendTopic = topicConfig.getRestartWorkTopic(dto.getDeviceSn());
            } else {
                sendTopic = topicConfig.getStopWorkTopic(dto.getDeviceSn());
            }
            Map<String, Object> result = mqttSdkClient.sendMessageWithResponseTopic(transNo, sendTopic, null, payload,
                    null, false);
            if (CollectionUtils.isEmpty(result)) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CONTROL_ERROR);
            }
            String code = (String) result.get("code");
            if (StringUtils.isNotBlank(code) && "0".equals(code)) {
                return BaseResponse.success();
            } else {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CONTROL_ERROR);
            }
        } catch (Exception e) {
            log.warn("设备控制暂停或开始异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_CONTROL_ERROR);
        }
    }

    /**
     * 设备上报文件传输状态
     */
    public void updateFileTransferStatus(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("设备上报文件传输状态信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        String deviceSn = (String) messageMap.get("sn");
        Integer status = (Integer) messageMap.get("status");
        String transNo = (String) messageMap.get("transNo");

        if (StringUtils.isBlank(deviceSn) || status == null) {
            log.info("设备上报文件传输状态信息为空，transNo: {},deviceModel: {},deviceSn: {},status: {}" +
                    ",上报设备: {}", transNo, model, deviceSn, status, sn);
            return;
        }
        if (bloom.mightContain(transNo)) {
            log.info("设备上报文件传输状态重入丢弃 transNo:{}, 上报信息:{}", transNo, messageMap);
            return;
        }
        try (RedisLock redisLock = new RedisLock("device_lock:" + deviceSn, 0, 1000 * 10)) {
            if (redisLock.lock()) {
                bloom.add(transNo);
                Long ts = (Long) messageMap.get("timestamp");
                String redisKey = "device_last_ts:" + deviceSn;
                long lastTs = Optional.ofNullable(redisTemplate.opsForValue().get(redisKey))
                        .map(Long::valueOf).orElse(0L);
                if (ts <= lastTs) {
                    log.info("丢弃过期文件传输状态 deviceSn:{}, 时间戳ts:{}", deviceSn, ts);
                    return;
                }
                redisTemplate.opsForValue().set(redisKey, String.valueOf(ts), deviceLastTsTTL, TimeUnit.SECONDS);
                FalconDeviceDetail dto = new FalconDeviceDetail();
                if (status == 603) {
                    dto.setWorkStatus(status);
                } else {
                    dto.setWorkStatus(2);
                }
                dto.setDeviceSn(deviceSn);
                falconIotDeviceService.updateFalconDeviceDetail(dto);
            }
        } catch (Exception e) {
            log.info("设备上报文件传输状态异常, 异常信息:{},{}", e, e.getStackTrace());
        }
    }

    /**
     * 交互屏登出
     */
    @Transactional
    public void deviceDisConnect(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("交互屏登出信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            String deviceSn = (String) messageMap.get("sn");
            String userId = (String) messageMap.get("userId");
            if (StringUtils.isBlank(deviceSn) || StringUtils.isBlank(userId)) {
                log.info("交互屏登出信息缺失,deviceSn: {},userId: {},上报设备: {}", deviceSn, userId, sn);
                return;
            }
            FalconDeviceDetail detail = falconIotDeviceService.getDeviceDetail(null,deviceSn);
            if (detail != null) {
                //1、与空气净化器解绑
                String transNo = BaseUtils.getUUID();
                IotApDeviceUnBindVo vo = new IotApDeviceUnBindVo();
                vo.setTransNo(transNo);
                vo.setLaserSn(deviceSn);
                String unBindPayload = new ObjectMapper().writeValueAsString(vo);
                String unBindSendTopic = topicConfig.getUnBindTopic(detail.getDeviceSn());
                mqttSdkClient.publishNoResponse(unBindSendTopic,unBindPayload);

                //2、风挡置为off
                String speedTransNo = BaseUtils.getUUID();
                IotApDeviceControlVo speedVo = new IotApDeviceControlVo();
                speedVo.setFanSpeed(0);
                speedVo.setAutoMode(0);
                speedVo.setTransNo(speedTransNo);
                String speedPayload = new ObjectMapper().writeValueAsString(speedVo);
                String sendSpeedTopic = topicConfig.getApSpeedControlTopic(detail.getDeviceSn());
                mqttSdkClient.publishNoResponse(sendSpeedTopic,speedPayload);

                //3、雕刻机与空气净化器解绑

                IotEmDeviceUnBindVo emUnBindVo = new IotEmDeviceUnBindVo();
                String emUnBindTransNo = BaseUtils.getUUID();
                emUnBindVo.setApSn(detail.getDeviceSn());
                emUnBindVo.setTransNo(emUnBindTransNo);
                String emUnBindPayload = new ObjectMapper().writeValueAsString(emUnBindVo);
                String sendTopic = topicConfig.getUnBindApTopic(deviceSn);
                mqttSdkClient.publishNoResponse(sendTopic, emUnBindPayload);
                FalconDeviceDetail dto = new FalconDeviceDetail();
                dto.setDeviceSn(detail.getDeviceSn());
                dto.setBindParentSn(null);
                falconIotDeviceService.updateUnBindDeviceDetail(dto);
            }
            FalconDeviceDetail detailDto = new FalconDeviceDetail();
            detailDto.setDeviceSn(deviceSn);
            detailDto.setStatus((byte) 0);
            falconIotDeviceService.updateDeviceBindStatus(userId, deviceSn, (byte) 0);
            falconIotDeviceService.updateFalconDeviceDetail(detailDto);
        } catch (Exception e) {
            log.warn("交互屏登出异常, 异常信息:{},{}", e, e.getStackTrace());
        }
    }

    /**
     * 获取云文件列表
     */
    public void getUserCloudFileList(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("获取云文件列表信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> payloadMap = (Map<String, Object>) messageMap.get("payload");
            if (CollectionUtils.isEmpty(payloadMap)) {
                log.info("设备获取云文件列表payload为空, deviceSn: {}", sn);
                return;
            }
            String deviceSn = (String) payloadMap.get("sn");
            String userId = (String) payloadMap.get("userId");
            String transNo = (String) messageMap.get("transNo");
            if (StringUtils.isBlank(deviceSn) || StringUtils.isBlank(userId)) {
                log.info("获取云文件列表信息缺失, transNo: {},deviceSn: {},userId: {},上报设备: {}", transNo, deviceSn, userId, sn);
                return;
            }

            List<AppSpaceFileVo> list = spaceFilesService.getSpaceDraftFiles(userId);
            IotDeviceCloudFileVo vo = new IotDeviceCloudFileVo();
            if (StringUtils.isEmpty(transNo)) {
                String fileTransNo = BaseUtils.getUUID();
                vo.setTransNo(fileTransNo);
            } else {
                vo.setTransNo(transNo);
            }
            vo.setPayload(list);
            String payload = convertObjectToJson(vo);
            String sendTopic = topicConfig.getCloudFileTopic(deviceSn);
            mqttSdkClient.publishNoResponse(sendTopic, payload);
        } catch (Exception e) {
            log.warn("获取云文件列表异常, 异常信息:{},{}", e, e.getStackTrace());
        }
    }

    /**
     * 上报二维码校验信息
     */
    public void putNewScanCode(String model, String sn, Map<String, Object> messageMap) {
        if (StringUtils.isBlank(model) || StringUtils.isBlank(sn) || CollectionUtils.isEmpty(messageMap)) {
            log.info("上报二维码校验信息为空, deviceModel:{}, deviceSn:{} , 上报信息：{}", model, sn, messageMap);
            return;
        }
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> payloadMap = (Map<String, Object>) messageMap.get("payload");
            if (CollectionUtils.isEmpty(payloadMap)) {
                log.info("设备上报二维码校验信息payload为空, deviceSn: {}", sn);
                return;
            }
            String deviceSn = (String) payloadMap.get("sn");
            String checkCodeInfo = (String) payloadMap.get("checkCodeInfo");
            if (StringUtils.isBlank(deviceSn) || StringUtils.isBlank(checkCodeInfo)) {
                log.info("设备上报二维码校验信息数据为空, deviceSn: {},checkCodeInfo: {},上报设备: {}", deviceSn, checkCodeInfo, sn);
                return;
            }
            String key = "device:qrCode:deviceSn:" + deviceSn;
            redisTemplate.opsForValue().set(key, checkCodeInfo, deviceQrCodeVerification, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设备上报二维码校验信息异常, 异常信息:{},{}", e, e.getStackTrace());
        }
    }

    /**
     * 获取坐标信息
     */
    public BaseResponse<AppDevicePositionVo> getPosition(String deviceSn) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            String transNo = BaseUtils.getUUID();
            IotDevicePositionVo positionVo = new IotDevicePositionVo();
            positionVo.setUserId(account.getUserId());
            positionVo.setTransNo(transNo);
            String payload = new ObjectMapper().writeValueAsString(positionVo);
            String sendTopic = topicConfig.getReqPositionTopic(deviceSn);
            Map<String, Object> result = mqttSdkClient.sendMessageWithResponseTopic(transNo, sendTopic, null, payload,
                    null, false);
            if (CollectionUtils.isEmpty(result)) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_GET_POSITION_ERROR);
            }
            String positionValue = (String) result.get("positionValue");
            AppDevicePositionVo vo = new AppDevicePositionVo();
            vo.setPositionValue(positionValue);
            return BaseResponse.success(vo);
        } catch (Exception e) {
            log.warn("获取坐标信息异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 相机拍照
     */
    public BaseResponse<AppCameraPhotographyVo> cameraPhotography(IotCameraPhotoDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            String transNo = BaseUtils.getUUID();
            IotCameraPhotographVo cameraPhotographVo = new IotCameraPhotographVo();
            cameraPhotographVo.setUserId(account.getUserId());
            cameraPhotographVo.setTransNo(transNo);
            cameraPhotographVo.setWidth(dto.getWidth());
            cameraPhotographVo.setHeight(dto.getHeight());
            String payload = new ObjectMapper().writeValueAsString(cameraPhotographVo);
            String sendTopic = topicConfig.getCapturePicTopic(dto.getDeviceSn());
            Map<String, Object> result = mqttSdkClient.sendMessageWithResponseTopic(transNo, sendTopic, null, payload,
                    null, false);
            if (CollectionUtils.isEmpty(result)) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CAMERA_PHOTOGRAPHY_ERROR);
            }
            String code = (String) result.get("code");
            if (StringUtils.isNotBlank(code) && "0".equals(code)) {
                String url = (String) result.get("url");
                AppCameraPhotographyVo vo = new AppCameraPhotographyVo();
                vo.setUrl(url);
                return BaseResponse.success(vo);
            } else {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_CAMERA_PHOTOGRAPHY_ERROR);
            }
        } catch (Exception e) {
            log.warn("相机拍照异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 设置标定参数
     */
    public BaseResponse<Object> setCalibrationValue(IotDeviceCalibrationDto dto) {
        try {
            String key = "device:calibrationValue:deviceSn:" + dto.getDeviceSn();
            redisTemplate.opsForValue().set(key, dto.getCalibrationValue());
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("设置标定参数异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 查询设备工作状态
     */
    public BaseResponse<Object> queryWorkStatus(String deviceSn) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            String transNo = BaseUtils.getUUID();
            IotDeviceWorkStatusVo workStatusVo = new IotDeviceWorkStatusVo();
            workStatusVo.setUserId(account.getUserId());
            workStatusVo.setTransNo(transNo);
            String payload = new ObjectMapper().writeValueAsString(workStatusVo);
            String sendTopic = topicConfig.getReqDeviceWorkStatusTopic(deviceSn);
            mqttSdkClient.publishNoResponse(sendTopic, payload);
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("查询设备工作状态异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 获取标定参数
     */
    public BaseResponse<AppDeviceCalibrationVo> getCalibrationValue(String deviceSn, String channel) {

        try {
            FalconAccount account = null;
            if ("APP".equals(channel)) {
                account = sessionManage.getUserInfo();
                if (Objects.isNull(account)) {
                    throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
                }
            }
            String key = "device:calibrationValue:deviceSn:" + deviceSn;
            String cacheValue = redisTemplate.opsForValue().get(key);
            AppDeviceCalibrationVo vo = new AppDeviceCalibrationVo();
            if (StringUtils.isEmpty(cacheValue)) {
                if ("PC".equals(channel)) {
                    return BaseResponse.success(vo);
                }
            } else {
                vo.setCalibrationValue(cacheValue);
                return BaseResponse.success(vo);
            }
            String transNo = BaseUtils.getUUID();
            IotDeviceCalibrationVo calibrationVo = new IotDeviceCalibrationVo();
            calibrationVo.setUserId(account.getUserId());
            calibrationVo.setTransNo(transNo);
            String payload = new ObjectMapper().writeValueAsString(calibrationVo);
            String sendTopic = topicConfig.getReqCalibrationValueTopic(deviceSn);
            Map<String, Object> result = mqttSdkClient.sendMessageWithResponseTopic(transNo, sendTopic, null, payload,
                    null, false);
            if (CollectionUtils.isEmpty(result)) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_GET_CALIBRATION_ERROR);
            }
            String calibrationValue = (String) result.get("calibrationValue");
            vo.setCalibrationValue(calibrationValue);
            return BaseResponse.success(vo);
        } catch (Exception e) {
            log.warn("获取标定参数异常，异常信息: {},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    public BaseResponse<Object> deviceInfoUpload(IotDeviceInfoUploadDto dto) {
        log.info("固件烧录上传 IotDeviceInfoUploadDto deviceDn = {}, dto = {}", dto.getDeviceDn(), dto);
        try (RedisLock redisLock = new RedisLock("device_lock:" + dto.getDeviceDn(), 0, 1000 * 10)) {
            if (redisLock.lock()) {
                falconIotDeviceService.deviceInfoUpload(dto);
            }
        } catch (Exception e) {
            log.error("固件烧录 deviceSn:{},deviceDn:{} 异常信息:{},{}", dto.getDeviceSn(), dto.getDeviceDn(), e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_CODE_EXIST);
        }
        return BaseResponse.success();
    }

    public IotDeviceInfoUploadCheckVo deviceInfoUploadCheck(IotDeviceInfoUploadDto dto) {
        return falconIotDeviceService.deviceInfoUploadCheck(dto);
    }

    /**
     * 上报设备告警
     *
     * @param model
     * @param sn
     * @param messageMap
     */
    public void putAlarm(String model, String sn, Map<String, Object> messageMap) {
        log.info("上报设备告警 model = {}, sn = {}, messageMap = {}", model, sn, messageMap);
        if (messageMap == null) {
            return;
        }
        IotDeviceAlarmBo bo = JSONObject.parseObject(JSONObject.toJSONString(messageMap), IotDeviceAlarmBo.class);
        if (bo.getPayload() != null) {
            MongoIotDeviceAlarmRecord record = new MongoIotDeviceAlarmRecord();
            record.setDeviceSn(bo.getPayload().getSn());
            record.setErrorCode(bo.getPayload().getErrorCode());
            record.setErrorLevel(bo.getPayload().getErrorLevel());
            record.setDesc(bo.getPayload().getDesc());
            mongoIotDeviceAlarmRecordService.saveRecord(record);
        }
    }


    public void putBuriedData(String model, String sn, Map<String, Object> messageMap) {
        log.info("上报埋点数据 model = {}, sn = {}, messageMap = {}", model, sn, messageMap);
        IotBuriedDataBo bo = JSONObject.parseObject(JSONObject.toJSONString(messageMap), IotBuriedDataBo.class);
        FalconDeviceDetailExtend deviceDetailExtend = BeanUtil.toBean(bo, FalconDeviceDetailExtend.class);
        deviceDetailExtend.setDeviceSn(bo.getSn());
        deviceDetailExtend.setPublicIp(bo.getPublicIP());
        falconIotDeviceService.saveOrUpdateDeviceDetailExtend(deviceDetailExtend);
    }

    /**
     * 设备出厂删除数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetToFactory(IotDeviceResetToFactoryDto dto) {
        if (dto == null || StringUtils.isEmpty(dto.getDeviceSn())) {
            log.info("设备出厂删除数据信息缺失, 设备信息: {}", dto);
            throw new ApiException(0, "deviceSn 不能为空");
        }
        falconIotDeviceService.deleteDeviceInfo(dto);
    }
    public BaseResponse<List<FalconFirmVersion>> queryOtaVersion(String model) {
        // 参数校验
        if (StringUtils.isBlank(model)) {
            return BaseResponse.error("deviceModel 不能为空");
        }

        // 构造查询：按 version 各段数值大小倒序并只取第一条
        QueryWrapper<FalconFirmVersion> qw = new QueryWrapper<>();
        qw.eq("device_model", model)
                .last("ORDER BY " +
                        "CAST(SUBSTRING_INDEX(version, '.', 1) AS UNSIGNED) DESC, " +
                        "CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(version, '.', 2), '.', -1) AS UNSIGNED) DESC, " +
                        "CAST(SUBSTRING_INDEX(version, '.', -1) AS UNSIGNED) DESC");

        // 执行查询
        List<FalconFirmVersion> latest = falconFirmVersionMapper.selectList(qw);

        // 如果没有数据，直接返回空
        if (CollectionUtils.isEmpty(latest)) {
            return BaseResponse.success(null);
        }
        //对下载连接进行加速处理
        otaDownLoadUrlProcessUtil.processOtaDownloadUrl(latest);
        // 复制属性到 VO 并返回
        return BaseResponse.success(latest);
    }
    /**
     * 自动对焦
     */
    public BaseResponse<Object> startFocus(IotDeviceDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            String transNo = BaseUtils.getUUID();
            IotBaseVo vo = new IotBaseVo();
            vo.setTransNo(transNo);
            String payload = new ObjectMapper().writeValueAsString(vo);

            String sendTopic = topicConfig.getReqStartFocusTopic(dto.getDeviceSn());
            mqttSdkClient.publishNoResponse(sendTopic, payload);
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("自动对焦异常, 异常信息:{},{}", e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_AUTO_FOCUS_ERROR);
        }
    }
    /**
     * 删除设备
     */
    public BaseResponse<Object> deleteDevice(IotDeviceDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            FalconDeviceBind bindInfo = falconIotDeviceService.getBindInfo(account.getUserId(), dto.getDeviceSn(), null);
            if (bindInfo == null) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_BIND_NOT_EXIST);
            } else if (1 == bindInfo.getStatus()) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_BIND_EXIST);
            } else {
                return falconIotDeviceService.deleteByUserIdAndDeviceSn(account.getUserId(), dto.getDeviceSn())
                        ? BaseResponse.success() : BaseResponse.error(ResultEnum.IOT_DEVICE_DELETION_ERROR);
            }
        } catch (Exception e) {
            log.warn("删除设备异常, 删除设备信息:{},异常信息:{},{}", dto, e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.IOT_DEVICE_DELETION_ERROR);
        }
    }
    /**
     * 设备重命名
     */
    public BaseResponse<Object> deviceRename(IotDeviceRenameDto dto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }
            boolean isExists= falconIotDeviceService.duplicateNameExists(account.getUserId(),dto.getDeviceSn(),dto.getCustomName());
            if (isExists) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_NAME_EXIST);
            }
            boolean isSuccess = falconIotDeviceService.updateCustomNameInBind(account.getUserId(),dto.getDeviceSn(),dto.getCustomName());
            if (!isSuccess) {
                return BaseResponse.error(ResultEnum.IOT_DEVICE_NAME_UPDATE_ERROR);
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.warn("设备重命名异常, 重命名信息:{},异常信息:{},{}", dto, e, e.getStackTrace());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    public void putJobDescData(String model, String sn, Map<String, Object> messageMap) {
        log.info("上报单次加工数据 model = {}, sn = {}, messageMap = {}", model, sn, messageMap);
        if (messageMap.isEmpty()) {
            return;
        }
        IotDeviceProcessingDataBo bo = JSONObject.parseObject(JSONObject.toJSONString(messageMap), IotDeviceProcessingDataBo.class);
        bo.setSn(sn);
        bo.setModel(model);
        falconIotDeviceService.saveProcessingData(bo);
    }

    /**
     * 处理雕刻机的空气净化器绑定状态变更
     * @param laserSn 雕刻机SN
     * @param apBindStatus 绑定状态 (0: 离线解绑, 1: 在线状态变更)
     * @param reportedApSn 雕刻机上报的空气净化器SN
     */
    private void handleLaserApBindStatusChange(String laserSn, Integer apBindStatus, String reportedApSn) {
        try {
            log.info("处理雕刻机的空气净化器绑定状态变更, laserSn: {}, apBindStatus: {}, reportedApSn: {}",
                    laserSn, apBindStatus, reportedApSn);

            switch (apBindStatus) {
                case 0: // 离线解绑
                    handleLaserOfflineUnbind(laserSn);
                    break;
                case 1: // 在线状态变更
                    handleLaserOnlineStatusChange(laserSn, reportedApSn);
                    break;
                default:
                    log.warn("未知的绑定状态: {}, laserSn: {}", apBindStatus, laserSn);
                    break;
            }
        } catch (Exception e) {
            log.error("处理雕刻机的空气净化器绑定状态变更异常, laserSn: {}, apBindStatus: {}, 异常信息: {}",
                     laserSn, apBindStatus, e.getMessage(), e);
        }
    }

    /**
     * 处理雕刻机离线解绑 (状态为0)
     * 雕刻机上报的deviceDetailDto.getDeviceSn作为净化器的父类bind_parent_sn有值，那么就清空该字段，并且给净化器发送解绑
     */
    private void handleLaserOfflineUnbind(String laserSn) {
        log.info("处理雕刻机离线解绑, laserSn: {}", laserSn);

        // 查询以该雕刻机SN作为bind_parent_sn的空气净化器
        FalconDeviceDetail apDevice = falconIotDeviceService.getDeviceDetail(null, laserSn);
        if (apDevice != null && StringUtils.isNotBlank(apDevice.getBindParentSn())) {
            String apSn = apDevice.getDeviceSn();

            // 清空bind_parent_sn
            FalconDeviceDetail updateDto = new FalconDeviceDetail();
            updateDto.setDeviceSn(apSn);
            updateDto.setBindParentSn(null);
            falconIotDeviceService.updateUnBindDeviceDetail(updateDto);

            // 发送解绑消息给净化器
            sendUnbindMessageToAp(apSn, laserSn);

            log.info("雕刻机离线解绑: 清空bind_parent_sn并发送解绑消息给净化器, laserSn: {}, apSn: {}", laserSn, apSn);
        } else {
            log.info("雕刻机离线解绑: 未找到绑定的空气净化器, laserSn: {}", laserSn);
        }
    }

    /**
     * 处理雕刻机在线状态变更 (状态为1)
     */
    private void handleLaserOnlineStatusChange(String laserSn, String reportedApSn) {
        log.info("处理雕刻机在线状态变更, laserSn: {}, reportedApSn: {}", laserSn, reportedApSn);

        // 查询以该雕刻机SN作为bind_parent_sn的空气净化器
        FalconDeviceDetail apDevice = falconIotDeviceService.getDeviceDetail(null, laserSn);

        if (apDevice == null || StringUtils.isBlank(apDevice.getBindParentSn())) {
            // 如果详情表的bind_parent_sn没有值，那么发送给雕刻机发送解绑
            sendUnbindMessageToLaser(laserSn, reportedApSn);
            log.info("雕刻机在线状态变更: 未找到绑定关系，发送解绑消息给雕刻机, laserSn: {}, reportedApSn: {}", laserSn, reportedApSn);
        } else {
            String currentApSn = apDevice.getDeviceSn();

            // 如果上报的apSn与查询出雕刻机deviceSn作为bind_parent_sn的数据，该条空气净化器的deviceSn不一致
            if (!currentApSn.equals(reportedApSn)) {
                // 将上报的apSn发送给雕刻机进行解绑
                sendUnbindMessageToLaser(laserSn, reportedApSn);

                // 将查询出的空气净化器的deviceSn加上bind_parent_sn数据一起发送进行解绑
                sendUnbindMessageToAp(currentApSn, laserSn);

                // 更新详情表，清空bind_parent_sn
                FalconDeviceDetail updateDto = new FalconDeviceDetail();
                updateDto.setDeviceSn(currentApSn);
                updateDto.setBindParentSn(null);
                falconIotDeviceService.updateUnBindDeviceDetail(updateDto);

                log.info("雕刻机在线状态变更: apSn不一致，发送解绑消息给雕刻机和净化器并清空bind_parent_sn, laserSn: {}, reportedApSn: {}, currentApSn: {}",
                        laserSn, reportedApSn, currentApSn);
            } else {
                log.info("雕刻机在线状态变更: apSn一致，无需解绑, laserSn: {}, apSn: {}", laserSn, currentApSn);
            }
        }
    }

    /**
     * 发送解绑消息给雕刻机
     * @param laserSn 雕刻机SN
     * @param apSn 空气净化器SN
     */
    private void sendUnbindMessageToLaser(String laserSn, String apSn) {
        try {
            IotEmDeviceUnBindVo emUnBindVo = new IotEmDeviceUnBindVo();
            String emUnBindTransNo = BaseUtils.getUUID();
            emUnBindVo.setApSn(apSn);
            emUnBindVo.setTransNo(emUnBindTransNo);

            String emUnBindPayload = new ObjectMapper().writeValueAsString(emUnBindVo);
            String sendTopic = topicConfig.getUnBindApTopic(laserSn);
            mqttSdkClient.publishNoResponse(sendTopic, emUnBindPayload);

            log.info("发送解绑消息给雕刻机成功, laserSn: {}, apSn: {}, transNo: {}", laserSn, apSn, emUnBindTransNo);
        } catch (Exception e) {
            log.error("发送解绑消息给雕刻机失败, laserSn: {}, apSn: {}, 异常信息: {}", laserSn, apSn, e.getMessage(), e);
        }
    }

    /**
     * 发送解绑消息给净化器
     * @param apSn 空气净化器SN
     * @param laserSn 雕刻机SN
     */
    private void sendUnbindMessageToAp(String apSn, String laserSn) {
        try {
            String transNo = BaseUtils.getUUID();
            IotApDeviceUnBindVo vo = new IotApDeviceUnBindVo();
            vo.setTransNo(transNo);
            vo.setLaserSn(laserSn);

            String unBindPayload = new ObjectMapper().writeValueAsString(vo);
            String unBindSendTopic = topicConfig.getUnBindTopic(apSn);
            mqttSdkClient.publishNoResponse(unBindSendTopic, unBindPayload);

            log.info("发送解绑消息给净化器成功, apSn: {}, laserSn: {}, transNo: {}", apSn, laserSn, transNo);
        } catch (Exception e) {
            log.error("发送解绑消息给净化器失败, apSn: {}, laserSn: {}, 异常信息: {}", apSn, laserSn, e.getMessage(), e);
        }
    }
}