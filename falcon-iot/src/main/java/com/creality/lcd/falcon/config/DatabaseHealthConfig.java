package com.creality.lcd.falcon.config;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 21:00
 * @Description: 数据库连接池健康监控配置
 */
@Configuration
@EnableScheduling
public class DatabaseHealthConfig implements HealthIndicator {

    private static final Logger log = LogManager.getLogger(DatabaseHealthConfig.class);

    @Autowired
    private DataSource dataSource;

    /**
     * 健康检查
     */
    @Override
    public Health health() {
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                
                return Health.up()
                    .withDetail("database", "MySQL")
                    .withDetail("activeConnections", poolMXBean.getActiveConnections())
                    .withDetail("idleConnections", poolMXBean.getIdleConnections())
                    .withDetail("totalConnections", poolMXBean.getTotalConnections())
                    .withDetail("threadsAwaitingConnection", poolMXBean.getThreadsAwaitingConnection())
                    .build();
            } else {
                return Health.up().withDetail("database", "MySQL").build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "MySQL")
                .withDetail("error", e.getMessage())
                .build();
        }
    }

    /**
     * 定期监控连接池状态
     */
    @Scheduled(fixedRate = 30000) // 每30秒检查一次
    public void monitorConnectionPool() {
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                
                int activeConnections = poolMXBean.getActiveConnections();
                int idleConnections = poolMXBean.getIdleConnections();
                int totalConnections = poolMXBean.getTotalConnections();
                int threadsAwaitingConnection = poolMXBean.getThreadsAwaitingConnection();
                
                log.info("连接池状态监控: 活跃连接={}, 空闲连接={}, 总连接={}, 等待连接的线程={}", 
                    activeConnections, idleConnections, totalConnections, threadsAwaitingConnection);
                
                // 警告阈值检查
                if (threadsAwaitingConnection > 0) {
                    log.warn("警告: 有{}个线程正在等待数据库连接", threadsAwaitingConnection);
                }
                
                if (activeConnections > totalConnections * 0.8) {
                    log.warn("警告: 活跃连接数过高 {}/{}", activeConnections, totalConnections);
                }
                
                if (totalConnections == hikariDataSource.getMaximumPoolSize()) {
                    log.warn("警告: 连接池已达到最大容量 {}", totalConnections);
                }
            }
        } catch (Exception e) {
            log.error("监控连接池状态失败", e);
        }
    }

    /**
     * 测试数据库连接
     */
    @Scheduled(fixedRate = 60000) // 每分钟测试一次
    public void testDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                log.debug("数据库连接测试成功");
            } else {
                log.warn("数据库连接测试失败: 连接无效");
            }
        } catch (SQLException e) {
            log.error("数据库连接测试失败", e);
        }
    }

    /**
     * 连接池统计信息
     */
    public void logConnectionPoolStats() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
            
            log.info("=== 连接池详细统计 ===");
            log.info("连接池名称: {}", hikariDataSource.getPoolName());
            log.info("最大连接数: {}", hikariDataSource.getMaximumPoolSize());
            log.info("最小空闲连接数: {}", hikariDataSource.getMinimumIdle());
            log.info("连接超时时间: {}ms", hikariDataSource.getConnectionTimeout());
            log.info("空闲超时时间: {}ms", hikariDataSource.getIdleTimeout());
            log.info("最大生命周期: {}ms", hikariDataSource.getMaxLifetime());
            log.info("当前活跃连接: {}", poolMXBean.getActiveConnections());
            log.info("当前空闲连接: {}", poolMXBean.getIdleConnections());
            log.info("当前总连接: {}", poolMXBean.getTotalConnections());
            log.info("等待连接的线程: {}", poolMXBean.getThreadsAwaitingConnection());
            log.info("========================");
        }
    }
}
