package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotApService;
import com.creality.lcd.falcon.service.IotService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 18:30
 * @Description: 统一的IoT消息处理器 - 极简版
 * 所有设备类型的消息都在这里处理，通过topic模式识别设备类型和消息类型
 */
@Component
public class UnifiedIotMessageHandler {

    private static final Logger log = LogManager.getLogger(UnifiedIotMessageHandler.class);

    @Value("${iot.topic.version}")
    private String version;

    @Autowired
    private IotService iotService;          // 雕刻机Service

    @Autowired
    private IotApService iotApService;      // 净化器Service

    // ==================== 设备类型识别 ====================

    /**
     * 判断是否为雕刻机消息
     */
    private boolean isLaserEngraver(String topic) {
        return topic.startsWith("device/") && topic.contains("Falcon") && !topic.contains("/ap/");
    }

    /**
     * 判断是否为空气净化器消息
     */
    private boolean isAirPurifier(String topic) {
        return topic.contains("/device/ap/");
    }

    // ==================== 统一消息处理入口 ====================

    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            log.info("处理IoT消息: topic={}", topic);

            // 提取基本信息
            String[] parts = topic.split("/");
            String model = extractModel(topic, parts);
            String sn = extractSn(topic, parts);

            // 根据设备类型分发
            if (isLaserEngraver(topic)) {
                handleLaserEngraverMessage(topic, model, sn, messageMap);
            } else if (isAirPurifier(topic)) {
                handleAirPurifierMessage(topic, model, sn, messageMap);
            } else {
                log.warn("未识别的设备类型: topic={}", topic);
            }

        } catch (Exception e) {
            log.error("处理IoT消息异常: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    // ==================== 雕刻机消息处理 ====================

    private void handleLaserEngraverMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        log.debug("处理雕刻机消息: model={}, sn={}, topic={}", model, sn, topic);

        // 使用简单的字符串匹配，避免复杂的正则表达式
        if (topic.contains("/OTA/reqVersion/")) {
            iotService.getOtaVersion(model, messageMap);
        } else if (topic.contains("/OTA/putOTAStatus/")) {
            iotService.updateOtaStatus(model, sn, messageMap);
        } else if (topic.contains("/info/putDeviceStatus/")) {
            iotService.updateDeviceWorkStatus(sn, messageMap);
        } else if (topic.contains("/info/online/")) {
            iotService.updateDeviceOnline(model, sn, messageMap);
        } else if (topic.contains("/info/offLine/")) {
            iotService.offLine(messageMap);
        } else if (topic.contains("/info/putNewIPConnect/")) {
            iotService.ipReport(model, sn, messageMap);
        } else if (topic.contains("/info/putFileTransferStatus/")) {
            iotService.updateFileTransferStatus(model, sn, messageMap);
        } else if (topic.contains("/info/disConnect/")) {
            iotService.deviceDisConnect(model, sn, messageMap);
        } else if (topic.contains("/info/reqCloudFileList/")) {
            iotService.getUserCloudFileList(model, sn, messageMap);
        } else if (topic.contains("/info/putNewScanCode/")) {
            iotService.putNewScanCode(model, sn, messageMap);
        } else if (topic.contains("/info/putAlarm/")) {
            iotService.putAlarm(model, sn, messageMap);
        } else if (topic.contains("/info/putBuriedData/")) {
            iotService.putBuriedData(model, sn, messageMap);
        } else if (topic.contains("/info/putJobDescData/")) {
            iotService.putJobDescData(model, sn, messageMap);
        } else {
            log.warn("未识别的雕刻机消息: topic={}", topic);
        }
    }

    // ==================== 空气净化器消息处理 ====================

    private void handleAirPurifierMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        log.debug("处理空气净化器消息: model={}, sn={}, topic={}", model, sn, topic);

        // 使用简单的字符串匹配
        if (topic.contains("/info/putApDeviceStatus")) {
            iotApService.updateDeviceWorkStatus(sn, messageMap);
        } else if (topic.contains("/info/online")) {
            iotApService.updateApDeviceOnline(sn, messageMap);
        } else if (topic.contains("/info/offLine")) {
            iotApService.apOffLine(messageMap);
        } else if (topic.contains("/info/putIPChange")) {
            iotApService.apIpReport(sn, messageMap);
        } else if (topic.contains("/info/putFilterLifeChange")) {
            iotApService.putFilterLifeChange(sn, messageMap);
        } else {
            log.warn("未识别的空气净化器消息: topic={}", topic);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 提取设备型号
     */
    private String extractModel(String topic, String[] parts) {
        if (isLaserEngraver(topic)) {
            // 雕刻机: device/{model}/{sn}/...
            return parts.length > 1 ? parts[1] : "";
        } else if (isAirPurifier(topic)) {
            // 空气净化器: 固定为 Creality Falcon AP
            return "Creality Falcon AP";
        }
        return "";
    }

    /**
     * 提取设备序列号
     */
    private String extractSn(String topic, String[] parts) {
        if (isLaserEngraver(topic)) {
            // 雕刻机: device/{model}/{sn}/...
            return parts.length > 2 ? parts[2] : "";
        } else if (isAirPurifier(topic)) {
            // 空气净化器: {version}/device/ap/{sn}/...
            return parts.length > 3 ? parts[3] : "";
        }
        return "";
    }

    // ==================== 设备型号特殊处理 ====================

    /**
     * 检查是否为A1 Pro
     */
    private boolean isA1Pro(String model) {
        return "Creality Falcon A1 Pro".equals(model);
    }

    /**
     * 检查是否为T1
     */
    private boolean isT1(String model) {
        return "Creality Falcon T1".equals(model);
    }

    /**
     * 如果将来A1 Pro和T1需要不同处理，可以在这里扩展
     */
    private void handleModelSpecificLogic(String model, String sn, Map<String, Object> messageMap, String action) {
        if (isA1Pro(model)) {
            // A1 Pro特有逻辑
            log.debug("A1 Pro特有处理: action={}, sn={}", action, sn);
        } else if (isT1(model)) {
            // T1特有逻辑
            log.debug("T1特有处理: action={}, sn={}", action, sn);
        }
    }
}
