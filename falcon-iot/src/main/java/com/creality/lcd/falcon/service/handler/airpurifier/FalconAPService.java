package com.creality.lcd.falcon.service.handler.airpurifier;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 16:50
 * @Description: Creality Falcon AP 专用服务接口
 */
public interface FalconAPService {

    /**
     * 更新空气净化器设备在线状态
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void updateApDeviceOnline(String sn, Map<String, Object> messageMap);

    /**
     * 处理空气净化器离线
     * @param messageMap 消息内容
     */
    void apOffLine(Map<String, Object> messageMap);

    /**
     * 处理空气净化器IP上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void apIpReport(String sn, Map<String, Object> messageMap);

    /**
     * 更新空气净化器工作状态
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void updateDeviceWorkStatus(String sn, Map<String, Object> messageMap);

    /**
     * 处理滤芯寿命变化上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void putFilterLifeChange(String sn, Map<String, Object> messageMap);

    /**
     * 处理空气质量数据上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void putAirQualityData(String sn, Map<String, Object> messageMap);

    /**
     * 处理风速调节上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void putFanSpeedAdjustment(String sn, Map<String, Object> messageMap);

    /**
     * 处理定时任务上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void putTimerTask(String sn, Map<String, Object> messageMap);

    /**
     * 处理维护提醒上报
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void putMaintenanceReminder(String sn, Map<String, Object> messageMap);
}
