package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.interceptor.SessionInterceptor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger log = LogManager.getLogger(WebConfig.class);

    @Autowired
    private SessionInterceptor sessionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义要排除的路径
        List<String> excludePaths = Arrays.asList(
                "/iot-api/test",
                "/iot-api/iot/set-calibration-value",
                "/iot-api/iot/get-calibration-value",
                "/iot-api/iot/device-info/upload",
                "/iot-api/iot/device-info/upload/check",
                "/iot-api/iot/reset-to-factory",
                "/iot-api/iot/query-ota-version"

        );
        registry.addInterceptor(sessionInterceptor)
                // 拦截所有请求
                .addPathPatterns("/**")
                .excludePathPatterns(excludePaths);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 对所有路径应用跨域策略
        registry.addMapping("/**")
                // 允许的源
                .allowedOrigins("*")
                // 允许的方法
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                // 允许的头部
                .allowedHeaders("*");
    }

}
