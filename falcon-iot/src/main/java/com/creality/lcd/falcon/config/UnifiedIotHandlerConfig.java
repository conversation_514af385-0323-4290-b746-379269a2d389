package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.service.handler.SimpleHandlerManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: 简单IoT处理器配置
 */
@Configuration
public class UnifiedIotHandlerConfig {

    private static final Logger log = LogManager.getLogger(UnifiedIotHandlerConfig.class);

    @Autowired
    private SimpleHandlerManager handlerManager;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        try {
            // 查找MqttPublishEvents bean
            Object mqttPublishEvents = applicationContext.getBean("mqttPublishEvents");

            // 使用反射设置处理器管理器
            mqttPublishEvents.getClass()
                .getMethod("setIotMessageHandlerManager", Object.class)
                .invoke(mqttPublishEvents, handlerManager);

            log.info("已设置简单IoT消息处理器管理器: SimpleHandlerManager");

        } catch (org.springframework.beans.factory.NoSuchBeanDefinitionException e) {
            log.warn("未找到MqttPublishEvents bean，处理器管理器未设置");
        } catch (Exception e) {
            log.error("设置IoT消息处理器管理器失败: {}", e.getMessage(), e);
        }
    }
}
