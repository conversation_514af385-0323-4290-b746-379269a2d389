package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.service.handler.UnifiedIotMessageHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 18:30
 * @Description: 统一IoT处理器配置
 */
@Configuration
public class UnifiedIotHandlerConfig {

    private static final Logger log = LogManager.getLogger(UnifiedIotHandlerConfig.class);

    @Autowired
    private UnifiedIotMessageHandler unifiedHandler;

    @Autowired(required = false)
    private Object mqttPublishEvents; // MqttPublishEvents在falcon-sdk中，避免直接依赖

    @PostConstruct
    public void init() {
        if (mqttPublishEvents != null) {
            try {
                // 使用反射设置统一处理器，避免循环依赖
                mqttPublishEvents.getClass()
                    .getMethod("setIotMessageHandlerManager", Object.class)
                    .invoke(mqttPublishEvents, unifiedHandler);
                
                log.info("已设置统一IoT消息处理器: UnifiedIotMessageHandler");
            } catch (Exception e) {
                log.error("设置统一IoT消息处理器失败: {}", e.getMessage(), e);
            }
        } else {
            log.warn("未找到MqttPublishEvents，统一处理器未设置");
        }
    }
}
