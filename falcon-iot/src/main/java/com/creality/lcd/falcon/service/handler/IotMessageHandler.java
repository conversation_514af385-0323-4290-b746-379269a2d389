package com.creality.lcd.falcon.service.handler;

import java.util.Map;

/**
 * @Author: tiant<PERSON>
 * @Date: 2025/7/11 12:00
 * @Description: IoT消息处理器接口
 */
public interface IotMessageHandler {

    /**
     * 判断是否支持处理该设备型号
     * @param deviceModel 设备型号
     * @return 是否支持
     */
    boolean supports(String deviceModel);

    /**
     * 处理IoT消息
     * @param topic MQTT主题
     * @param messageMap 消息内容
     */
    void handleMessage(String topic, Map<String, Object> messageMap);

    /**
     * 获取处理器优先级，数值越小优先级越高
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
