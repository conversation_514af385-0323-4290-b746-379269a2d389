package com.creality.lcd.falcon.controller.device;

import com.creality.lcd.falcon.pojo.dto.iot.*;
import com.creality.lcd.falcon.pojo.vo.iot.IotApDeviceInfoVo;
import com.creality.lcd.falcon.pojo.vo.iot.IotDeviceDetailVo;
import com.creality.lcd.falcon.pojo.vo.iot.IotDeviceVo;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.service.IotApService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 空气净化器设备相关服务
 * @Author: tiantao
 * @Date: 2025/4/14 9:48
 * @Description:
 */
@RestController
@RequestMapping("/ap")
public class IotApController {

    @Autowired
    private IotApService iotApService;
    /**
     * 风档控制
     */
    @PostMapping("/ap-control")
    public BaseResponse<Object> apDeviceControl(@RequestBody @Valid IotApDeviceControlDto dto) {
        return iotApService.apDeviceControl(dto);
    }
    /**
     * 获取当前设备详情
     */
    @GetMapping("/query-ap-info")
    public BaseResponse<IotDeviceDetailVo> getApDetail(
            @RequestParam String apSn) {
        IotDeviceDetailVo response = iotApService.getApDetail(apSn);
        return BaseResponse.success(response);
    }
    /**
     * 更换滤芯
     */
    @PostMapping("/reset-filter-life")
    public BaseResponse<Object> resetFilterLife(@RequestBody @Valid IotApDeviceDto dto) {
        return iotApService.resetFilterLife(dto);
    }
    /**
     * 固件升级
     */
    @PostMapping("/firmware-upgrade")
    public BaseResponse<Object> firmwareUpgrade(@RequestBody @Valid IotDeviceFirmwareUpgradeDto dto) {
        return iotApService.firmwareUpgrade(dto);
    }
    /**
     * 获取当前设备列表
     */
    @GetMapping("/get-em-list")
    public BaseResponse<List<IotDeviceVo>> getEmList() {
        List<IotDeviceVo> response = iotApService.getEmList();
        return BaseResponse.success(response);
    }
    /**
     * 空气净化器与雕刻机绑定
     */
    @PostMapping("/bind-em")
    public BaseResponse<Object> bindEm(@RequestBody @Valid IotApBindAndUnBindDto dto) {
        return iotApService.bindEm(dto);
    }
    /**
     * 空气净化器与雕刻机解绑
     */
    @PostMapping("/unbind-em")
    public BaseResponse<Object> unbindEm(@RequestBody @Valid IotApBindAndUnBindDto dto) {
        return iotApService.unbindEm(dto);
    }
    /**
     * 删除空气净化器
     */
    @PostMapping("/delete-ap")
    public BaseResponse<Object> deleteAp(@RequestBody @Valid IotApBindAndUnBindDto dto) {
        return iotApService.deleteAp(dto);
    }
    /**
     * 发送延时关闭时间
     */
    @PostMapping("/delayOff")
    public BaseResponse<Object> delayOff(@RequestBody @Valid IotApDelayOffDto dto) {
        return iotApService.delayOff(dto);
    }
    /**
     * 获取风挡数据
     */
    @GetMapping("/query-speed-info")
    public BaseResponse<Object> getApSpeedInfo(@RequestParam String apSn) {
        return iotApService.getApSpeedInfo(apSn);
    }

    /**
     * 获取净化器SN相关数据
     */
    @GetMapping("/query-apSn")
    public BaseResponse<IotApDeviceInfoVo> getApSnInfo(@RequestParam String deviceSn) {
        return iotApService.getApSnInfo(deviceSn);
    }

}

