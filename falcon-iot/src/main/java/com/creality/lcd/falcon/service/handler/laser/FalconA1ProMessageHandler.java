package com.creality.lcd.falcon.service.handler.laser;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 16:40
 * @Description: Creality Falcon A1 Pro 专用消息处理器
 */
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {

    private static final String SUPPORTED_MODEL = "Creality Falcon A1 Pro";

    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODEL.equals(deviceModel);
    }

    @Override
    public int getPriority() {
        return 11; // 比基类优先级稍高，确保精确匹配优先
    }

    @Override
    protected String getSupportedModel() {
        return SUPPORTED_MODEL;
    }

    // ==================== Falcon A1 Pro 特有功能 ====================

    /**
     * 处理A1 Pro特有的高级校准功能
     */
    private void handleAdvancedCalibration(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro高级校准: model={}, sn={}, data={}", model, sn, messageMap);
        // A1 Pro特有的高级校准逻辑
        // 可能需要调用特殊的service方法或者有不同的参数处理
    }

    /**
     * 处理A1 Pro特有的智能切割模式
     */
    private void handleSmartCuttingMode(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro智能切割模式: model={}, sn={}, data={}", model, sn, messageMap);
        // A1 Pro特有的智能切割逻辑
    }

    /**
     * 处理A1 Pro特有的材料识别功能
     */
    private void handleMaterialRecognition(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro材料识别: model={}, sn={}, data={}", model, sn, messageMap);
        // A1 Pro特有的材料识别逻辑
    }

    /**
     * 处理A1 Pro特有的安全防护功能
     */
    private void handleSafetyProtection(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro安全防护: model={}, sn={}, data={}", model, sn, messageMap);
        // A1 Pro特有的安全防护逻辑
    }

    // ==================== 重写基类方法以实现A1 Pro特有逻辑 ====================

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro状态上报: model={}, sn={}", model, sn);
        
        // A1 Pro可能有额外的状态字段需要处理
        if (messageMap.containsKey("advancedStatus")) {
            log.info("处理A1 Pro高级状态信息: {}", messageMap.get("advancedStatus"));
            // 处理A1 Pro特有的高级状态信息
        }
        
        // 调用基类的通用处理
        super.handleDeviceStatusReport(model, sn, messageMap);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro告警上报: model={}, sn={}", model, sn);
        
        // A1 Pro可能有特殊的告警类型
        Object alarmType = messageMap.get("alarmType");
        if ("MATERIAL_RECOGNITION_FAILED".equals(alarmType)) {
            log.warn("A1 Pro材料识别失败告警: sn={}", sn);
            // 处理材料识别失败的特殊逻辑
        } else if ("SAFETY_PROTECTION_TRIGGERED".equals(alarmType)) {
            log.warn("A1 Pro安全防护触发告警: sn={}", sn);
            // 处理安全防护触发的特殊逻辑
        }
        
        // 调用基类的通用处理
        super.handleAlarmReport(model, sn, messageMap);
    }

    // ==================== A1 Pro特有的Topic识别 ====================

    @Override
    protected boolean isModelSpecificTopic(String topic) {
        // A1 Pro特有的topic格式
        return isAdvancedCalibrationTopic(topic) ||
               isSmartCuttingModeTopic(topic) ||
               isMaterialRecognitionTopic(topic) ||
               isSafetyProtectionTopic(topic);
    }

    private boolean isAdvancedCalibrationTopic(String topic) {
        return topic.matches("^device/Creality Falcon A1 Pro/.+/info/putAdvancedCalibration/" + version + "$");
    }

    private boolean isSmartCuttingModeTopic(String topic) {
        return topic.matches("^device/Creality Falcon A1 Pro/.+/info/putSmartCuttingMode/" + version + "$");
    }

    private boolean isMaterialRecognitionTopic(String topic) {
        return topic.matches("^device/Creality Falcon A1 Pro/.+/info/putMaterialRecognition/" + version + "$");
    }

    private boolean isSafetyProtectionTopic(String topic) {
        return topic.matches("^device/Creality Falcon A1 Pro/.+/info/putSafetyProtection/" + version + "$");
    }

    @Override
    protected void handleModelSpecificMessage(String topic, Map<String, Object> messageMap) {
        String[] topicParts = topic.split("/");
        String model = extractModel(topicParts);
        String sn = extractSn(topicParts);

        if (isAdvancedCalibrationTopic(topic)) {
            handleAdvancedCalibration(model, sn, messageMap);
        } else if (isSmartCuttingModeTopic(topic)) {
            handleSmartCuttingMode(model, sn, messageMap);
        } else if (isMaterialRecognitionTopic(topic)) {
            handleMaterialRecognition(model, sn, messageMap);
        } else if (isSafetyProtectionTopic(topic)) {
            handleSafetyProtection(model, sn, messageMap);
        } else {
            log.warn("Falcon A1 Pro收到未识别的特殊消息: topic={}", topic);
        }
    }

    // ==================== A1 Pro特有的参数处理 ====================

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro文件传输状态上报: model={}, sn={}", model, sn);
        
        // A1 Pro可能支持更大的文件或特殊的文件格式
        Object fileSize = messageMap.get("fileSize");
        if (fileSize != null && ((Number) fileSize).longValue() > 100 * 1024 * 1024) {
            log.info("A1 Pro处理大文件传输: size={}MB", ((Number) fileSize).longValue() / 1024 / 1024);
            // 大文件传输的特殊处理逻辑
        }
        
        // 调用基类的通用处理
        super.handleFileTransferStatusReport(model, sn, messageMap);
    }

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro加工数据上报: model={}, sn={}", model, sn);
        
        // A1 Pro可能有更详细的加工数据
        if (messageMap.containsKey("materialType")) {
            log.info("A1 Pro材料类型: {}", messageMap.get("materialType"));
        }
        if (messageMap.containsKey("cuttingQuality")) {
            log.info("A1 Pro切割质量: {}", messageMap.get("cuttingQuality"));
        }
        
        // 调用基类的通用处理
        super.handleJobDescDataReport(model, sn, messageMap);
    }
}
