package com.creality.lcd.falcon.service.handler.laser;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 16:40
 * @Description: Creality Falcon A1 Pro 专用消息处理器
 * 注意：大部分功能使用FalconSeriesMessageHandler的通用逻辑
 * 只有A1 Pro特有的差异化功能才在这里处理
 */
@Component
public class FalconA1ProMessageHandler extends AbstractLaserEngraverHandler {

    private static final String SUPPORTED_MODEL = "Creality Falcon A1 Pro";

    @Override
    public boolean supports(String deviceModel) {
        // 只有在需要A1 Pro特有处理逻辑时才返回true
        // 目前暂时返回false，让FalconSeriesMessageHandler处理
        return false; // 暂时禁用，使用通用处理器
    }

    // 移除优先级设置，使用默认优先级

    @Override
    protected String getSupportedModel() {
        return SUPPORTED_MODEL;
    }

    // ==================== Falcon A1 Pro 特有功能 ====================
    // 注意：只有当A1 Pro需要与T1不同的处理逻辑时，才在这里实现
    // 目前A1 Pro和T1使用相同的逻辑，所以这些方法暂时保留作为示例

    // ==================== A1 Pro特有的参数处理 ====================

    

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon A1 Pro加工数据上报: model={}, sn={}", model, sn);
        
        // A1 Pro可能有更详细的加工数据
        if (messageMap.containsKey("materialType")) {
            log.info("A1 Pro材料类型: {}", messageMap.get("materialType"));
        }
        if (messageMap.containsKey("cuttingQuality")) {
            log.info("A1 Pro切割质量: {}", messageMap.get("cuttingQuality"));
        }
        
        // 调用基类的通用处理
        super.handleJobDescDataReport(model, sn, messageMap);
    }
}
