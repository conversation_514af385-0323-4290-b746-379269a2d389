package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.handler.message.MessageProcessor;
import com.creality.lcd.falcon.service.handler.message.MessageType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;

import jakarta.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: IoT消息处理器抽象基类
 */
public abstract class AbstractIotMessageHandler implements IotMessageHandler {

    protected static final Logger log = LogManager.getLogger(AbstractIotMessageHandler.class);

    @Value("${iot.topic.version}")
    protected String version;

    // 消息处理器映射表
    private final Map<MessageType, MessageProcessor> messageProcessors = new EnumMap<>(MessageType.class);

    @PostConstruct
    protected void initMessageProcessors() {
        // 注册消息处理器
        messageProcessors.put(MessageType.OTA_REQUEST, this::handleOtaRequest);
        messageProcessors.put(MessageType.OTA_STATUS_REPORT, this::handleOtaStatusReport);
        messageProcessors.put(MessageType.DEVICE_STATUS_REPORT, this::handleDeviceStatusReport);
        messageProcessors.put(MessageType.DEVICE_ONLINE_REPORT, this::handleDeviceOnlineReport);
        messageProcessors.put(MessageType.DEVICE_OFFLINE_REPORT, this::handleDeviceOfflineReport);
        messageProcessors.put(MessageType.IP_REPORT, this::handleIpReport);
        messageProcessors.put(MessageType.FILE_TRANSFER_STATUS_REPORT, this::handleFileTransferStatusReport);
        messageProcessors.put(MessageType.CLOUD_FILE_LIST_REQUEST, this::handleCloudFileListRequest);
        messageProcessors.put(MessageType.DEVICE_DISCONNECT, this::handleDeviceDisconnect);
        messageProcessors.put(MessageType.SCAN_CODE_REPORT, this::handleScanCodeReport);
        messageProcessors.put(MessageType.ALARM_REPORT, this::handleAlarmReport);
        messageProcessors.put(MessageType.BURIED_DATA_REPORT, this::handleBuriedDataReport);
        messageProcessors.put(MessageType.JOB_DESC_DATA_REPORT, this::handleJobDescDataReport);
        messageProcessors.put(MessageType.FILTER_LIFE_CHANGE, this::handleFilterLifeChange);

        // 子类可以重写此方法添加自定义处理器
        registerCustomProcessors();
    }

    @Override
    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            log.info("处理IoT消息: topic={}, handler={}", topic, this.getClass().getSimpleName());

            // 解析topic获取基本信息
            String[] topicParts = topic.split("/");
            String model = extractModel(topicParts);
            String sn = extractSn(topicParts);

            // 根据topic类型查找对应的处理器
            MessageType messageType = MessageType.fromTopic(topic, version);
            MessageProcessor processor = messageProcessors.get(messageType);

            if (processor != null) {
                log.debug("使用处理器处理消息: type={}, topic={}", messageType.getDescription(), topic);
                processor.process(model, sn, messageMap);
            } else {
                log.debug("使用自定义消息处理: type={}, topic={}", messageType.getDescription(), topic);
                handleCustomMessage(topic, messageMap);
            }
        } catch (Exception e) {
            log.error("处理IoT消息异常: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    // ==================== 抽象方法，子类必须实现 ====================

    /**
     * 从topic中提取设备型号
     */
    protected abstract String extractModel(String[] topicParts);

    /**
     * 从topic中提取设备序列号
     */
    protected abstract String extractSn(String[] topicParts);

    /**
     * 子类可以重写此方法注册自定义消息处理器
     */
    protected void registerCustomProcessors() {
        // 默认实现为空，子类可以重写
    }

    // ==================== 通用处理方法，子类可以重写 ====================

    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理OTA请求: model={}, sn={}", model, sn);
    }

    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备状态上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备上线上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备离线上报: model={}, sn={}", model, sn);
    }

    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理IP上报: model={}, sn={}", model, sn);
    }

    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理OTA状态上报: model={}, sn={}", model, sn);
    }

    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理文件传输状态上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备断开连接: model={}, sn={}", model, sn);
    }

    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理云文件列表请求: model={}, sn={}", model, sn);
    }

    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理扫码上报: model={}, sn={}", model, sn);
    }

    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理告警上报: model={}, sn={}", model, sn);
    }

    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理埋点数据上报: model={}, sn={}", model, sn);
    }

    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理加工数据上报: model={}, sn={}", model, sn);
    }

    protected void handleFilterLifeChange(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理滤芯寿命变化: model={}, sn={}", model, sn);
    }

    protected void handleCustomMessage(String topic, Map<String, Object> messageMap) {
        log.warn("未识别的消息类型: topic={}", topic);
    }
}
