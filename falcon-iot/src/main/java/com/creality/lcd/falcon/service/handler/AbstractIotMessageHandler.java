package com.creality.lcd.falcon.service.handler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: IoT消息处理器抽象基类
 */
public abstract class AbstractIotMessageHandler implements IotMessageHandler {

    protected static final Logger log = LogManager.getLogger(AbstractIotMessageHandler.class);

    @Value("${iot.topic.version}")
    protected String version;

    @Override
    public void handleMessage(String topic, Map<String, Object> messageMap) {
        try {
            log.info("处理IoT消息: topic={}, handler={}", topic, this.getClass().getSimpleName());
            
            // 解析topic获取基本信息
            String[] topicParts = topic.split("/");
            String model = extractModel(topicParts);
            String sn = extractSn(topicParts);
            
            // 根据topic类型分发到具体的处理方法
            if (isOtaRequest(topic)) {
                handleOtaRequest(model, sn, messageMap);
            } else if (isDeviceStatusReport(topic)) {
                handleDeviceStatusReport(model, sn, messageMap);
            } else if (isDeviceOnlineReport(topic)) {
                handleDeviceOnlineReport(model, sn, messageMap);
            } else if (isDeviceOfflineReport(topic)) {
                handleDeviceOfflineReport(model, sn, messageMap);
            } else if (isIpReport(topic)) {
                handleIpReport(model, sn, messageMap);
            } else if (isOtaStatusReport(topic)) {
                handleOtaStatusReport(model, sn, messageMap);
            } else if (isFileTransferStatusReport(topic)) {
                handleFileTransferStatusReport(model, sn, messageMap);
            } else if (isDeviceDisconnect(topic)) {
                handleDeviceDisconnect(model, sn, messageMap);
            } else if (isCloudFileListRequest(topic)) {
                handleCloudFileListRequest(model, sn, messageMap);
            } else if (isScanCodeReport(topic)) {
                handleScanCodeReport(model, sn, messageMap);
            } else if (isAlarmReport(topic)) {
                handleAlarmReport(model, sn, messageMap);
            } else if (isBuriedDataReport(topic)) {
                handleBuriedDataReport(model, sn, messageMap);
            } else if (isJobDescDataReport(topic)) {
                handleJobDescDataReport(model, sn, messageMap);
            } else {
                handleCustomMessage(topic, messageMap);
            }
        } catch (Exception e) {
            log.error("处理IoT消息异常: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    // ==================== 抽象方法，子类必须实现 ====================

    /**
     * 从topic中提取设备型号
     */
    protected abstract String extractModel(String[] topicParts);

    /**
     * 从topic中提取设备序列号
     */
    protected abstract String extractSn(String[] topicParts);

    // ==================== 通用处理方法，子类可以重写 ====================

    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理OTA请求: model={}, sn={}", model, sn);
    }

    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备状态上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备上线上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备离线上报: model={}, sn={}", model, sn);
    }

    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理IP上报: model={}, sn={}", model, sn);
    }

    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理OTA状态上报: model={}, sn={}", model, sn);
    }

    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理文件传输状态上报: model={}, sn={}", model, sn);
    }

    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理设备断开连接: model={}, sn={}", model, sn);
    }

    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理云文件列表请求: model={}, sn={}", model, sn);
    }

    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理扫码上报: model={}, sn={}", model, sn);
    }

    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理告警上报: model={}, sn={}", model, sn);
    }

    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理埋点数据上报: model={}, sn={}", model, sn);
    }

    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理加工数据上报: model={}, sn={}", model, sn);
    }

    protected void handleCustomMessage(String topic, Map<String, Object> messageMap) {
        log.warn("未识别的消息类型: topic={}", topic);
    }

    // ==================== Topic类型判断方法 ====================

    protected boolean isOtaRequest(String topic) {
        return topic.contains("/OTA/reqVersion/");
    }

    protected boolean isDeviceStatusReport(String topic) {
        return topic.contains("/info/putDeviceStatus/") || topic.contains("/info/putApDeviceStatus");
    }

    protected boolean isDeviceOnlineReport(String topic) {
        return topic.contains("/info/online");
    }

    protected boolean isDeviceOfflineReport(String topic) {
        return topic.contains("/info/offLine");
    }

    protected boolean isIpReport(String topic) {
        return topic.contains("/info/putNewIPConnect/") || topic.contains("/info/putIPChange");
    }

    protected boolean isOtaStatusReport(String topic) {
        return topic.contains("/OTA/putOTAStatus/");
    }

    protected boolean isFileTransferStatusReport(String topic) {
        return topic.contains("/info/putFileTransferStatus/");
    }

    protected boolean isDeviceDisconnect(String topic) {
        return topic.contains("/info/disConnect/");
    }

    protected boolean isCloudFileListRequest(String topic) {
        return topic.contains("/info/reqCloudFileList/");
    }

    protected boolean isScanCodeReport(String topic) {
        return topic.contains("/info/putNewScanCode/");
    }

    protected boolean isAlarmReport(String topic) {
        return topic.contains("/info/putAlarm/");
    }

    protected boolean isBuriedDataReport(String topic) {
        return topic.contains("/info/putBuriedData/");
    }

    protected boolean isJobDescDataReport(String topic) {
        return topic.contains("/info/putJobDescData/");
    }
}
