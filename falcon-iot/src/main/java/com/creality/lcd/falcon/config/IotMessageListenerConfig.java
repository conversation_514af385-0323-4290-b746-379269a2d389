package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.service.handler.IotMessageHandlerManager;
import jakarta.annotation.PostConstruct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/2/12 17:36
 * @Description: MQTT消息监听配置
 */
@Configuration
public class IotMessageListenerConfig {
    private static final Logger log = LogManager.getLogger(IotMessageListenerConfig.class);

    @Autowired
    private MqttPublishEvents mqttPublishEvents;

    @Autowired
    private IotMessageHandlerManager messageHandlerManager;
    @PostConstruct
    public void init() {
        log.info("初始化MQTT消息监听器，使用策略模式处理不同设备型号的消息");

        // 直接将消息处理器管理器设置到MqttPublishEvents中
        // 这样所有MQTT消息都会优先通过新的处理器管理器处理
        mqttPublishEvents.setIotMessageHandlerManager(messageHandlerManager);

        log.info("已将IoT消息处理器管理器注册到MQTT事件处理器中");
    }
}
