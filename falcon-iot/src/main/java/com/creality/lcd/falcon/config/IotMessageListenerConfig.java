package com.creality.lcd.falcon.config;

import com.creality.lcd.falcon.service.handler.IotMessageHandlerManager;
import jakarta.annotation.PostConstruct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/2/12 17:36
 * @Description: MQTT消息监听配置
 */
@Configuration
public class IotMessageListenerConfig {
    private static final Logger log = LogManager.getLogger(IotMessageListenerConfig.class);

    @Autowired
    private MqttPublishEvents mqttPublishEvents;

    @Autowired
    private IotMessageHandlerManager messageHandlerManager;
    @PostConstruct
    public void init() {
        // TODO tiantao 后期优化抽到单独的biz进行
        mqttPublishEvents.addListener(new MqttMessageListener() {
            @Override
            public void onMessage(String topic, Map<String, Object> messageMap) {
                //1、雕刻机
                // 1.1 设备请求OTA
                if (topic.matches("^device/.+/.+/OTA/reqVersion/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    iotService.getOtaVersion(model, messageMap);
                }
                // 1.2 设备上报工作 status
                if (topic.matches("^device/.+/.+/info/putDeviceStatus/"+ version +"$")) {
                    String sn = topic.split("/")[2];
                    iotService.updateDeviceWorkStatus(sn, messageMap);
                }
                // 1.3 设备上报信息
                if (topic.matches("^device/.+/.+/info/online/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.updateDeviceOnline(model, sn, messageMap);
                }
                // 1.4 设备离线上报状态
                if (topic.matches("^device/.+/.+/info/offLine/"+ version +"$")) {
                    iotService.offLine(messageMap);
                }
                // 1.5 设备上报ip
                if (topic.matches("^device/.+/.+/info/putNewIPConnect/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.ipReport(model,sn,messageMap);
                }
                // 1.6 设备上报OTA状态
                if (topic.matches("^device/.+/.+/OTA/putOTAStatus/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.updateOtaStatus(model,sn,messageMap);
                }
                // 1.7 文件传输状态
                if (topic.matches("^device/.+/.+/info/putFileTransferStatus/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.updateFileTransferStatus(model,sn,messageMap);
                }
                // 1.8 交互屏登出
                if (topic.matches("^device/.+/.+/info/disConnect/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.deviceDisConnect(model,sn,messageMap);
                }
                // 1.9 获取云文件列表
                if (topic.matches("^device/.+/.+/info/reqCloudFileList/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.getUserCloudFileList(model,sn,messageMap);
                }
                // 1.10. 上报二维码校验信息
                if (topic.matches("^device/.+/.+/info/putNewScanCode/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.putNewScanCode(model,sn,messageMap);
                }
                // 1.11. 上报设备告警
                if (topic.matches("^device/.+/.+/info/putAlarm/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.putAlarm(model,sn,messageMap);
                }
                // 1.12. 上报埋点数据统计
                if (topic.matches("^device/.+/.+/info/putBuriedData/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.putBuriedData(model,sn,messageMap);
                }
                // 1.13. 上报单次加工数据
                if (topic.matches("^device/.+/.+/info/putJobDescData/"+ version +"$")) {
                    String model = topic.split("/")[1];
                    String sn = topic.split("/")[2];
                    iotService.putJobDescData(model,sn,messageMap);
                }

                //2、空气净化器
                // 2.1 设备上报信息
                if (topic.matches("^"+version+"/device/ap/.+/info/online"+"$")) {
                    String apSn = topic.split("/")[3];
                    iotApService.updateApDeviceOnline(apSn,messageMap);
                }
                // 2.2 设备离线上报状态
                if (topic.matches("^"+version+"/device/ap/.+/info/offLine"+"$")) {
                    iotApService.apOffLine(messageMap);
                }
                // 2.3 设备上报ip
                if (topic.matches("^"+version+"/device/ap/.+/info/putIPChange"+"$")) {
                    String apSn = topic.split("/")[3];
                    iotApService.apIpReport(apSn,messageMap);
                }
                // 2.4 设备上报滤芯
                if (topic.matches("^"+version+"/device/ap/.+/info/putFilterLifeChange"+"$")) {
                    String apSn = topic.split("/")[3];
                    iotApService.putFilterLifeChange(apSn,messageMap);
                }
                // 2.5 设备上报工作状态
                if (topic.matches("^"+version+"/device/ap/.+/info/putApDeviceStatus"+"$")) {
                    String apSn = topic.split("/")[3];
                    iotApService.updateDeviceWorkStatus(apSn,messageMap);
                }

            }
        });
    }
}
