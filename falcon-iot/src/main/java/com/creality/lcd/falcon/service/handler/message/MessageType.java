package com.creality.lcd.falcon.service.handler.message;

import java.util.regex.Pattern;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 16:30
 * @Description: IoT消息类型枚举
 */
public enum MessageType {

    // OTA相关 - 雕刻机
    OTA_REQUEST("OTA请求", Pattern.compile("^device/.+/.+/OTA/reqVersion/.+$")),
    OTA_STATUS_REPORT("OTA状态上报", Pattern.compile("^device/.+/.+/OTA/putOTAStatus/.+$")),

    // 设备状态相关
    DEVICE_STATUS_REPORT("设备状态上报",
        Pattern.compile("^device/.+/.+/info/putDeviceStatus/.+$|^.+/device/ap/.+/info/putApDeviceStatus$")),

    // 设备上线
    DEVICE_ONLINE_REPORT("设备上线上报",
        Pattern.compile("^device/.+/.+/info/online/.+$|^.+/device/ap/.+/info/online$")),

    // 设备离线
    DEVICE_OFFLINE_REPORT("设备离线上报",
        Pattern.compile("^device/.+/.+/info/offLine/.+$|^.+/device/ap/.+/info/offLine$")),

    // 网络相关
    IP_REPORT("IP上报",
        Pattern.compile("^device/.+/.+/info/putNewIPConnect/.+$|^.+/device/ap/.+/info/putIPChange$")),

    // 文件传输相关 - 雕刻机专用
    FILE_TRANSFER_STATUS_REPORT("文件传输状态上报",
        Pattern.compile("^device/.+/.+/info/putFileTransferStatus/.+$")),
    CLOUD_FILE_LIST_REQUEST("云文件列表请求",
        Pattern.compile("^device/.+/.+/info/reqCloudFileList/.+$")),

    // 设备操作相关 - 雕刻机专用
    DEVICE_DISCONNECT("设备断开连接",
        Pattern.compile("^device/.+/.+/info/disConnect/.+$")),
    SCAN_CODE_REPORT("扫码上报",
        Pattern.compile("^device/.+/.+/info/putNewScanCode/.+$")),

    // 告警和数据上报 - 雕刻机专用
    ALARM_REPORT("告警上报",
        Pattern.compile("^device/.+/.+/info/putAlarm/.+$")),
    BURIED_DATA_REPORT("埋点数据上报",
        Pattern.compile("^device/.+/.+/info/putBuriedData/.+$")),
    JOB_DESC_DATA_REPORT("加工数据上报",
        Pattern.compile("^device/.+/.+/info/putJobDescData/.+$")),

    // 净化器特有
    FILTER_LIFE_CHANGE("滤芯寿命变化",
        Pattern.compile("^.+/device/ap/.+/info/putFilterLifeChange$")),

    // 未知消息类型
    UNKNOWN("未知消息", null);

    private final String description;
    private final Pattern pattern;

    MessageType(String description, Pattern pattern) {
        this.description = description;
        this.pattern = pattern;
    }

    /**
     * 根据topic匹配消息类型
     * 注意：由于IotSubscribeConfig已经按version精确订阅，这里收到的都是正确版本的消息
     */
    public static MessageType fromTopic(String topic) {
        if (topic == null || topic.isEmpty()) {
            return UNKNOWN;
        }

        for (MessageType type : values()) {
            if (type != UNKNOWN && type.pattern != null && type.pattern.matcher(topic).matches()) {
                return type;
            }
        }

        return UNKNOWN;
    }

    public String getDescription() {
        return description;
    }

    public Pattern getPattern() {
        return pattern;
    }
}
