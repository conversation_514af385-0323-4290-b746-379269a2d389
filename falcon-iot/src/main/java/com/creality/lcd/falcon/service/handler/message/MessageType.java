package com.creality.lcd.falcon.service.handler.message;

import java.util.regex.Pattern;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 16:30
 * @Description: IoT消息类型枚举
 */
public enum MessageType {

    // OTA相关 - 雕刻机格式: device/{model}/{sn}/OTA/reqVersion/{version}
    OTA_REQUEST("OTA请求", "^device/.+/.+/OTA/reqVersion/{version}$"),
    OTA_STATUS_REPORT("OTA状态上报", "^device/.+/.+/OTA/putOTAStatus/{version}$"),

    // 设备状态相关
    // 雕刻机: device/{model}/{sn}/info/putDeviceStatus/{version}
    // 净化器: {version}/device/ap/{sn}/info/putApDeviceStatus
    DEVICE_STATUS_REPORT("设备状态上报", "^device/.+/.+/info/putDeviceStatus/{version}$|^{version}/device/ap/.+/info/putApDeviceStatus$"),

    // 雕刻机: device/{model}/{sn}/info/online/{version}
    // 净化器: {version}/device/ap/{sn}/info/online
    DEVICE_ONLINE_REPORT("设备上线上报", "^device/.+/.+/info/online/{version}$|^{version}/device/ap/.+/info/online$"),

    // 雕刻机: device/{model}/{sn}/info/offLine/{version}
    // 净化器: {version}/device/ap/{sn}/info/offLine
    DEVICE_OFFLINE_REPORT("设备离线上报", "^device/.+/.+/info/offLine/{version}$|^{version}/device/ap/.+/info/offLine$"),

    // 网络相关
    // 雕刻机: device/{model}/{sn}/info/putNewIPConnect/{version}
    // 净化器: {version}/device/ap/{sn}/info/putIPChange
    IP_REPORT("IP上报", "^device/.+/.+/info/putNewIPConnect/{version}$|^{version}/device/ap/.+/info/putIPChange$"),

    // 文件传输相关 - 雕刻机专用
    FILE_TRANSFER_STATUS_REPORT("文件传输状态上报", "^device/.+/.+/info/putFileTransferStatus/{version}$"),
    CLOUD_FILE_LIST_REQUEST("云文件列表请求", "^device/.+/.+/info/reqCloudFileList/{version}$"),

    // 设备操作相关 - 雕刻机专用
    DEVICE_DISCONNECT("设备断开连接", "^device/.+/.+/info/disConnect/{version}$"),
    SCAN_CODE_REPORT("扫码上报", "^device/.+/.+/info/putNewScanCode/{version}$"),

    // 告警和数据上报 - 雕刻机专用
    ALARM_REPORT("告警上报", "^device/.+/.+/info/putAlarm/{version}$"),
    BURIED_DATA_REPORT("埋点数据上报", "^device/.+/.+/info/putBuriedData/{version}$"),
    JOB_DESC_DATA_REPORT("加工数据上报", "^device/.+/.+/info/putJobDescData/{version}$"),

    // 净化器特有 - {version}/device/ap/{sn}/info/putFilterLifeChange
    FILTER_LIFE_CHANGE("滤芯寿命变化", "^{version}/device/ap/.+/info/putFilterLifeChange$"),

    // 未知消息类型
    UNKNOWN("未知消息", null);

    private final String description;
    private final String patternTemplate;

    MessageType(String description, String patternTemplate) {
        this.description = description;
        this.patternTemplate = patternTemplate;
    }



    /**
     * 根据topic匹配消息类型（需要外部传入version）
     * @param topic MQTT主题
     * @param version 从配置文件读取的版本号
     * @return 匹配的消息类型
     */
    public static MessageType fromTopic(String topic, String version) {
        if (topic == null || topic.isEmpty() || version == null) {
            return UNKNOWN;
        }

        for (MessageType type : values()) {
            if (type != UNKNOWN && type.patternTemplate != null) {
                // 将{version}替换为配置文件中的version值
                String actualPattern = type.patternTemplate.replace("{version}", version);
                Pattern pattern = Pattern.compile(actualPattern);

                if (pattern.matcher(topic).matches()) {
                    return type;
                }
            }
        }

        return UNKNOWN;
    }

    public String getDescription() {
        return description;
    }

    public String getPatternTemplate() {
        return patternTemplate;
    }
}
