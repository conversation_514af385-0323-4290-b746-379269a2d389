package com.creality.lcd.falcon.service.handler.laser;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 09:30
 * @Description: Falcon系列雕刻机通用消息处理器 (A1 Pro & T1)
 */
@Component
public class FalconSeriesMessageHandler extends AbstractLaserEngraverHandler {

    // 支持的Falcon系列型号
    private static final Set<String> SUPPORTED_MODELS = Set.of(
        "Creality Falcon A1 Pro",
        "Creality Falcon T1"
    );

    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODELS.contains(deviceModel);
    }

    @Override
    protected String getSupportedModel() {
        return "Falcon Series (A1 Pro & T1)";
    }

    // ==================== Falcon系列通用处理方法 ====================
    // 这些方法对A1 Pro和T1都适用，使用相同的逻辑

    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备OTA请求: model={}, sn={}", model, sn);
        // Falcon系列通用的OTA处理逻辑
        iotService.getOtaVersion(model, messageMap);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备状态上报: model={}, sn={}", model, sn);
        // Falcon系列通用的状态处理逻辑
        iotService.updateDeviceWorkStatus(sn, messageMap);
    }

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备上线上报: model={}, sn={}", model, sn);
        // Falcon系列通用的上线处理逻辑
        iotService.updateDeviceOnline(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备离线上报: model={}, sn={}", model, sn);
        // Falcon系列通用的离线处理逻辑
        iotService.offLine(messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备IP上报: model={}, sn={}", model, sn);
        // Falcon系列通用的IP处理逻辑
        iotService.ipReport(model, sn, messageMap);
    }

    @Override
    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备OTA状态上报: model={}, sn={}", model, sn);
        // Falcon系列通用的OTA状态处理逻辑
        iotService.updateOtaStatus(model, sn, messageMap);
    }

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备文件传输状态上报: model={}, sn={}", model, sn);
        // Falcon系列通用的文件传输处理逻辑
        iotService.updateFileTransferStatus(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备断开连接: model={}, sn={}", model, sn);
        // Falcon系列通用的断开连接处理逻辑
        iotService.deviceDisConnect(model, sn, messageMap);
    }

    @Override
    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备云文件列表请求: model={}, sn={}", model, sn);
        // Falcon系列通用的云文件列表处理逻辑
        iotService.getUserCloudFileList(model, sn, messageMap);
    }

    @Override
    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备扫码上报: model={}, sn={}", model, sn);
        // Falcon系列通用的扫码处理逻辑
        iotService.putNewScanCode(model, sn, messageMap);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备告警上报: model={}, sn={}", model, sn);
        // Falcon系列通用的告警处理逻辑
        iotService.putAlarm(model, sn, messageMap);
    }

    @Override
    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备埋点数据上报: model={}, sn={}", model, sn);
        // Falcon系列通用的埋点数据处理逻辑
        iotService.putBuriedData(model, sn, messageMap);
    }

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("设备加工数据上报: model={}, sn={}", model, sn);
        // Falcon系列通用的加工数据处理逻辑
        iotService.putJobDescData(model, sn, messageMap);
    }

    // ==================== Falcon系列通用的特殊功能 ====================
    // 这些功能A1 Pro和T1都支持，但可能在具体实现上有细微差别

    /**
     * 处理Falcon系列的高级功能请求
     * A1 Pro和T1都支持，但具体参数可能不同
     */
    protected void handleAdvancedFeatureRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列高级功能请求: model={}, sn={}", model, sn);
        
        // 通用的高级功能处理逻辑
        String featureType = (String) messageMap.get("featureType");
        
        if ("SMART_MODE".equals(featureType)) {
            handleSmartMode(model, sn, messageMap);
        } else if ("PRECISION_CONTROL".equals(featureType)) {
            handlePrecisionControl(model, sn, messageMap);
        } else if ("SAFETY_CHECK".equals(featureType)) {
            handleSafetyCheck(model, sn, messageMap);
        } else {
            log.warn("Falcon系列收到未知的高级功能请求: featureType={}", featureType);
        }
    }

    /**
     * 处理智能模式 - A1 Pro和T1通用
     */
    private void handleSmartMode(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列智能模式: model={}, sn={}", model, sn);
        // 通用的智能模式处理逻辑
        // 如果后续A1 Pro和T1有差异，可以在各自的处理器中重写
    }

    /**
     * 处理精密控制 - A1 Pro和T1通用
     */
    private void handlePrecisionControl(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列精密控制: model={}, sn={}", model, sn);
        // 通用的精密控制处理逻辑
    }

    /**
     * 处理安全检查 - A1 Pro和T1通用
     */
    private void handleSafetyCheck(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon系列安全检查: model={}, sn={}", model, sn);
        // 通用的安全检查处理逻辑
    }

    // ==================== Falcon系列通用的Topic识别 ====================

    @Override
    protected boolean isModelSpecificTopic(String topic) {
        return isAdvancedFeatureTopic(topic);
    }

    /**
     * 检查是否为Falcon系列高级功能topic
     */
    private boolean isAdvancedFeatureTopic(String topic) {
        return topic.matches("^device/(Creality Falcon A1 Pro|Creality Falcon T1)/.+/info/putAdvancedFeature/" + version + "$");
    }

    @Override
    protected void handleModelSpecificMessage(String topic, Map<String, Object> messageMap) {
        String[] topicParts = topic.split("/");
        String model = extractModel(topicParts);
        String sn = extractSn(topicParts);

        if (isAdvancedFeatureTopic(topic)) {
            handleAdvancedFeatureRequest(model, sn, messageMap);
        } else {
            log.warn("Falcon系列收到未识别的特殊消息: topic={}", topic);
        }
    }

    // ==================== 工具方法 ====================
}
