package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

import static com.creality.lcd.falcon.service.handler.MessageTopicConstants.*;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: 雕刻机消息处理器 - 极简版
 * 处理所有雕刻机型号 (A1 Pro, T1, 未来型号)
 * 使用基类的通用方法，只重写不同的部分
 */
@Component
public class LaserEngraverHandler extends BaseIotHandler {

    @Autowired
    private IotService iotService;

    // 支持的雕刻机型号 - 使用常量
    private static final Set<String> SUPPORTED_MODELS = Set.of(
        MODEL_FALCON_A1_PRO,
        MODEL_FALCON_T1
        // 未来新增型号在这里添加
    );

    @Override
    public boolean supports(String deviceModel) {
        return SUPPORTED_MODELS.contains(deviceModel);
    }

    @Override
    protected Object getService() {
        return iotService;
    }

    @Override
    protected String getDeviceType() {
        return "雕刻机";
    }

    // ==================== 雕刻机特有的消息处理 ====================
    // 只有当雕刻机有特殊逻辑时才重写，否则使用基类方法

    @Override
    protected void handleCustomMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        // 雕刻机特有的消息类型处理 - 使用常量
        if (containsPath(topic, INFO_PUT_SPECIAL_FEATURE)) {
            handleSpecialFeature(model, sn, messageMap);
        } else {
            // 调用基类方法，会检查Future响应和系统内部消息
            super.handleCustomMessage(topic, model, sn, messageMap);
        }
    }

    /**
     * 雕刻机特有功能处理
     */
    private void handleSpecialFeature(String model, String sn, Map<String, Object> messageMap) {
        log.info("处理雕刻机特有功能: model={}, sn={}", model, sn);
        
        // 如果A1 Pro和T1需要不同处理 - 使用常量
        if (isA1Pro(model)) {
            // A1 Pro特有逻辑
            log.info("A1 Pro特有处理");
        } else if (isT1(model)) {
            // T1特有逻辑
            log.info("T1特有处理");
        }
        
        // 调用Service方法 - 使用常量
        callServiceMethod(METHOD_HANDLE_SPECIAL_FEATURE, model, sn, messageMap);
    }

    // ==================== 工具方法 ====================

    @Override
    protected String extractModel(String[] topicParts) {
        // 雕刻机格式: device/{model}/{sn}/...
        return topicParts.length > 1 ? topicParts[1] : "";
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 雕刻机格式: device/{model}/{sn}/...
        return topicParts.length > 2 ? topicParts[2] : "";
    }

    // ==================== 如果需要重写基类方法 ====================
    // 只有当雕刻机的处理逻辑与基类不同时才重写

    // 示例：如果雕刻机的上线处理有特殊逻辑
    // @Override
    // protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
    //     log.info("雕刻机上线特殊处理: model={}, sn={}", model, sn);
    //     
    //     // 雕刻机特有的上线逻辑
    //     if ("Creality Falcon A1 Pro".equals(model)) {
    //         // A1 Pro上线特殊处理
    //     }
    //     
    //     // 调用基类方法
    //     super.handleDeviceOnlineReport(model, sn, messageMap);
    // }
}
