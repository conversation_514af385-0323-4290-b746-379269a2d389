package com.creality.lcd.falcon.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.session.SessionManagerAll;

import jakarta.servlet.http.HttpServletRequest;


@Component
public class SessionManage {

    //private static final Logger log = LogManager.getLogger(SessionManage.class);
    
    @Autowired
    private SessionManagerAll sessionManagerAll;

    /**
     * 检查SessionId是否过期
     * @param sessionId
     * @return
     */
    public boolean checkSessionExpired(String sessionId){
    	return sessionManagerAll.checkSessionExpired(sessionId);
    }

    /**
     * 获取SessionId
     * @return
     */
    public String getSessionId(){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        return request.getHeader("sessionId");
    }

    /**
     * 通过sessionId获取当前用户信息
     * @return
     */
    public FalconAccount getUserInfo(){
        String sessionId = getSessionId();
        return sessionManagerAll.getUserInfo(sessionId);
    }
}
