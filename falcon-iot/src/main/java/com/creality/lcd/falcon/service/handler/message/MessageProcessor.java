package com.creality.lcd.falcon.service.handler.message;

import java.util.Map;

/**
 * @Author: tiant<PERSON>
 * @Date: 2025/7/14 16:30
 * @Description: 消息处理器接口
 */
@FunctionalInterface
public interface MessageProcessor {
    
    /**
     * 处理消息
     * @param model 设备型号
     * @param sn 设备序列号
     * @param messageMap 消息内容
     */
    void process(String model, String sn, Map<String, Object> messageMap);
}
