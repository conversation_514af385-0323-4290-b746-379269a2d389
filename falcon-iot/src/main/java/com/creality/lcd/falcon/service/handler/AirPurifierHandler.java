package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotApService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.creality.lcd.falcon.service.handler.MessageTopicConstants.*;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: 空气净化器消息处理器 - 极简版
 * 处理所有空气净化器型号
 * 使用基类的通用方法，只重写不同的部分
 */
@Component
public class AirPurifierHandler extends BaseIotHandler {

    @Autowired
    private IotApService iotApService;

    @Override
    public boolean supports(String deviceModel) {
        return isAP(deviceModel);
    }

    @Override
    protected Object getService() {
        return iotApService;
    }

    @Override
    protected String getDeviceType() {
        return "空气净化器";
    }

    // ==================== 空气净化器特有的消息处理 ====================

    @Override
    protected void handleCustomMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        // 空气净化器特有的消息类型 - 使用常量
        if (containsPath(topic, INFO_PUT_FILTER_LIFE_CHANGE)) {
            handleFilterLifeChange(model, sn, messageMap);
        }  else {
            super.handleCustomMessage(topic, model, sn, messageMap);
        }
    }

    /**
     * 滤芯寿命变化 - 空气净化器特有
     */
    private void handleFilterLifeChange(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器滤芯寿命上报: model={}, sn={}", model, sn);
        callServiceMethod(METHOD_PUT_FILTER_LIFE_CHANGE, sn, messageMap);
    }

    /**
     * 空气质量上报 - 空气净化器特有
     */
    private void handleAirQualityReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器空气质量上报: model={}, sn={}", model, sn);
        callServiceMethod(METHOD_PUT_AIR_QUALITY_DATA, sn, messageMap);
    }

    // ==================== 重写基类方法 - 适配空气净化器的Service方法名 ====================

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器上线上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名 - 使用常量
        callServiceMethod(METHOD_UPDATE_AP_DEVICE_ONLINE, sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器离线上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名 - 使用常量
        callServiceMethod(METHOD_AP_OFF_LINE, messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器IP上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名 - 使用常量
        callServiceMethod(METHOD_AP_IP_REPORT, sn, messageMap);
    }

    // ==================== 工具方法 ====================

    @Override
    protected String extractModel(String[] topicParts) {
        // 空气净化器固定型号 - 使用常量
        return MODEL_FALCON_AP;
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 空气净化器格式: {version}/device/ap/{sn}/...
        return topicParts.length > 3 ? topicParts[3] : "";
    }
}
