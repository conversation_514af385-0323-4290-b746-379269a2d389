package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotApService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.creality.lcd.falcon.service.handler.MessageTopicConstants.*;

/**
 * @Author: tiantao
 * @Date: 2025/7/14 19:00
 * @Description: 空气净化器消息处理器 - 极简版
 * 处理所有空气净化器型号
 * 使用基类的通用方法，只重写不同的部分
 */
@Component
public class AirPurifierHandler extends BaseIotHandler {

    @Autowired
    private IotApService iotApService;

    @Override
    public boolean supports(String deviceModel) {
        return "Creality Falcon AP".equals(deviceModel) ||
               (deviceModel != null && deviceModel.contains("Falcon") && deviceModel.contains("AP"));
    }

    @Override
    protected Object getService() {
        return iotApService;
    }

    @Override
    protected String getDeviceType() {
        return "空气净化器";
    }

    // ==================== 空气净化器特有的消息处理 ====================

    @Override
    protected void handleCustomMessage(String topic, String model, String sn, Map<String, Object> messageMap) {
        // 空气净化器特有的消息类型
        if (topic.contains("/info/putFilterLifeChange")) {
            handleFilterLifeChange(model, sn, messageMap);
        } else {
            super.handleCustomMessage(topic, model, sn, messageMap);
        }
    }

    /**
     * 滤芯寿命变化 - 空气净化器特有
     */
    private void handleFilterLifeChange(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器滤芯寿命上报: model={}, sn={}", model, sn);
        callServiceMethod("putFilterLifeChange", sn, messageMap);
    }

    // ==================== 重写基类方法 - 适配空气净化器的Service方法名 ====================

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器上线上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名
        callServiceMethod("updateApDeviceOnline", sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器离线上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名
        callServiceMethod("apOffLine", messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("空气净化器IP上报: model={}, sn={}", model, sn);
        // 空气净化器使用不同的Service方法名
        callServiceMethod("apIpReport", sn, messageMap);
    }

    // ==================== 工具方法 ====================

    @Override
    protected String extractModel(String[] topicParts) {
        // 空气净化器固定型号
        return "Creality Falcon AP";
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 空气净化器格式: {version}/device/ap/{sn}/...
        return topicParts.length > 3 ? topicParts[3] : "";
    }
}
