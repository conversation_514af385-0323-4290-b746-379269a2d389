package com.creality.lcd.falcon.service.handler;

import com.creality.lcd.falcon.service.IotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 12:00
 * @Description: 激光雕刻机消息处理器
 */
@Component
public class LaserEngraverMessageHandler extends AbstractIotMessageHandler {

    @Autowired
    private IotService iotService;

    // 支持的雕刻机型号
    private static final Set<String> SUPPORTED_MODELS = Set.of(
        "Creality Falcon A1 Pro",
        "Creality Falcon T1"
    );

    @Override
    public boolean supports(String deviceModel) {
        // 精确匹配支持的型号
        if (SUPPORTED_MODELS.contains(deviceModel)) {
            return true;
        }

        // 兼容性匹配：支持包含Falcon但不是AP的设备
        return deviceModel != null &&
               deviceModel.contains("Falcon") &&
               !deviceModel.contains("AP") &&
               !deviceModel.equals("Creality Falcon AP");
    }

    @Override
    public int getPriority() {
        return 10; // 雕刻机处理器优先级较高
    }

    @Override
    protected String extractModel(String[] topicParts) {
        // 雕刻机topic格式: device/{model}/{sn}/...
        return topicParts.length > 1 ? topicParts[1] : "";
    }

    @Override
    protected String extractSn(String[] topicParts) {
        // 雕刻机topic格式: device/{model}/{sn}/...
        return topicParts.length > 2 ? topicParts[2] : "";
    }

    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机OTA请求: model={}, sn={}", model, sn);
        iotService.getOtaVersion(model, messageMap);
    }

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机状态上报: model={}, sn={}", model, sn);
        iotService.updateDeviceWorkStatus(sn, messageMap);
    }

    @Override
    protected void handleDeviceOnlineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机上线上报: model={}, sn={}", model, sn);
        iotService.updateDeviceOnline(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceOfflineReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机离线上报: model={}, sn={}", model, sn);
        iotService.offLine(messageMap);
    }

    @Override
    protected void handleIpReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机IP上报: model={}, sn={}", model, sn);
        iotService.ipReport(model, sn, messageMap);
    }

    @Override
    protected void handleOtaStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机OTA状态上报: model={}, sn={}", model, sn);
        iotService.updateOtaStatus(model, sn, messageMap);
    }

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机文件传输状态上报: model={}, sn={}", model, sn);
        iotService.updateFileTransferStatus(model, sn, messageMap);
    }

    @Override
    protected void handleDeviceDisconnect(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机断开连接: model={}, sn={}", model, sn);
        iotService.deviceDisConnect(model, sn, messageMap);
    }

    @Override
    protected void handleCloudFileListRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机云文件列表请求: model={}, sn={}", model, sn);
        iotService.getUserCloudFileList(model, sn, messageMap);
    }

    @Override
    protected void handleScanCodeReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机扫码上报: model={}, sn={}", model, sn);
        iotService.putNewScanCode(model, sn, messageMap);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机告警上报: model={}, sn={}", model, sn);
        iotService.putAlarm(model, sn, messageMap);
    }

    @Override
    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机埋点数据上报: model={}, sn={}", model, sn);
        iotService.putBuriedData(model, sn, messageMap);
    }

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("雕刻机加工数据上报: model={}, sn={}", model, sn);
        iotService.putJobDescData(model, sn, messageMap);
    }

    @Override
    protected boolean isOtaRequest(String topic) {
        return topic.matches("^device/.+/.+/OTA/reqVersion/" + version + "$");
    }

    @Override
    protected boolean isDeviceStatusReport(String topic) {
        return topic.matches("^device/.+/.+/info/putDeviceStatus/" + version + "$");
    }

    @Override
    protected boolean isDeviceOnlineReport(String topic) {
        return topic.matches("^device/.+/.+/info/online/" + version + "$");
    }

    @Override
    protected boolean isDeviceOfflineReport(String topic) {
        return topic.matches("^device/.+/.+/info/offLine/" + version + "$");
    }

    @Override
    protected boolean isIpReport(String topic) {
        return topic.matches("^device/.+/.+/info/putNewIPConnect/" + version + "$");
    }

    @Override
    protected boolean isOtaStatusReport(String topic) {
        return topic.matches("^device/.+/.+/OTA/putOTAStatus/" + version + "$");
    }

    @Override
    protected boolean isFileTransferStatusReport(String topic) {
        return topic.matches("^device/.+/.+/info/putFileTransferStatus/" + version + "$");
    }

    @Override
    protected boolean isDeviceDisconnect(String topic) {
        return topic.matches("^device/.+/.+/info/disConnect/" + version + "$");
    }

    @Override
    protected boolean isCloudFileListRequest(String topic) {
        return topic.matches("^device/.+/.+/info/reqCloudFileList/" + version + "$");
    }

    @Override
    protected boolean isScanCodeReport(String topic) {
        return topic.matches("^device/.+/.+/info/putNewScanCode/" + version + "$");
    }

    @Override
    protected boolean isAlarmReport(String topic) {
        return topic.matches("^device/.+/.+/info/putAlarm/" + version + "$");
    }

    @Override
    protected boolean isBuriedDataReport(String topic) {
        return topic.matches("^device/.+/.+/info/putBuriedData/" + version + "$");
    }

    @Override
    protected boolean isJobDescDataReport(String topic) {
        return topic.matches("^device/.+/.+/info/putJobDescData/" + version + "$");
    }
}
