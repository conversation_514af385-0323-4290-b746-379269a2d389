package com.creality.lcd.falcon.service.handler.laser;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: tiantao
 * @Date: 2025/7/11 16:40
 * @Description: Creality Falcon T1 专用消息处理器
 * 注意：大部分功能使用FalconSeriesMessageHandler的通用逻辑
 * 只有T1特有的差异化功能才在这里处理
 */
@Component
public class FalconT1MessageHandler extends AbstractLaserEngraverHandler {

    private static final String SUPPORTED_MODEL = "Creality Falcon T1";

    @Override
    public boolean supports(String deviceModel) {
        // 只有在需要T1特有处理逻辑时才返回true
        // 目前暂时返回false，让FalconSeriesMessageHandler处理
        return false; // 暂时禁用，使用通用处理器
    }

    // 移除优先级设置，使用默认优先级

    @Override
    protected String getSupportedModel() {
        return SUPPORTED_MODEL;
    }

    // ==================== Falcon T1 特有功能 ====================
    // 注意：只有当T1需要与A1 Pro不同的处理逻辑时，才在这里实现
    // 目前T1和A1 Pro使用相同的逻辑，所以这些方法暂时保留作为示例

    /**
     * 处理T1特有的温度监控功能
     * 示例：当T1的温度监控功能与A1 Pro有差异时启用
     */
    private void handleT1TemperatureMonitoring(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1特有温度监控: model={}, sn={}, data={}", model, sn, messageMap);
        // T1特有的温度监控逻辑
        // 例如：T1有更精确的温度传感器，或不同的温度控制算法
    }

    /**
     * 处理T1特有的批量处理功能
     * 示例：当T1的批量处理功能与A1 Pro有差异时启用
     */
    private void handleT1BatchProcessing(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1特有批量处理: model={}, sn={}, data={}", model, sn, messageMap);
        // T1特有的批量处理逻辑
        // 例如：T1支持更大的批量任务，或有不同的调度算法
    }

    // ==================== 重写基类方法以实现T1特有逻辑 ====================

    @Override
    protected void handleDeviceStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1状态上报: model={}, sn={}", model, sn);
        
        // T1可能有特殊的状态字段
        if (messageMap.containsKey("turboMode")) {
            log.info("T1快速模式状态: {}", messageMap.get("turboMode"));
        }
        if (messageMap.containsKey("temperature")) {
            log.info("T1温度状态: {}", messageMap.get("temperature"));
        }
        
        // 调用基类的通用处理
        super.handleDeviceStatusReport(model, sn, messageMap);
    }

    @Override
    protected void handleOtaRequest(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1 OTA请求: model={}, sn={}", model, sn);
        
        // T1可能有特殊的OTA要求
        if (messageMap.containsKey("firmwareType")) {
            String firmwareType = (String) messageMap.get("firmwareType");
            if ("TURBO_FIRMWARE".equals(firmwareType)) {
                log.info("T1请求快速雕刻固件更新");
                // 处理T1特有的固件类型
            }
        }
        
        // 调用基类的通用处理
        super.handleOtaRequest(model, sn, messageMap);
    }

    @Override
    protected void handleAlarmReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1告警上报: model={}, sn={}", model, sn);
        
        // T1特有的告警类型
        Object alarmType = messageMap.get("alarmType");
        if ("TEMPERATURE_OVERHEAT".equals(alarmType)) {
            log.warn("T1温度过热告警: sn={}", sn);
            // 处理温度过热的特殊逻辑
        } else if ("PRECISION_POSITIONING_ERROR".equals(alarmType)) {
            log.warn("T1精密定位错误告警: sn={}", sn);
            // 处理定位错误的特殊逻辑
        } else if ("BATCH_PROCESSING_INTERRUPTED".equals(alarmType)) {
            log.warn("T1批量加工中断告警: sn={}", sn);
            // 处理批量加工中断的特殊逻辑
        }
        
        // 调用基类的通用处理
        super.handleAlarmReport(model, sn, messageMap);
    }

    // ==================== T1特有的Topic识别 ====================
    // 注意：目前T1使用通用处理器，这些方法暂时保留作为示例
    // 当需要T1特有处理时，可以启用supports()方法并实现这些逻辑

    /**
     * 示例：T1特有的topic识别
     * 当需要T1特有处理时启用
     */
    private boolean isT1SpecificTopic(String topic) {
        return topic.matches("^device/Creality Falcon T1/.+/info/putT1Specific/" + version + "$");
    }

    /**
     * 示例：T1特有的消息处理
     * 当需要T1特有处理时启用
     */
    private void handleT1SpecificMessage(String topic, Map<String, Object> messageMap) {
        String[] topicParts = topic.split("/");
        String model = extractModel(topicParts);
        String sn = extractSn(topicParts);

        if (isT1SpecificTopic(topic)) {
            handleT1TemperatureMonitoring(model, sn, messageMap);
        } else {
            log.warn("Falcon T1收到未识别的特殊消息: topic={}", topic);
        }
    }

    // ==================== T1特有的参数处理 ====================

    @Override
    protected void handleJobDescDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1加工数据上报: model={}, sn={}", model, sn);
        
        // T1可能有批量加工的特殊数据
        if (messageMap.containsKey("batchCount")) {
            log.info("T1批量加工数量: {}", messageMap.get("batchCount"));
        }
        if (messageMap.containsKey("turboModeUsed")) {
            log.info("T1是否使用快速模式: {}", messageMap.get("turboModeUsed"));
        }
        if (messageMap.containsKey("averageTemperature")) {
            log.info("T1平均温度: {}", messageMap.get("averageTemperature"));
        }
        
        // 调用基类的通用处理
        super.handleJobDescDataReport(model, sn, messageMap);
    }

    @Override
    protected void handleFileTransferStatusReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1文件传输状态上报: model={}, sn={}", model, sn);
        
        // T1可能支持批量文件传输
        Object batchTransfer = messageMap.get("batchTransfer");
        if (Boolean.TRUE.equals(batchTransfer)) {
            log.info("T1批量文件传输模式");
            // 批量文件传输的特殊处理逻辑
        }
        
        // 调用基类的通用处理
        super.handleFileTransferStatusReport(model, sn, messageMap);
    }

    @Override
    protected void handleBuriedDataReport(String model, String sn, Map<String, Object> messageMap) {
        log.info("Falcon T1埋点数据上报: model={}, sn={}", model, sn);
        
        // T1可能有特殊的埋点数据
        if (messageMap.containsKey("turboModeUsageTime")) {
            log.info("T1快速模式使用时长: {}", messageMap.get("turboModeUsageTime"));
        }
        if (messageMap.containsKey("temperatureHistory")) {
            log.info("T1温度历史数据: {}", messageMap.get("temperatureHistory"));
        }
        
        // 调用基类的通用处理
        super.handleBuriedDataReport(model, sn, messageMap);
    }

    // ==================== T1特有的工具方法 ====================

    /**
     * 检查T1是否处于快速雕刻模式
     */
    private boolean isTurboModeEnabled(Map<String, Object> messageMap) {
        return Boolean.TRUE.equals(messageMap.get("turboMode"));
    }

    /**
     * 检查T1温度是否正常
     */
    private boolean isTemperatureNormal(Map<String, Object> messageMap) {
        Object temperature = messageMap.get("temperature");
        if (temperature instanceof Number) {
            double temp = ((Number) temperature).doubleValue();
            return temp >= 20 && temp <= 80; // T1正常工作温度范围
        }
        return true; // 如果没有温度数据，假设正常
    }
}
