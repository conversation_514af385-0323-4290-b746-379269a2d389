server:
  tomcat:
    connection-timeout: 30s
    threads:
      max: 150
      min-spare: 30
    accept-count: 100
    max-connections: 800
  servlet:
    context-parameters:
      env: test

spring:
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
  datasource:
    hikari:
      maximum-pool-size: 25
      minimum-idle: 15
      connection-timeout: 8000
      idle-timeout: 300000
      max-lifetime: 1200000
    url: *************************************************************************************************************************************************************************************************************
    username: root
    password: ucr@123
  data:
    redis:
      host: **************
      port: 6379
      database: 2
      password: ucr@123
    mongodb:
      host: **************
      port: 27017
      database: falcon_stat
      username: admin
      password: admin@2025..


# AES
security:
  crypto-enable: false
  aes-key: ZHdueHRoZWtleTkwMjkwMg==

logging:
  level:
    org.apache.ibatis: warn
    org.mybatis.spring.SqlSessionUtils: warn
    com.zaxxer.hikari: warn

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

oss:
  endpoint: oss-cn-shenzhen.aliyuncs.com
  access-key-id: LTAI5tDN4qNDxmFv2f883DoA
  access-key-secret: ******************************
  bucket-name: falcon-test-file
  custom-domain: https://falcon-test.craftseek.com/

S3:
  access-key: ********************
  secret-key: 1wU/ZbtCVwjG6tFcDZiSRCw4ajpVhjNaai5GTvxr
  bucket-name: creality-account
  custom-domain: https://creality-images.s3.us-east-2.amazonaws.com/

cx-cloud:
  grant-type: authorization_code
  client-id: 9c7c674118a455f8af1af67c24715823
  client-secret: 1013807b-f1fc-4d02-8ffa-1ca6c811e631
  token-url: https://admin-pre.crealitycloud.com/api/cxy/oauth2/token
  user-info-url: https://admin-pre.crealitycloud.com/api/cxy/oauth2/user/getUserInfo
  redirect-url: http://craftseek.com/

back-avatar:
  url: https://pic2-cdn.creality.com/avatar/default/A912A1F6067E97B041F5C4417402BCB3.png

aws-mqtt:
  enabled : true
  endpoint: a179pyz0mljc6j-ats.iot.us-east-2.amazonaws.com
  cert-path: /home/<USER>/falcon-iot/5d5d2b0b108c7bd8ffb4f04d2932eee6d80e898892188558cc2b153ef94287d5-certificate.pem.crt
  private-key-path: /home/<USER>/falcon-iot/5d5d2b0b108c7bd8ffb4f04d2932eee6d80e898892188558cc2b153ef94287d5-private.pem.key
  ca-path: /home/<USER>/falcon-iot/AmazonRootCA1.pem

iot:
  topic:
    version: test

bloom:
  key: "{bloom:iotInfoList}"        # Redis 中的 Key
  capacity: 100_000_000 # 上限：1 亿
  error-rate: 0.03      # 误判率：3%
  lock-key: "{bloom:iotInfoList:lock}"
  counter-key: "{bloom:iotInfoList:count}"