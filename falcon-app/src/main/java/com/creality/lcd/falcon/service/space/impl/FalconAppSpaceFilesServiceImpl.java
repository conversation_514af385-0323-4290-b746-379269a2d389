package com.creality.lcd.falcon.service.space.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.creality.lcd.falcon.mappers.app.FalconUserFilesMapper;
import com.creality.lcd.falcon.page.PageAppResult;
import com.creality.lcd.falcon.pojo.dto.app.AppFileRenameDto;
import com.creality.lcd.falcon.pojo.dto.app.AppSpaceFileUploadDto;
import com.creality.lcd.falcon.pojo.dto.app.AppSpaceFileConflictHandleDto;
import com.creality.lcd.falcon.response.BaseResponse;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import com.creality.lcd.falcon.pojo.entity.account.FalconAccount;
import com.creality.lcd.falcon.pojo.entity.app.FalconUserFiles;
import com.creality.lcd.falcon.pojo.vo.app.AppSpaceFileListVo;
import com.creality.lcd.falcon.pojo.vo.app.AppSpaceFileUploadVo;
import com.creality.lcd.falcon.pojo.vo.app.AppSpaceUserCapacityVo;
import com.creality.lcd.falcon.service.space.FalconAppSpaceFilesService;
import com.creality.lcd.falcon.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: zhangshaoming
 * @Date: 2025/01/07 16:31
 * @Description:
 */
@Service
public class FalconAppSpaceFilesServiceImpl implements FalconAppSpaceFilesService {

    private static final Logger log = LogManager.getLogger(FalconAppSpaceFilesServiceImpl.class);

    private static final String FILENAME_PATTERN = "^[a-zA-Z0-9_]{1,50}$";

    @Autowired
    private SessionManage sessionManage;

    @Autowired
    private FalconUserFilesMapper userFilesMapper;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private S3Utils s3Utils;

    @Autowired
    private Environment environment;

    @Autowired
    private EnvironmentUtils environmentUtils;

    /**
     * 我的空间-文件列表-上传文件
     * @return
     */
    @Override
    public AppSpaceFileUploadVo uploadSpaceFiles(AppSpaceFileUploadDto uploadDto) {

        //判断请求参数是否准确
        List<String> sceneList = Arrays.asList("recent","draft");
        if(!sceneList.contains(uploadDto.getScene())){
            throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_SCENE_PARAMETER_ERROR);
        }
        //获取当前用户信息
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        //获取当前时间
        LocalDateTime now = LocalDateTime.now();
        AppSpaceFileUploadVo uploadVo = new AppSpaceFileUploadVo();
        //查询当前用户下的指定类型的文件列表
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.eq("scene", uploadDto.getScene());
        userFilesQuery.eq("user_id",account.getUserId());
        userFilesQuery.orderByDesc("create_time");
        List<FalconUserFiles> userFileList = userFilesMapper.selectList(userFilesQuery);

        int fdsSize = 0;
        if(0 == uploadDto.getFdsSize()){
            fdsSize = getFdsSizeByFdsUrl(uploadDto.getFdsPath());
        }
        Long fileId = uploadDto.getFileId();
        //判断场景：最近打开 recent 草稿箱 draft
        //如果是草稿箱,只能存储20个,如果当前数量大于等于20,则报错提示用户:您的草稿箱已满,请先清理草稿箱中的文件
        if("draft".equals(uploadDto.getScene())){
            final int MAX_CAPACITY = 50 * 1024 * 1024;
            final int MAX_NAME_LEN = 50;
            if(uploadDto.getFileName().length() > MAX_NAME_LEN){
                throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_FILENAME_INVALID);
            }
            boolean duplicate = userFileList.stream()
                    .filter(f -> !Objects.equals(f.getId(), fileId))
                    .anyMatch(f -> f.getFileName().equals(uploadDto.getFileName()));
            if (duplicate) {
                throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_FILENAME_REPEATED);
            }
            int usedCapacity = userFileList.stream().mapToInt(FalconUserFiles::getFdsSize).sum();
            int freeCapacity = MAX_CAPACITY - usedCapacity;
            if(freeCapacity < fdsSize){
                throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_EXCEEDS_AVAILABLE_CAPACITY);
            }
        } else { //如果是最近打开 recent,只保留列表的前5条数据,其余数据都删除,需要过滤出前5条数据以外的其他数据
            // 获取根据 create_time 倒序排序的前 5 条数据
            List<FalconUserFiles> top5Files = userFileList.stream()
                    .sorted((f1, f2) -> f2.getCreateTime().compareTo(f1.getCreateTime()))
                    .limit(5)
                    .toList();
            // 获取前 5 条数据以外的其他数据
            List<FalconUserFiles> otherFiles = userFileList.stream()
                    .filter(file -> !top5Files.contains(file))
                    .toList();
            if(!CollectionUtils.isEmpty(otherFiles)){
                List<Long> delIds = new ArrayList<>();
                otherFiles.forEach(otherFile -> {
                    delIds.add(otherFile.getId());
                });
                if(!CollectionUtils.isEmpty(delIds)){
                    userFilesMapper.deleteBatchIds(delIds);
                }
            }
        }

        //构造我的文件实体类并插入数据表中
        FalconUserFiles userFiles = constructUserFiles(account.getUserId(),uploadDto.getScene(), uploadDto.getFdsPath(),
                uploadDto.getFdsImage(), uploadDto.getGcodePath(),uploadDto.getFileName(),fdsSize);
        userFiles.setUpdater(account.getUserId());
        userFiles.setUpdateTime(now);
        if(null != uploadDto.getFileId()){
            userFiles.setId(uploadDto.getFileId());
            userFilesMapper.updateById(userFiles);
            uploadVo.setFileId(userFiles.getId());
            return uploadVo;
        }
        userFiles.setCreator(account.getUserId());
        userFiles.setCreateTime(now);
        userFilesMapper.insert(userFiles);
        uploadVo.setFileId(userFiles.getId());
        return uploadVo;
    }

    /**
     * 我的空间-上传云文件-fds文件大小获取
     */
    private int getFdsSizeByFdsUrl(String fdsUrl){
        //从文件存储服务获取文件大小和md5值
        String objectName = StringUtils.isNotBlank(fdsUrl)? fdsUrl.replace(environmentUtils.getCustomDomainByEnv("common"),"") : "";
        if(StringUtils.isBlank(objectName)){
            return 0;
        }
        Map<String,String> metaData;
        if("test".equals(environmentUtils.getCurrentEnv())){
            metaData = ossUtils.getObjectMeta(environmentUtils.getBucketNameByEnv("common"),objectName);
        }else{
            metaData = s3Utils.getObjectDetails(environmentUtils.getBucketNameByEnv("common"),objectName);
        }
        String fileSize = metaData.get("fileSize");
        return Integer.parseInt(fileSize);
    }

    /**
     * 验证文件名是否符合规范
     * @param filename 文件命名
     */
    private boolean validateFilename(String filename) {
        Pattern pattern = Pattern.compile(FILENAME_PATTERN);
        Matcher matcher = pattern.matcher(filename);
        return matcher.matches();
    }

    /**
     * 构造我的文件实体类
     * @param userId
     * @param scene
     * @param fdsPath
     * @param fdsImage
     * @param gcodePath
     * @return
     */
    private FalconUserFiles constructUserFiles(String userId,
                                               String scene,
                                               String fdsPath,
                                               String fdsImage,
                                               String gcodePath,
                                               String fileName,
                                               Integer fdsSize){
        // 组装文件信息
        FalconUserFiles userFiles = new FalconUserFiles();
        userFiles.setUserId(userId);
        userFiles.setScene(scene);
        userFiles.setFdsSize(fdsSize);
        userFiles.setFileName(fileName);
        userFiles.setFdsPath(fdsPath);
        userFiles.setFdsImage(fdsImage);
        userFiles.setGcodePath(gcodePath);
        //从文件存储服务获取文件大小和md5值
        String objectName = StringUtils.isNotBlank(gcodePath) ? gcodePath.replace(environmentUtils.getCustomDomainByEnv("common"),"") : "";
        if(objectName.isEmpty()){
            return userFiles;
        }
        String currentEnv = environment.getProperty("spring.profiles.active");
        Map<String,String> metaData;
        if("test".equals(currentEnv)){
            metaData = ossUtils.getObjectMeta(environmentUtils.getBucketNameByEnv("common"),objectName);
        }else{
            metaData = s3Utils.getObjectDetails(environmentUtils.getBucketNameByEnv("common"),objectName);
        }
        userFiles.setGcodeSize(FileUtils.convertSize(metaData.get("fileSize")));
        userFiles.setGcodeMd5(metaData.get("fileMd5"));
        return userFiles;
    }

    /**
     * 我的空间-文件列表-获取指定用户下的指定类型的文件列表
     * @param pageNum
     * @param pageSize
     * @param scene
     * @return
     */
    @Override
    public PageAppResult<AppSpaceFileListVo> listSpaceFiles(int pageNum,
                                                            int pageSize,
                                                            String scene) {
        List<AppSpaceFileListVo> spaceFileListVos;
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }

        // 设置分页参数
        Page<FalconUserFiles> page = null;

        if("draft".equals(scene)){
            page = new Page<>(pageNum, pageSize);
        } else {
            page = new Page<>(pageNum, 5);
        }

        // 创建查询条件
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.eq("user_id", account.getUserId());
        userFilesQuery.eq("scene", scene);
        // 按创建时间降序排序
        userFilesQuery.orderByDesc("update_time");

        // 查询数据
        Page<FalconUserFiles> resultPage = userFilesMapper.selectPage(page, userFilesQuery);

        // 封装数据返回前端
        if(!CollectionUtils.isEmpty(resultPage.getRecords())){
            spaceFileListVos = new ArrayList<>();
            resultPage.getRecords().forEach(item->{
                AppSpaceFileListVo spaceFileListVo = new AppSpaceFileListVo();
                spaceFileListVo.setFileId(item.getId());
                spaceFileListVo.setFileName(item.getFileName());
                spaceFileListVo.setFdsImage(item.getFdsImage());
                spaceFileListVo.setFdsPath(item.getFdsPath());
                spaceFileListVo.setGcodePath(item.getGcodePath());
                spaceFileListVo.setCreateTime(TimeFormatter.transferLocalDateTimeToMillis(item.getCreateTime()));
                spaceFileListVos.add(spaceFileListVo);
            });
        } else {
            spaceFileListVos = new ArrayList<>();
        }

        // 创建新的 Page<BackProjectVo> 对象并返回前端
        PageAppResult<AppSpaceFileListVo> pageAppResult = new PageAppResult<>();
        pageAppResult.setPage((int) resultPage.getCurrent());
        pageAppResult.setSize((int) resultPage.getSize());
        pageAppResult.setTotal(resultPage.getTotal());
        pageAppResult.setList(spaceFileListVos);

        return pageAppResult;
    }

    /**
     * 我的空间-文件列表-批量删除文件
     * @param fileIds
     */
    @Override
    public void batchDelSpaceFiles(List<Long> fileIds) {
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.in("id", fileIds);
        List<FalconUserFiles> userFiles = userFilesMapper.selectList(userFilesQuery);
        if(!CollectionUtils.isEmpty(userFiles)){
            List<String> delFiles = new ArrayList<>();
            userFiles.forEach(item->{
                delFiles.add(item.getFdsPath());
                delFiles.add(item.getFdsImage());
                delFiles.add(item.getGcodePath());
            });
            String currentEnv = environment.getProperty("spring.profiles.active");
            //先清除OSS中的文件
            if("test".equals(currentEnv)){
                ossUtils.deleteObjectsByUrls(delFiles,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            }else{
                s3Utils.deleteObjectByUrls(delFiles,environmentUtils.getBucketNameByEnv("common"),environmentUtils.getCustomDomainByEnv("common"));
            }
            //再清除数据表中的数据
            userFilesMapper.deleteBatchIds(fileIds);
        }
    }

    /**
     * 我的空间-文件列表-重命名
     * @param fileRenameDto
     * @return
     */
    @Override
    public void renameSpaceFile(AppFileRenameDto fileRenameDto) {
        //获取当前用户信息
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        //获取当前时间
        LocalDateTime now = LocalDateTime.now();
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.eq("id", fileRenameDto.getFileId());
        FalconUserFiles userFiles = userFilesMapper.selectOne(userFilesQuery);
        if(Objects.isNull(userFiles)){
            throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_NOT_EXIST);
        }
        LambdaQueryWrapper<FalconUserFiles> dupWrapper = new LambdaQueryWrapper<>();
        dupWrapper.eq(FalconUserFiles::getUserId, account.getUserId())
                .eq(FalconUserFiles::getFileName, fileRenameDto.getNewName())
                .ne(FalconUserFiles::getId, userFiles.getId());

        if (userFilesMapper.selectCount(dupWrapper) > 0) {
            throw new ApiException(ResultEnum.COMMON_USER_SPACE_FILE_FILENAME_REPEATED);
        }

        userFiles.setFileName(fileRenameDto.getNewName());
        userFiles.setUpdater(account.getUserId());
        userFiles.setUpdateTime(now);
        userFilesMapper.updateById(userFiles);
    }

    /**
     * 云文件容量获取
     * @return 总容量、剩余容量、已用容量
     */
    @Override
    public AppSpaceUserCapacityVo userCapacity() {
        AppSpaceUserCapacityVo userCapacityVo = new AppSpaceUserCapacityVo();
        userCapacityVo.setTotalCapacity(********);
        //获取当前用户信息
        FalconAccount account = sessionManage.getUserInfo();
        if(Objects.isNull(account)){
            throw new ApiException(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
        }
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.eq("user_id",account.getUserId());
        userFilesQuery.eq("scene","draft");
        List<FalconUserFiles> userFiles = userFilesMapper.selectList(userFilesQuery);
        if(CollectionUtils.isEmpty(userFiles)){
            userCapacityVo.setFreeCapacity(********);
            userCapacityVo.setUsedCapacity(0);
            return userCapacityVo;
        }
        int usedCapacity = userFiles.stream()
                .mapToInt(FalconUserFiles::getFdsSize)  // 直接提取 int 类型的 fdsSize
                .sum();// 直接求和，结果为 int 类型
        userCapacityVo.setFreeCapacity(******** - usedCapacity);
        userCapacityVo.setUsedCapacity(usedCapacity);
        return userCapacityVo;
    }

    /**
     * 画布-查询云文件保存状态
     * @return true 已保存 false 未保存
     */
    @Override
    public Boolean checkIsSafe(Long fileId) {
        boolean saveFlag = false;
        //查询当前文件id在数据表中是否存在记录
        QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
        userFilesQuery.eq("id", fileId);
        userFilesQuery.eq("scene","draft");
        FalconUserFiles userFiles = userFilesMapper.selectOne(userFilesQuery);
        if(Objects.nonNull(userFiles)){
            saveFlag = true;
        }
        return saveFlag;
    }

    @Override
    public BaseResponse<Object> handleFileConflict(AppSpaceFileConflictHandleDto conflictHandleDto) {
        try {
            FalconAccount account = sessionManage.getUserInfo();
            if (Objects.isNull(account)) {
                return BaseResponse.error(ResultEnum.ACCOUNT_NOT_LOGGED_IN);
            }

            String action = conflictHandleDto.getAction();
            if (StringUtils.isBlank(action)) {
                return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
            }

            // 对于replace和add操作，需要检查文件大小限制
            if ("replace".equals(action.toLowerCase()) || "add".equals(action.toLowerCase())) {
                BaseResponse<Object> sizeCheckResult = checkFileSizeLimit(conflictHandleDto, account);
                if (sizeCheckResult.getCode() != ResultEnum.SUCCESS.getCode()) {
                    return sizeCheckResult;
                }
            }

            switch (action.toLowerCase()) {
                case "replace":
                    return handleReplaceFile(conflictHandleDto, account);
                case "add":
                    return handleAddFileWithNumber(conflictHandleDto, account);
                case "cancel":
                    return handleCancelUpload(conflictHandleDto, account);
                default:
                    return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
            }
        } catch (Exception e) {
            log.error("处理文件冲突异常: action={}, fileName={}, 错误: {}",
                     conflictHandleDto.getAction(), conflictHandleDto.getFileName(), e.getMessage(), e);
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 检查文件大小限制（与uploadSpaceFiles保持一致）
     */
    private BaseResponse<Object> checkFileSizeLimit(AppSpaceFileConflictHandleDto dto, FalconAccount account) {
        try {
            String scene = dto.getScene();

            // 只对draft场景进行大小限制检查
            if (!"draft".equals(scene)) {
                return BaseResponse.success(null);
            }

            // 文件名长度检查
            final int MAX_NAME_LEN = 50;
            if (dto.getFileName().length() > MAX_NAME_LEN) {
                return BaseResponse.error(ResultEnum.COMMON_USER_SPACE_FILE_FILENAME_INVALID);
            }

            // 获取文件大小
            int fdsSize = dto.getFdsSize();
            if (fdsSize == 0) {
                fdsSize = getFdsSizeByFdsUrl(dto.getFdsPath());
            }

            // 查询当前用户下的draft文件列表
            QueryWrapper<FalconUserFiles> userFilesQuery = new QueryWrapper<>();
            userFilesQuery.eq("scene", scene);
            userFilesQuery.eq("user_id", account.getUserId());
            userFilesQuery.orderByDesc("create_time");
            List<FalconUserFiles> userFileList = userFilesMapper.selectList(userFilesQuery);

            // 计算已使用容量和可用容量
            final int MAX_CAPACITY = 50 * 1024 * 1024; // 50MB
            int usedCapacity = userFileList.stream().mapToInt(FalconUserFiles::getFdsSize).sum();

            // 如果是替换操作，需要排除被替换文件的大小
            if ("replace".equals(dto.getAction().toLowerCase())) {
                // 查找要被替换的文件
                FalconUserFiles existingFile = userFileList.stream()
                        .filter(f -> f.getFileName().equals(dto.getFileName()))
                        .findFirst()
                        .orElse(null);

                if (existingFile != null) {
                    usedCapacity -= existingFile.getFdsSize(); // 减去被替换文件的大小
                }
            }

            int freeCapacity = MAX_CAPACITY - usedCapacity;
            if (freeCapacity < fdsSize) {
                return BaseResponse.error(ResultEnum.COMMON_USER_SPACE_FILE_EXCEEDS_AVAILABLE_CAPACITY);
            }

            return BaseResponse.success();
        } catch (Exception e) {
            log.error("检查文件大小限制异常: fileName={}, 错误: {}", dto.getFileName(), e.getMessage());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 处理替换文件
     */
    @Transactional(rollbackFor = Exception.class)
    protected BaseResponse<Object> handleReplaceFile(AppSpaceFileConflictHandleDto dto, FalconAccount account) {
        try {
            String fileName = dto.getFileName();
            String scene = dto.getScene();

            // 1. 查找现有文件
            QueryWrapper<FalconUserFiles> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", account.getId())
                       .eq("file_name", fileName)
                       .eq("scene", scene);
            FalconUserFiles existingFile = userFilesMapper.selectOne(queryWrapper);

            if (existingFile == null) {
                return BaseResponse.error(ResultEnum.PARAMETER_ERROR);
            }

            // 2. 保存旧文件路径（用于回滚）
            List<String> oldFilePaths = new ArrayList<>();
            if (StringUtils.isNotBlank(existingFile.getFdsPath())) {
                oldFilePaths.add(existingFile.getFdsPath());
            }
            if (StringUtils.isNotBlank(existingFile.getFdsImage())) {
                oldFilePaths.add(existingFile.getFdsImage());
            }
            if (StringUtils.isNotBlank(existingFile.getGcodePath())) {
                oldFilePaths.add(existingFile.getGcodePath());
            }

            // 3. 删除原有数据库记录
            int deleteResult = userFilesMapper.deleteById(existingFile.getId());
            if (deleteResult <= 0) {
                return BaseResponse.error(ResultEnum.SERVER_ERROR);
            }

            // 4. 新增新的数据库记录
            FalconUserFiles newFile = new FalconUserFiles();
            newFile.setUserId(String.valueOf(account.getId()));
            newFile.setFileName(fileName);
            newFile.setScene(scene);
            newFile.setFdsSize(dto.getFdsSize());
            newFile.setFdsPath(dto.getFdsPath());
            newFile.setFdsImage(dto.getFdsImage());
            newFile.setGcodePath(dto.getGcodePath());
            newFile.setCreateTime(LocalDateTime.now());
            newFile.setUpdateTime(LocalDateTime.now());

            int insertResult = userFilesMapper.insert(newFile);
            if (insertResult <= 0) {
                return BaseResponse.error(ResultEnum.SERVER_ERROR);
            }

            // 5. 删除OSS/S3上的旧文件（在事务提交后异步执行）
            if (!oldFilePaths.isEmpty()) {
                try {
                    batchDeleteFilesFromStorage(oldFilePaths);
                } catch (Exception e) {
                    // 删除旧文件失败，记录日志但不影响替换操作
                    log.warn("替换文件成功，但删除旧文件失败: {}, 错误: {}", oldFilePaths, e.getMessage());
                }
            }

            return BaseResponse.success();

        } catch (Exception e) {
            log.error("替换文件失败: fileName={}, userId={}, 错误: {}", dto.getFileName(), account.getId(), e.getMessage());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 处理新增文件（带序号）
     */
    @Transactional(rollbackFor = Exception.class)
    protected BaseResponse<Object> handleAddFileWithNumber(AppSpaceFileConflictHandleDto dto, FalconAccount account) {
        try {
            String originalFileName = dto.getFileName();
            String scene = dto.getScene();

            // 1. 生成唯一文件名
            String newFileName = generateUniqueFileName(originalFileName, String.valueOf(account.getId()), scene);

            // 2. 创建新文件记录
            FalconUserFiles userFiles = new FalconUserFiles();
            userFiles.setUserId(String.valueOf(account.getId()));
            userFiles.setFileName(newFileName);
            userFiles.setScene(scene);
            userFiles.setFdsSize(dto.getFdsSize());
            userFiles.setFdsPath(dto.getFdsPath());
            userFiles.setFdsImage(dto.getFdsImage());
            userFiles.setGcodePath(dto.getGcodePath());
            userFiles.setCreateTime(LocalDateTime.now());
            userFiles.setUpdateTime(LocalDateTime.now());

            int insertResult = userFilesMapper.insert(userFiles);
            if (insertResult <= 0) {
                return BaseResponse.error(ResultEnum.SERVER_ERROR);
            }

            return BaseResponse.success();

        } catch (Exception e) {
            log.error("新增文件失败: originalFileName={}, userId={}, 错误: {}",
                     dto.getFileName(), account.getId(), e.getMessage());

            // 如果数据库操作失败，需要删除已上传的文件
            List<String> filesToDelete = new ArrayList<>();
            if (StringUtils.isNotBlank(dto.getFdsPath())) {
                filesToDelete.add(dto.getFdsPath());
            }
            if (StringUtils.isNotBlank(dto.getFdsImage())) {
                filesToDelete.add(dto.getFdsImage());
            }
            if (StringUtils.isNotBlank(dto.getGcodePath())) {
                filesToDelete.add(dto.getGcodePath());
            }

            try {
                batchDeleteFilesFromStorage(filesToDelete);
                log.info("新增文件失败，已清理上传的文件: {}", filesToDelete);
            } catch (Exception deleteException) {
                log.error("新增文件失败，清理上传文件也失败: {}, 错误: {}", filesToDelete, deleteException.getMessage());
            }

            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 处理取消上传
     */
    private BaseResponse<Object> handleCancelUpload(AppSpaceFileConflictHandleDto dto, FalconAccount account) {
        try {
            // 批量删除已上传到OSS/S3的文件
            List<String> filesToDelete = new ArrayList<>();
            if (StringUtils.isNotBlank(dto.getFdsPath())) {
                filesToDelete.add(dto.getFdsPath());
            }
            if (StringUtils.isNotBlank(dto.getFdsImage())) {
                filesToDelete.add(dto.getFdsImage());
            }
            if (StringUtils.isNotBlank(dto.getGcodePath())) {
                filesToDelete.add(dto.getGcodePath());
            }

            if (!filesToDelete.isEmpty()) {
                batchDeleteFilesFromStorage(filesToDelete);
                log.info("取消上传，成功删除临时文件: {}", filesToDelete);
            }

            return BaseResponse.success();

        } catch (Exception e) {
            log.error("取消上传时删除临时文件失败: fileName={}, 错误: {}", dto.getFileName(), e.getMessage());
            return BaseResponse.error(ResultEnum.SERVER_ERROR);
        }
    }

    /**
     * 生成唯一文件名（带序号）
     */
    private String generateUniqueFileName(String originalFileName, String userId, String scene) {
        // 正则表达式匹配文件名和已有的序号
        Pattern pattern = Pattern.compile("^(.+?)(?:\\((\\d+)\\))?$");
        Matcher matcher = pattern.matcher(originalFileName);

        String baseName = originalFileName;
        int currentNumber = 0;

        if (matcher.matches()) {
            baseName = matcher.group(1);
            String numberStr = matcher.group(2);
            if (numberStr != null) {
                currentNumber = Integer.parseInt(numberStr);
            }
        }

        // 查询数据库中同名文件的最大序号
        QueryWrapper<FalconUserFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("scene", scene)
                   .like("file_name", baseName);

        List<FalconUserFiles> existingFiles = userFilesMapper.selectList(queryWrapper);

        int maxNumber = 0;
        Pattern numberPattern = Pattern.compile("^" + Pattern.quote(baseName) + "(?:\\((\\d+)\\))?$");

        for (FalconUserFiles file : existingFiles) {
            Matcher numberMatcher = numberPattern.matcher(file.getFileName());
            if (numberMatcher.matches()) {
                String numberStr = numberMatcher.group(1);
                if (numberStr != null) {
                    maxNumber = Math.max(maxNumber, Integer.parseInt(numberStr));
                } else if (file.getFileName().equals(baseName)) {
                    maxNumber = Math.max(maxNumber, 0);
                }
            }
        }

        // 生成新的文件名
        return baseName + "(" + (maxNumber + 1) + ")";
    }

    /**
     * 批量从存储中删除文件
     */
    private void batchDeleteFilesFromStorage(List<String> filePaths) {
        if (filePaths == null || filePaths.isEmpty()) {
            return;
        }

        // 过滤掉空的文件路径
        List<String> validFilePaths = filePaths.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (validFilePaths.isEmpty()) {
            return;
        }

        try {
            String currentEnv = environment.getProperty("spring.profiles.active");

            if("test".equals(currentEnv)){
                ossUtils.deleteObjectsByUrls(validFilePaths, environmentUtils.getBucketNameByEnv("common"), environmentUtils.getCustomDomainByEnv("common"));
            } else {
                s3Utils.deleteObjectByUrls(validFilePaths, environmentUtils.getBucketNameByEnv("common"), environmentUtils.getCustomDomainByEnv("common"));
            }

            log.info("批量删除文件成功，文件数量: {}", validFilePaths.size());
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主流程
            log.error("批量删除文件失败，文件路径: {}, 错误: {}", validFilePaths, e.getMessage());
        }
    }

    /**
     * 从存储中删除单个文件（保留此方法用于其他地方的兼容性）
     */
    private void deleteFileFromStorage(String filePath) {
        batchDeleteFilesFromStorage(Collections.singletonList(filePath));
    }
}
