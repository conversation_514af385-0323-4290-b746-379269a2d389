package com.creality.lcd.falcon.interceptor;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.creality.lcd.falcon.enums.ResultEnum;
import com.creality.lcd.falcon.exceptions.ApiException;
import com.creality.lcd.falcon.util.IpUtils;
import com.creality.lcd.falcon.util.SessionManage;
import com.creality.lcd.falcon.util.StatisticUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


@Component
public class SessionInterceptor implements HandlerInterceptor {


    private static final Logger log = LogManager.getLogger(SessionInterceptor.class);

    @Autowired
    private SessionManage sessionManage;

    @Autowired
    private StatisticUtils statisticUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String sessionId = request.getHeader("sessionId");
        String ip = IpUtils.getClientIp(request);
        log.info("App服务--sessionId:{},ip:{}", sessionId, ip);
        // 检查请求的 URI
        String requestURI = request.getRequestURI();
        log.info("App服务--请求URI:{}",requestURI);
        if(!"/app-api/test".equals(requestURI)){
            //统计访问量,以日进行区分
            statisticUtils.recordVisitAndUniqueIpCount("APP","VISIT",null);
            //统计日活量,以请求IP去重进行区分(不区分是否登录)
            statisticUtils.recordVisitAndUniqueIpCount("APP","UNIQUE_IP",ip);
        }

        //放行特定接口,使用Set提升效率
        Set<String> ALLOWED_PATHS = new HashSet<>(Arrays.asList(
                "/app-api/test",
                "/app-api/error",
                "/app-api/home/<USER>",
                "/app-api/home/<USER>",
                "/app-api/home/<USER>",
                "/app-api/home/<USER>",
                "/app-api/home/<USER>",
                "/app-api/home/<USER>",
                "/app-api/upgrade/check",
                "/app-api/upgrade/latest",
                "/app-api/canvas/query-image-category",
                "/app-api/canvas/images-by-category",
                "/app-api/canvas/query-material-info"
        ));

        if (ALLOWED_PATHS.contains(requestURI)) {
            return true;
        }

        // 检查 sessionId 是否为空
        if (StringUtils.isBlank(sessionId)) {
            log.warn("App服务--请求sessionId为空");
            throw new ApiException(ResultEnum.PARAMETER_ERROR);
        }

        // 检查 session 是否过期
        if (!sessionManage.checkSessionExpired(sessionId)) {
            log.warn("App服务--请求session已过期");
            throw new ApiException(ResultEnum.ACCOUNT_SESSION_EXPIRE_ERROR);
        }
        // 放行请求
        return true;
    }
}
